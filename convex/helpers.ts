import { mutation, query } from '@/convex/_generated/server';
import { BotAuthenticationGateway } from '@/src/server/infrastructure/gateways/AuthenticationGateway/BotAuthenticationGateway';
import { ConvexAuthenticationGateway } from '@/src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway';
import { customMutation, customQuery } from 'convex-helpers/server/customFunctions';
import {v} from "convex/values";

export const protectedQuery = customQuery(query, {
  args: {},
  input: async (ctx, args) => {
    const authenticationGateway = new ConvexAuthenticationGateway(ctx);
    const user = await authenticationGateway.getAuthenticatedUser();

    return { ctx: { ...ctx, user }, args };
  },
});

export const protectedMutation = customMutation(mutation, {
  args: {},
  input: async (ctx, args) => {
    const authenticationGateway = new ConvexAuthenticationGateway(ctx);
    const user = await authenticationGateway.getAuthenticatedUser();

    return { ctx: { ...ctx, user }, args };
  },
});

export const botQuery = customQuery(query, {
  args: {
    apiKey: v.string(),
  },
  input: async (ctx, {apiKey}) => {
    const authenticationGateway = new BotAuthenticationGateway(ctx, apiKey);
    const user = await authenticationGateway.getAuthenticatedUser();

    return { ctx: { ...ctx, user }, args: {} };
  },
});

export const botMutation = customMutation(mutation, {
  args: {
    apiKey: v.string(),
  },
  input: async (ctx, {apiKey}) => {
    const authenticationGateway = new BotAuthenticationGateway(ctx, apiKey);
    const user = await authenticationGateway.getAuthenticatedUser();

    return { ctx: { ...ctx, user }, args: {} };
  },
});
