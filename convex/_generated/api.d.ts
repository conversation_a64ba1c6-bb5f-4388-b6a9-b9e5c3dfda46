/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type * as auth from "../auth.js";
import type * as counters_feedbackCounter from "../counters/feedbackCounter.js";
import type * as helpers from "../helpers.js";
import type * as http from "../http.js";
import type * as mutations_bot_closeCodeReview from "../mutations/bot/closeCodeReview.js";
import type * as mutations_bot_createCodeReview from "../mutations/bot/createCodeReview.js";
import type * as mutations_bot_postFeedback from "../mutations/bot/postFeedback.js";
import type * as mutations_bot_publishComment from "../mutations/bot/publishComment.js";
import type * as mutations_bot_startCodeReview from "../mutations/bot/startCodeReview.js";
import type * as mutations_user_acceptTeamInvitation from "../mutations/user/acceptTeamInvitation.js";
import type * as mutations_user_closeCodeReview from "../mutations/user/closeCodeReview.js";
import type * as mutations_user_createCodeReview from "../mutations/user/createCodeReview.js";
import type * as mutations_user_createTeam from "../mutations/user/createTeam.js";
import type * as mutations_user_deleteCodeReview from "../mutations/user/deleteCodeReview.js";
import type * as mutations_user_deleteComment from "../mutations/user/deleteComment.js";
import type * as mutations_user_deleteFeedback from "../mutations/user/deleteFeedback.js";
import type * as mutations_user_editCodeReview from "../mutations/user/editCodeReview.js";
import type * as mutations_user_getCodeReviewFeedbacksMarkdown from "../mutations/user/getCodeReviewFeedbacksMarkdown.js";
import type * as mutations_user_participateToCodeReview from "../mutations/user/participateToCodeReview.js";
import type * as mutations_user_postFeedback from "../mutations/user/postFeedback.js";
import type * as mutations_user_publishComment from "../mutations/user/publishComment.js";
import type * as mutations_user_rejectTeamInvitation from "../mutations/user/rejectTeamInvitation.js";
import type * as mutations_user_sendTeamInvitation from "../mutations/user/sendTeamInvitation.js";
import type * as mutations_user_startCodeReview from "../mutations/user/startCodeReview.js";
import type * as queries_bot_getCodeSnippetByCodeSnippetId from "../queries/bot/getCodeSnippetByCodeSnippetId.js";
import type * as queries_bot_getCodeSnippetsByCodeReviewId from "../queries/bot/getCodeSnippetsByCodeReviewId.js";
import type * as queries_bot_getCommentsByFeedbackId from "../queries/bot/getCommentsByFeedbackId.js";
import type * as queries_bot_getFeedbacksByCodeSnippetId from "../queries/bot/getFeedbacksByCodeSnippetId.js";
import type * as queries_codeReviewEdit from "../queries/codeReviewEdit.js";
import type * as queries_codeReviewEditor from "../queries/codeReviewEditor.js";
import type * as queries_codeReviewFeedbacksMarkdown from "../queries/codeReviewFeedbacksMarkdown.js";
import type * as queries_codeReviewParticipationHistory from "../queries/codeReviewParticipationHistory.js";
import type * as queries_commentList from "../queries/commentList.js";
import type * as queries_feedbackDetails from "../queries/feedbackDetails.js";
import type * as queries_feedbackList from "../queries/feedbackList.js";
import type * as queries_firstSnippetIdByCodeReviewId from "../queries/firstSnippetIdByCodeReviewId.js";
import type * as queries_getCurrentUserEmail from "../queries/getCurrentUserEmail.js";
import type * as queries_getCurrentUserId from "../queries/getCurrentUserId.js";
import type * as queries_getUserProfileByUserId from "../queries/getUserProfileByUserId.js";
import type * as queries_lobby_getPrivateCodeReviews from "../queries/lobby/getPrivateCodeReviews.js";
import type * as queries_lobby_getPublicCodeReviews from "../queries/lobby/getPublicCodeReviews.js";
import type * as queries_lobby_getTeamByTeamId from "../queries/lobby/getTeamByTeamId.js";
import type * as queries_lobby_getUserCodeReviewsViewModel from "../queries/lobby/getUserCodeReviewsViewModel.js";
import type * as queries_lobby_getUserTeamInvitations from "../queries/lobby/getUserTeamInvitations.js";
import type * as queries_lobby_getUserTeams from "../queries/lobby/getUserTeams.js";
import type * as queries_myCommentList from "../queries/myCommentList.js";
import type * as queries_taskList from "../queries/taskList.js";
import type * as queries_userFeedbacks from "../queries/userFeedbacks.js";
import type * as queries_userProfile from "../queries/userProfile.js";

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  "counters/feedbackCounter": typeof counters_feedbackCounter;
  helpers: typeof helpers;
  http: typeof http;
  "mutations/bot/closeCodeReview": typeof mutations_bot_closeCodeReview;
  "mutations/bot/createCodeReview": typeof mutations_bot_createCodeReview;
  "mutations/bot/postFeedback": typeof mutations_bot_postFeedback;
  "mutations/bot/publishComment": typeof mutations_bot_publishComment;
  "mutations/bot/startCodeReview": typeof mutations_bot_startCodeReview;
  "mutations/user/acceptTeamInvitation": typeof mutations_user_acceptTeamInvitation;
  "mutations/user/closeCodeReview": typeof mutations_user_closeCodeReview;
  "mutations/user/createCodeReview": typeof mutations_user_createCodeReview;
  "mutations/user/createTeam": typeof mutations_user_createTeam;
  "mutations/user/deleteCodeReview": typeof mutations_user_deleteCodeReview;
  "mutations/user/deleteComment": typeof mutations_user_deleteComment;
  "mutations/user/deleteFeedback": typeof mutations_user_deleteFeedback;
  "mutations/user/editCodeReview": typeof mutations_user_editCodeReview;
  "mutations/user/getCodeReviewFeedbacksMarkdown": typeof mutations_user_getCodeReviewFeedbacksMarkdown;
  "mutations/user/participateToCodeReview": typeof mutations_user_participateToCodeReview;
  "mutations/user/postFeedback": typeof mutations_user_postFeedback;
  "mutations/user/publishComment": typeof mutations_user_publishComment;
  "mutations/user/rejectTeamInvitation": typeof mutations_user_rejectTeamInvitation;
  "mutations/user/sendTeamInvitation": typeof mutations_user_sendTeamInvitation;
  "mutations/user/startCodeReview": typeof mutations_user_startCodeReview;
  "queries/bot/getCodeSnippetByCodeSnippetId": typeof queries_bot_getCodeSnippetByCodeSnippetId;
  "queries/bot/getCodeSnippetsByCodeReviewId": typeof queries_bot_getCodeSnippetsByCodeReviewId;
  "queries/bot/getCommentsByFeedbackId": typeof queries_bot_getCommentsByFeedbackId;
  "queries/bot/getFeedbacksByCodeSnippetId": typeof queries_bot_getFeedbacksByCodeSnippetId;
  "queries/codeReviewEdit": typeof queries_codeReviewEdit;
  "queries/codeReviewEditor": typeof queries_codeReviewEditor;
  "queries/codeReviewFeedbacksMarkdown": typeof queries_codeReviewFeedbacksMarkdown;
  "queries/codeReviewParticipationHistory": typeof queries_codeReviewParticipationHistory;
  "queries/commentList": typeof queries_commentList;
  "queries/feedbackDetails": typeof queries_feedbackDetails;
  "queries/feedbackList": typeof queries_feedbackList;
  "queries/firstSnippetIdByCodeReviewId": typeof queries_firstSnippetIdByCodeReviewId;
  "queries/getCurrentUserEmail": typeof queries_getCurrentUserEmail;
  "queries/getCurrentUserId": typeof queries_getCurrentUserId;
  "queries/getUserProfileByUserId": typeof queries_getUserProfileByUserId;
  "queries/lobby/getPrivateCodeReviews": typeof queries_lobby_getPrivateCodeReviews;
  "queries/lobby/getPublicCodeReviews": typeof queries_lobby_getPublicCodeReviews;
  "queries/lobby/getTeamByTeamId": typeof queries_lobby_getTeamByTeamId;
  "queries/lobby/getUserCodeReviewsViewModel": typeof queries_lobby_getUserCodeReviewsViewModel;
  "queries/lobby/getUserTeamInvitations": typeof queries_lobby_getUserTeamInvitations;
  "queries/lobby/getUserTeams": typeof queries_lobby_getUserTeams;
  "queries/myCommentList": typeof queries_myCommentList;
  "queries/taskList": typeof queries_taskList;
  "queries/userFeedbacks": typeof queries_userFeedbacks;
  "queries/userProfile": typeof queries_userProfile;
}>;
declare const fullApiWithMounts: typeof fullApi;

export declare const api: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "internal">
>;

export declare const components: {
  shardedCounter: {
    public: {
      add: FunctionReference<
        "mutation",
        "internal",
        { count: number; name: string; shard?: number; shards?: number },
        number
      >;
      count: FunctionReference<"query", "internal", { name: string }, number>;
      estimateCount: FunctionReference<
        "query",
        "internal",
        { name: string; readFromShards?: number; shards?: number },
        any
      >;
      rebalance: FunctionReference<
        "mutation",
        "internal",
        { name: string; shards?: number },
        any
      >;
      reset: FunctionReference<"mutation", "internal", { name: string }, any>;
    };
  };
};
