import {defineSchema, defineTable} from "convex/server";
import {v} from "convex/values";
import {authTables} from "@convex-dev/auth/server";

const schema = defineSchema({
  ...authTables,

  appUsers: defineTable({
    appUserId: v.string(),
    convexUserId: v.string(),
    name: v.string(),
    createdAt: v.number(),
    avatar: v.string(),
    active: v.optional(v.boolean()),
    email: v.optional(v.string()),
    status: v.optional(v.string()),
  })
    .index('by_appUserId', ['appUserId'])
    .index('by_convexUserId', ['convexUserId']),

  games: defineTable({
    name: v.string()
  }),

  matches: defineTable({
    gameId: v.id("games"),
    players: v.array(v.string()),
    status: v.string(),
    createdAt: v.number(),
    winner: v.optional(v.string()),
  }).index("by_game_status", ["gameId", "status"]),

  matchEvents: defineTable({
    gameId: v.id("games"),
    matchId: v.id("matches"),
    type: v.string(),
    payload: v.any(),
    occurredAt: v.number(),
  }).index('by_matchId_and_type', ['matchId', 'type'])
    .index('by_matchId', ["matchId"]),

  matchmakingEvents: defineTable({
    gameId: v.id("games"),
    aggregateId: v.id("matchmakingQueue"),
    type: v.string(),
    payload: v.any(),
    occurredAt: v.number(),
  }).index("by_gameId", ["gameId"])
    .index('by_gameId_and_type', ['gameId', 'type']),

  matchmakingQueue: defineTable({
    gameId: v.id("games"),
    playerId: v.string(),
    queuedAt: v.number(),
    deckId: v.id("decks"),
  }).index("by_gameId", ["gameId"])
    .index("by_playerId_and_gameId", ["playerId", "gameId"]),

  decks: defineTable({
    gameId: v.id("games"),
    playerId: v.string(),
    name: v.string(),
    cards: v.array(v.object({cardId: v.string(), quantity: v.number()})),
  }).index("by_playerId_and_gameId", ["playerId", "gameId"]),

  catalogCards: defineTable({
    gameId: v.id("games"),
    name: v.string(),
    image: v.string(),
    language: v.string(),
    minDeckQuantity: v.number(),
    maxDeckQuantity: v.number(),
    data: v.any(),
  }).index("by_gameId", ["gameId"]),

  gameSettings: defineTable({
    gameId: v.id("games"),
    maxCardsInDeck: v.number(),
  }).index("by_gameId", ["gameId"]),

  deckBuilderSettings: defineTable({
    gameId: v.id("games"),
  }).index("by_gameId", ["gameId"]),

  availableFilters: defineTable({
    gameId: v.id("games"),
    text: v.string(),
    name: v.string(),
    dataProperty: v.string(),
    dataType: v.string(),
    value: v.any(),
    order: v.number(),
  }).index("by_gameId", ["gameId"]),
});

export default schema;
