import { protectedMutation } from '@/convex/helpers';
import { DeleteCodeReviewCommand } from '@/src/server/application/usecases/DeleteCodeReview/DeleteCodeReviewCommand';
import { DeleteCodeReviewCommandHandler } from '@/src/server/application/usecases/DeleteCodeReview/DeleteCodeReviewCommandHandler';
import { ConvexCodeReviewRepository } from '@/src/server/infrastructure/repositories/CodeReviewRepository/ConvexCodeReviewRepository';
import { v } from 'convex/values';

export const endpoint = protectedMutation({
  args: {
    codeReviewId: v.string(),
  },
  handler: async (ctx, { codeReviewId }) => {
    const codeReviewRepository = new ConvexCodeReviewRepository(ctx.db);
    const commandHandler = new DeleteCodeReviewCommandHandler(codeReviewRepository);

    const command = new DeleteCodeReviewCommand(codeReviewId, ctx.user.id);

    await commandHandler.handle(command);
  },
});
