import { protectedMutation } from '@/convex/helpers';
import { DeleteCommentCommand } from '@/src/server/application/usecases/DeleteComment/DeleteCommentCommand';
import { DeleteCommentCommandHandler } from '@/src/server/application/usecases/DeleteComment/DeleteCommentCommandHandler';
import { ConvexCommentRepository } from '@/src/server/infrastructure/repositories/CommentRepository/ConvexCommentRepository';
import { v } from 'convex/values';

export const endpoint = protectedMutation({
  args: {
    commentId: v.string(),
  },
  handler: async (ctx, { commentId }) => {
    const commentRepository = new ConvexCommentRepository(ctx.db);
    const commandHandler = new DeleteCommentCommandHandler(commentRepository);

    const command = new DeleteCommentCommand(commentId, ctx.user.id);

    const result = await commandHandler.handle(command);

    if (result.isFail()) {
      throw result.getError();
    }
  },
});
