import { protectedMutation } from '@/convex/helpers';
import { SendTeamInvitationCommand } from '@/src/server/application/usecases/SendTeamInvitation/SendTeamInvitationCommand';
import { SendTeamInvitationCommandHandler } from '@/src/server/application/usecases/SendTeamInvitation/SendTeamInvitationCommandHandler';
import { UuidIdentityProvider } from '@/src/server/infrastructure/providers/IdentityProvider/UuidIdentityProvider';
import { ConvexTeamInvitationRepository } from '@/src/server/infrastructure/repositories/TeamInvitationRepository/ConvexTeamInvitationRepository';
import { ConvexTeamMemberRepository } from '@/src/server/infrastructure/repositories/TeamMemberRepository/ConvexTeamMemberRepository';
import { ConvexTeamRepository } from '@/src/server/infrastructure/repositories/TeamRepository/ConvexTeamRepository';
import { TeamInvitationService } from '@/src/server/infrastructure/services/TeamInvitationService/TeamInvitationService';
import { v } from 'convex/values';

export const endpoint = protectedMutation({
  args: {
    teamId: v.string(),
    email: v.string(),
  },
  handler: async (ctx, { teamId, email }) => {
    const teamRepository = new ConvexTeamRepository(ctx.db);
    const teamInvitationRepository = new ConvexTeamInvitationRepository(ctx.db);
    const teamMemberRepository = new ConvexTeamMemberRepository(ctx.db);
    const identityProvider = new UuidIdentityProvider(crypto);

    const teamInvitationService = new TeamInvitationService(
      teamRepository,
      teamInvitationRepository,
      teamMemberRepository
    );

    const sendTeamInvitationCommandHandler = new SendTeamInvitationCommandHandler(
      teamInvitationService,
      teamInvitationRepository,
      identityProvider
    );

    const command = new SendTeamInvitationCommand(ctx.user.id, teamId, email);
    const result = await sendTeamInvitationCommandHandler.handle(command);

    if (result.isFail()) {
      throw result.getError();
    }

    return result.getValue();
  },
});
