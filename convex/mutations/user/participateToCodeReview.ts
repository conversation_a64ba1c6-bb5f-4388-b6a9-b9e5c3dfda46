import { protectedMutation } from '@/convex/helpers';
import { ParticipateToCodeReviewCommand } from '@/src/server/application/usecases/ParticipateToCodeReview/ParticipateToCodeReviewCommand';
import { ParticipateToCodeReviewCommandHandler } from '@/src/server/application/usecases/ParticipateToCodeReview/ParticipateToCodeReviewCommandHandler';
import { UuidIdentityProvider } from '@/src/server/infrastructure/providers/IdentityProvider/UuidIdentityProvider';
import { ConvexCodeReviewParticipationRepository } from '@/src/server/infrastructure/repositories/CodeReviewParticipationRepository/ConvexCodeReviewParticipationRepository';
import { v } from 'convex/values';

export const endpoint = protectedMutation({
  args: {
    codeReviewId: v.string(),
  },
  handler: async (ctx, { codeReviewId }) => {
    const codeReviewRepository = new ConvexCodeReviewParticipationRepository(ctx.db);
    const identityProvider = new UuidIdentityProvider(crypto);
    const commandHandler = new ParticipateToCodeReviewCommandHandler(codeReviewRepository, identityProvider);

    const command = new ParticipateToCodeReviewCommand(codeReviewId, ctx.user.id);

    await commandHandler.handle(command);
  },
});
