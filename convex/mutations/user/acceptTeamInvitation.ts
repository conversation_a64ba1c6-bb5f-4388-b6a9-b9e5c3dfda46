import { protectedMutation } from '@/convex/helpers';
import { AcceptTeamInvitationCommand } from '@/src/server/application/usecases/AcceptTeamInvitation/AcceptTeamInvitationCommand';
import { AcceptTeamInvitationCommandHandler } from '@/src/server/application/usecases/AcceptTeamInvitation/AcceptTeamInvitationCommandHandler';
import { UuidIdentityProvider } from '@/src/server/infrastructure/providers/IdentityProvider/UuidIdentityProvider';
import { ConvexTeamInvitationRepository } from '@/src/server/infrastructure/repositories/TeamInvitationRepository/ConvexTeamInvitationRepository';
import { ConvexTeamMemberRepository } from '@/src/server/infrastructure/repositories/TeamMemberRepository/ConvexTeamMemberRepository';
import { ConvexTeamRepository } from '@/src/server/infrastructure/repositories/TeamRepository/ConvexTeamRepository';
import { TeamInvitationService } from '@/src/server/infrastructure/services/TeamInvitationService/TeamInvitationService';
import { v } from 'convex/values';

export const endpoint = protectedMutation({
  args: {
    invitationId: v.string(),
    userEmail: v.string(),
  },
  handler: async (ctx, { invitationId, userEmail }) => {
    const teamRepository = new ConvexTeamRepository(ctx.db);
    const teamInvitationRepository = new ConvexTeamInvitationRepository(ctx.db);
    const teamMemberRepository = new ConvexTeamMemberRepository(ctx.db);
    const identityProvider = new UuidIdentityProvider(crypto);

    const teamInvitationService = new TeamInvitationService(
      teamRepository,
      teamInvitationRepository,
      teamMemberRepository
    );

    const acceptTeamInvitationCommandHandler = new AcceptTeamInvitationCommandHandler(
      teamInvitationService,
      teamInvitationRepository,
      teamMemberRepository,
      identityProvider
    );

    const command = new AcceptTeamInvitationCommand(ctx.user.id, invitationId, userEmail);

    const result = await acceptTeamInvitationCommandHandler.handle(command);

    if (result.isFail()) {
      throw result.getError();
    }
  },
});
