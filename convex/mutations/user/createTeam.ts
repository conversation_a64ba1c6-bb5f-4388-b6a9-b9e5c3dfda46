import { protectedMutation } from '@/convex/helpers';
import { CreateTeamCommand } from '@/src/server/application/usecases/CreateTeam/CreateTeamCommand';
import { CreateTeamCommandHandler } from '@/src/server/application/usecases/CreateTeam/CreateTeamCommandHandler';
import { UuidIdentityProvider } from '@/src/server/infrastructure/providers/IdentityProvider/UuidIdentityProvider';
import { ConvexTeamRepository } from '@/src/server/infrastructure/repositories/TeamRepository/ConvexTeamRepository';
import { v } from 'convex/values';

export const endpoint = protectedMutation({
  args: {
    name: v.string(),
  },
  handler: async (ctx, { name }) => {
    const teamRepository = new ConvexTeamRepository(ctx.db);
    const identityProvider = new UuidIdentityProvider(crypto);

    const createTeamCommandHandler = new CreateTeamCommandHandler(teamRepository, identityProvider);

    const command = new CreateTeamCommand(ctx.user.id, name);
    return await createTeamCommandHandler.handle(command);
  },
});
