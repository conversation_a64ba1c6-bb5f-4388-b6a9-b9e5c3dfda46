import { protectedMutation } from '@/convex/helpers';
import { RejectTeamInvitationCommand } from '@/src/server/application/usecases/RejectTeamInvitation/RejectTeamInvitationCommand';
import { RejectTeamInvitationCommandHandler } from '@/src/server/application/usecases/RejectTeamInvitation/RejectTeamInvitationCommandHandler';
import { ConvexTeamInvitationRepository } from '@/src/server/infrastructure/repositories/TeamInvitationRepository/ConvexTeamInvitationRepository';
import { v } from 'convex/values';

export const endpoint = protectedMutation({
  args: {
    invitationId: v.string(),
  },
  handler: async (ctx, { invitationId }) => {
    const teamInvitationRepository = new ConvexTeamInvitationRepository(ctx.db);
    const rejectTeamInvitationCommandHandler = new RejectTeamInvitationCommandHandler(teamInvitationRepository);
    const command = new RejectTeamInvitationCommand(ctx.user.id, invitationId);
    const result = await rejectTeamInvitationCommandHandler.handle(command);

    if (result.isFail()) {
      throw new Error(result.getError().message);
    }
  },
});
