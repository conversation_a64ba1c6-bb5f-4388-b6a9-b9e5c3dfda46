import { protectedMutation } from '@/convex/helpers';
import { UpdateCodeReviewCommand } from '@/src/server/application/usecases/UpdateCodeReview/UpdateCodeReviewCommand';
import { UpdateCodeReviewCommandHandler } from '@/src/server/application/usecases/UpdateCodeReview/UpdateCodeReviewCommandHandler';
import { UpdateCodeSnippetsCommand } from '@/src/server/application/usecases/UpdateCodeSnippets/UpdateCodeSnippetsCommand';
import { UpdateCodeSnippetsCommandHandler } from '@/src/server/application/usecases/UpdateCodeSnippets/UpdateCodeSnippetsCommandHandler';
import { ConvexCodeReviewRepository } from '@/src/server/infrastructure/repositories/CodeReviewRepository/ConvexCodeReviewRepository';
import { ConvexCodeSnippetRepository } from '@/src/server/infrastructure/repositories/CodeSnippetRepository/ConvexCodeSnippetRepository';
import { v } from 'convex/values';

export const endpoint = protectedMutation({
  args: {
    codeReviewId: v.string(),
    title: v.string(),
    snippets: v.array(
      v.object({
        id: v.string(),
        filename: v.string(),
        content: v.string(),
        language: v.string(),
      })
    ),
  },
  handler: async (ctx, { codeReviewId, title, snippets }) => {
    const codeReviewRepository = new ConvexCodeReviewRepository(ctx.db);
    const codeSnippetRepository = new ConvexCodeSnippetRepository(ctx.db);

    const updateCodeReviewCommandHandler = new UpdateCodeReviewCommandHandler(codeReviewRepository);
    const updateCodeSnippetsCommandHandler = new UpdateCodeSnippetsCommandHandler(codeSnippetRepository);

    const updateCodeReviewCommand = new UpdateCodeReviewCommand(codeReviewId, title, ctx.user.id);
    const codeReviewResult = await updateCodeReviewCommandHandler.handle(updateCodeReviewCommand);

    if (codeReviewResult.isFail()) {
      throw codeReviewResult.getError();
    }

    if (snippets.length > 0) {
      const updateCodeSnippetsCommand = new UpdateCodeSnippetsCommand(codeReviewId, snippets);
      const codeSnippetsResult = await updateCodeSnippetsCommandHandler.handle(updateCodeSnippetsCommand);

      if (codeSnippetsResult.isFail()) {
        throw codeSnippetsResult.getError();
      }
    }

    return codeReviewId;
  },
});
