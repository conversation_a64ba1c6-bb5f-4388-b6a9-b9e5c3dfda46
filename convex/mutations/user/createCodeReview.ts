import { protectedMutation } from '@/convex/helpers';
import { CreateCodeReviewCommand } from '@/src/server/application/usecases/CreateCodeReview/CreateCodeReviewCommand';
import { CreateCodeReviewCommandHandler } from '@/src/server/application/usecases/CreateCodeReview/CreateCodeReviewCommandHandler';
import { CreateCodeSnippetsCommand } from '@/src/server/application/usecases/CreateCodeSnippets/CreateCodeSnippetsCommand';
import { CreateCodeSnippetsCommandHandler } from '@/src/server/application/usecases/CreateCodeSnippets/CreateCodeSnippetsCommandHandler';
import { UuidIdentityProvider } from '@/src/server/infrastructure/providers/IdentityProvider/UuidIdentityProvider';
import { ConvexCodeReviewRepository } from '@/src/server/infrastructure/repositories/CodeReviewRepository/ConvexCodeReviewRepository';
import { ConvexCodeSnippetRepository } from '@/src/server/infrastructure/repositories/CodeSnippetRepository/ConvexCodeSnippetRepository';
import { v } from 'convex/values';

export const endpoint = protectedMutation({
  args: {
    title: v.string(),
    snippets: v.array(
      v.object({
        id: v.string(),
        filename: v.string(),
        content: v.string(),
        language: v.string(),
      })
    ),
  },
  handler: async (ctx, { title, snippets }) => {
    const codeReviewRepository = new ConvexCodeReviewRepository(ctx.db);
    const codeSnippetRepository = new ConvexCodeSnippetRepository(ctx.db);
    const identityProvider = new UuidIdentityProvider(crypto);

    const createCodeReviewCommandHandler = new CreateCodeReviewCommandHandler(codeReviewRepository, identityProvider);

    const createCodeSnippetsCommandHandler = new CreateCodeSnippetsCommandHandler(
      codeSnippetRepository,
      identityProvider
    );

    const createCodeReviewCommand = new CreateCodeReviewCommand(title, ctx.user.id);
    const codeReviewResult = await createCodeReviewCommandHandler.handle(createCodeReviewCommand);

    if (codeReviewResult.isFail()) {
      throw codeReviewResult.getError();
    }

    const codeReviewId = codeReviewResult.getValue();

    const createCodeSnippetsCommand = new CreateCodeSnippetsCommand(codeReviewId, snippets);
    const codeSnippetsResult = await createCodeSnippetsCommandHandler.handle(createCodeSnippetsCommand);

    if (codeSnippetsResult.isFail()) {
      throw codeSnippetsResult.getError();
    }

    return codeReviewId;
  },
});
