import { protectedMutation } from '@/convex/helpers';
import { DeleteFeedbackCommand } from '@/src/server/application/usecases/DeleteFeedback/DeleteFeedbackCommand';
import { DeleteFeedbackCommandHandler } from '@/src/server/application/usecases/DeleteFeedback/DeleteFeedbackCommandHandler';
import { ConvexFeedbackRepository } from '@/src/server/infrastructure/repositories/FeedbackRepository/ConvexFeedbackRepository';
import { v } from 'convex/values';

export const endpoint = protectedMutation({
  args: {
    feedbackId: v.string(),
  },
  handler: async (ctx, { feedbackId }) => {
    const repository = new ConvexFeedbackRepository(ctx);
    const commandHandler = new DeleteFeedbackCommandHandler(repository);

    const command = new DeleteFeedbackCommand(feedbackId, ctx.user.id);

    const result = await commandHandler.handle(command);

    if (result.isFail()) {
      throw result.getError();
    }
  },
});
