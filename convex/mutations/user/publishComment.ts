import { protectedMutation } from '@/convex/helpers';
import { PublishCommentCommand } from '@/src/server/application/usecases/PublishComment/PublishCommentCommand';
import { PublishCommentCommandHandler } from '@/src/server/application/usecases/PublishComment/PublishCommentCommandHandler';
import { UuidIdentityProvider } from '@/src/server/infrastructure/providers/IdentityProvider/UuidIdentityProvider';
import { ConvexCommentRepository } from '@/src/server/infrastructure/repositories/CommentRepository/ConvexCommentRepository';
import { v } from 'convex/values';

export const endpoint = protectedMutation({
  args: {
    feedbackId: v.string(),
    content: v.string(),
    parentId: v.optional(v.string()),
  },
  handler: async (ctx, { feedbackId, content, parentId }) => {
    const command = new PublishCommentCommand(ctx.user.id, feedbackId, content, parentId);
    const commentRepository = new ConvexCommentRepository(ctx.db);
    const identityProvider = new UuidIdentityProvider(crypto);
    const commandHandler = new PublishCommentCommandHandler(commentRepository, identityProvider);

    return await commandHandler.handle(command);
  },
});
