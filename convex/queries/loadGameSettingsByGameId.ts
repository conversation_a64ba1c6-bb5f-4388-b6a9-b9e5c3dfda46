import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";

export const endpoint = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, {gameId}) => {
    const settings = await ctx.db
      .query("gameSettings")
      .withIndex("by_gameId", (q) => q.eq("gameId", gameId))
      .first();

    return {
      error: null,
      data: settings
    };
  },
});