import {protectedQuery} from "../helpers";
import {v} from "convex/values";
import {
  LoadDecksByUserIdAndGameIdQueryHandler
} from "@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdQueryHandler";

export const endpoint = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, {gameId}) => {
    const queryHandler = new LoadDecksByUserIdAndGameIdQueryHandler(ctx);
    return await queryHandler.handle({gameId, userId: ctx.userId});
  },
});