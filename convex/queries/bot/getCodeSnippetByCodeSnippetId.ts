import { botQuery } from '@/convex/helpers';
import { getOneFrom } from 'convex-helpers/server/relationships';
import { v } from 'convex/values';

export const endpoint = botQuery({
  args: {
    codeSnippetId: v.string(),
  },
  handler: async ({ db }, { codeSnippetId }) => {
    const codeSnippet = await getOneFrom(db, 'codeSnippets', 'by_codeSnippetId', codeSnippetId);

    if (!codeSnippet) {
      return null;
    }

    return {
      id: codeSnippet.codeSnippetId,
      filename: codeSnippet.filename,
      language: codeSnippet.language,
      content: codeSnippet.content,
    };
  },
});
