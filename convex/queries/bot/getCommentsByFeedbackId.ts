import { botQuery } from '@/convex/helpers';
import { asyncMap } from 'convex-helpers';
import { getOneFrom } from 'convex-helpers/server/relationships';
import type { GenericDatabaseReader } from 'convex/server';
import { v } from 'convex/values';
import type { DataModel } from '../../_generated/dataModel';

export const endpoint = botQuery({
  args: {
    feedbackId: v.string(),
  },
  handler: async ({ db }, { feedbackId }) => {
    // Get all top-level comments (no parentId) for this feedback
    const topLevelComments = await getPublishedCommentsByFeedbackId(db, feedbackId);

    if (topLevelComments.length === 0) {
      return { comments: [] };
    }

    // Process each top-level comment and include its replies
    const commentsWithReplies = await asyncMap(topLevelComments, async (comment) => {
      const author = await getOneFrom(db, 'craftUsers', 'by_craftUserId', comment.userId);
      const repliesDocument = await getRepliesByParentCommentId(db, comment.commentId);
      const replies = await asyncMap(repliesDocument, async (reply) => {
        const replyAuthor = await getOneFrom(db, 'craftUsers', 'by_craftUserId', reply.userId);

        return {
          id: reply.commentId,
          content: reply.content,
          createdAt: reply.createdAt,
          author: replyAuthor ? { name: replyAuthor.name } : undefined,
        };
      });

      return {
        id: comment.commentId,
        content: comment.content,
        createdAt: comment.createdAt,
        updatedAt: comment.updatedAt,
        author: author ? { name: author.name } : undefined,
        replies,
      };
    });

    return { comments: commentsWithReplies };
  },
});

function getPublishedCommentsByFeedbackId(db: GenericDatabaseReader<DataModel>, feedbackId: string) {
  return db
    .query('comments')
    .withIndex('by_feedbackId_and_status_and_parentId', (q) =>
      q.eq('feedbackId', feedbackId).eq('status', 'published').eq('parentId', undefined)
    )
    .collect();
}

function getRepliesByParentCommentId(db: GenericDatabaseReader<DataModel>, parentId: string) {
  return db
    .query('comments')
    .withIndex('by_parentId_and_status', (q) => q.eq('parentId', parentId).eq('status', 'published'))
    .collect();
}


