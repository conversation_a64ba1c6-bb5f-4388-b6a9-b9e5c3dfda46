import { botQuery } from '@/convex/helpers';
import { asyncMap } from 'convex-helpers';
import { getOneFrom } from 'convex-helpers/server/relationships';
import { v } from 'convex/values';

export const endpoint = botQuery({
  args: {
    codeSnippetId: v.string(),
  },
  handler: async ({ db }, { codeSnippetId }) => {
    const feedbacks = await db
      .query('feedbacks')
      .withIndex('by_codeSnippetId_and_deleted', (q) => q.eq('codeSnippetId', codeSnippetId).eq('deleted', false))
      .collect();

    if (feedbacks.length === 0) {
      return { feedbacks: [] };
    }

    const feedbacksWithAuthors = await asyncMap(feedbacks, async (feedback) => {
      const author = await getOneFrom(db, 'craftUsers', 'by_craftUserId', feedback.userId);
      
      return {
        id: feedback.feedbackId,
        title: feedback.title,
        content: feedback.content,
        type: feedback.type,
        subType: feedback.subType,
        startLine: feedback.startLine,
        endLine: feedback.endLine,
        createdAt: feedback.createdAt,
        updatedAt: feedback.updatedAt,
        author: author ? { name: author.name } : undefined,
      };
    });

    return { feedbacks: feedbacksWithAuthors };
  },
});
