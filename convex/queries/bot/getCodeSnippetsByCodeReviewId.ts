import {botQuery} from '@/convex/helpers';
import {getManyFrom} from 'convex-helpers/server/relationships';
import {v} from 'convex/values';

export const endpoint = botQuery({
  args: {
    codeReviewId: v.string(),
  },
  handler: async ({db}, {codeReviewId}) => {
    const snippets = await getManyFrom(db, 'codeSnippets', 'by_codeReviewId', codeReviewId);

    return {
      codeSnippets: snippets.map((snippet) => ({
        id: snippet.codeSnippetId,
        filename: snippet.filename,
      })),
    };
  },
});
