import {v} from "convex/values";
import {protectedQuery} from "@/convex/helpers";
import {
  LoadMatchEndedEventQueryHandler
} from "@/src/server/Gaming/application/queries/LoadMatchEndedEvent/LoadMatchEndedEventQueryHandler";

export const endpoint = protectedQuery({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const queryHandler = new LoadMatchEndedEventQueryHandler(ctx);
    return await queryHandler.handle({matchId});
  },
});