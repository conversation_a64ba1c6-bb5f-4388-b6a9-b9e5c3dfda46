import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {Id} from "@/convex/_generated/dataModel";

export const endpoint = protectedQuery({
  args: {
    matchId: v.string(),
  },
  handler: async (ctx, {matchId}) => {
    const match = await ctx.db.get(matchId as Id<"matches">);

    if (!match) {
      return {
        error: "Match not found",
        data: null
      };
    }

    return {
      error: null,
      data: {
        id: match._id,
        status: match.status,
        players: match.players,
        isWinner: match.winner === ctx.userId,
        gameId: match.gameId
      }
    };
  },
});