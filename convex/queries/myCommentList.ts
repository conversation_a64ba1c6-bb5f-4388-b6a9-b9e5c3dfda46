import type { DataModel } from '@/convex/_generated/dataModel';
import { protectedQuery } from '@/convex/helpers';
import { asyncMap } from 'convex-helpers';
import { type GenericQueryCtx, type PaginationOptions, paginationOptsValidator } from 'convex/server';

export const endpoint = protectedQuery({
  args: { paginationOpts: paginationOptsValidator },
  handler: async (ctx, args) => {
    const paginatedResult = await getPublishedUserComments(ctx, ctx.user.id, args.paginationOpts);

    const comments = await asyncMap(paginatedResult.page, async (comment) => {
      const feedback = await ctx.db
        .query('feedbacks')
        .withIndex('by_feedbackId', (q) => q.eq('feedbackId', comment.feedbackId))
        .first();

      if (!feedback) {
        throw new Error(`Feedback not found for comment ${comment.commentId}`);
      }

      const codeSnippet = await ctx.db
        .query('codeSnippets')
        .withIndex('by_codeSnippetId', (q) => q.eq('codeSnippetId', feedback.codeSnippetId))
        .first();

      if (!codeSnippet) {
        throw new Error(`Code snippet not found for feedback ${feedback.feedbackId}`);
      }

      const codeReview = await ctx.db
        .query('codeReviews')
        .withIndex('by_codeReviewId', (q) => q.eq('codeReviewId', codeSnippet.codeReviewId))
        .first();

      if (!codeReview) {
        throw new Error(`Code review not found for feedback ${feedback.feedbackId}`);
      }

      return {
        id: comment.commentId,
        author: {
          name: ctx.user.name,
          avatar: ctx.user.image,
          avatarFallback: ctx.user.name[0].toUpperCase() + ctx.user.name[1].toUpperCase(),
          isCurrentUser: true,
        },
        codeSnippet: {
          id: codeSnippet.codeSnippetId,
          content: codeSnippet.content,
          filename: codeSnippet.filename,
          language: codeSnippet.language,
        },
        codeReview: {
          id: codeReview.codeReviewId,
          name: codeReview.title,
          image: generateAvatarUrl(codeReview.codeReviewId),
        },
        feedback: {
          id: feedback.feedbackId,
          title: feedback.title,
          content: feedback.content,
          startLine: feedback.startLine,
          endLine: feedback.endLine,
          type: feedback.type,
          subType: feedback.subType,
        },
        content: comment.content,
        creationDay: comment.createdAt.split('T')[0],
        createdAt: comment.createdAt,
      };
    });

    return {
      ...paginatedResult,
      page: comments,
    };
  },
});

function getPublishedUserComments(ctx: GenericQueryCtx<DataModel>, userId: string, paginationOpts: PaginationOptions) {
  return ctx.db
    .query('comments')
    .withIndex('by_userId_and_status', (q) => q.eq('userId', userId).eq('status', 'published'))
    .order('desc')
    .paginate(paginationOpts);
}

function generateAvatarUrl(codeReviewId: string) {
  return `https://api.dicebear.com/9.x/identicon/svg?rowColor=85E89D,9ECBFF,B392F0,F97583,FFEA7F,F99B15,C9D1D9,0D1117,161B22&seed=${codeReviewId}`;
}
