import { protectedQuery } from '@/convex/helpers';
import { v } from 'convex/values';

export const endpoint = protectedQuery({
  args: {
    codeReviewId: v.string(),
  },
  handler: async ({ db }, { codeReviewId }) => {
    const firstCodeSnippet = await db
      .query('codeSnippets')
      .withIndex('by_codeReviewId', (q) => q.eq('codeReviewId', codeReviewId))
      .first();

    if (!firstCodeSnippet) {
      throw new Error('Code review session has no code snippets');
    }

    return {
      firstCodeSnippetId: firstCodeSnippet.codeSnippetId,
    };
  },
});
