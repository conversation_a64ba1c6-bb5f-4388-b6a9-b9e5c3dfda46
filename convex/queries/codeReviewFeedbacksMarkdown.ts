import { protectedQuery } from '@/convex/helpers';
import { asyncMap } from 'convex-helpers';
import { getMany<PERSON>rom, getOne<PERSON>rom } from 'convex-helpers/server/relationships';
import type { GenericQueryCtx } from 'convex/server';
import { v } from 'convex/values';
import type { DataModel } from '../_generated/dataModel';

type Feedback = {
  feedbackId: string;
  codeSnippetId: string;
  title: string;
  content: string;
  userId: string;
  startLine: number;
  endLine: number;
  type: string;
  subType: string;
  createdAt: string;
  updatedAt: string;
  deleted: boolean;
  author?: {
    name: string;
  };
  comments?: Comment[];
};

type Comment = {
  commentId: string;
  feedbackId: string;
  content: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  status: string;
  author?: {
    name: string;
  };
};

type CodeSnippet = {
  codeSnippetId: string;
  filename: string;
  language: string;
  content: string;
  codeReviewId: string;
  feedbacks?: Feedback[];
};

type CodeReview = {
  codeReviewId: string;
  title: string;
  authorId: string;
  status: string;
  visibility: string;
  participantIds: string[];
  codeSnippets?: CodeSnippet[];
};

export const endpoint = protectedQuery({
  args: {
    codeReviewId: v.string(),
  },
  handler: async (ctx, { codeReviewId }) => {
    const codeReview = await getOneFrom(ctx.db, 'codeReviews', 'by_codeReviewId', codeReviewId);

    if (!codeReview) {
      return { markdown: '' };
    }

    const codeSnippets = await getManyFrom(ctx.db, 'codeSnippets', 'by_codeReviewId', codeReviewId);

    if (codeSnippets.length === 0) {
      return {
        markdown: `# ${codeReview.title}\n\nNo code snippets found in this code review.`,
      };
    }

    const snippetsWithFeedbacks = await asyncMap(codeSnippets, async (snippet) => {
      const feedbacks = await getFeedbacksForCodeSnippet(ctx, snippet.codeSnippetId);
      return {
        ...snippet,
        feedbacks,
      };
    });

    const markdown = generateMarkdown({
      ...codeReview,
      codeSnippets: snippetsWithFeedbacks,
    });

    return { markdown };
  },
});

async function getFeedbacksForCodeSnippet(ctx: GenericQueryCtx<DataModel>, codeSnippetId: string): Promise<Feedback[]> {
  const feedbacks = await ctx.db
    .query('feedbacks')
    .withIndex('by_codeSnippetId_and_deleted', (q) => q.eq('codeSnippetId', codeSnippetId).eq('deleted', false))
    .collect();

  if (feedbacks.length === 0) {
    return [];
  }

  return await asyncMap(feedbacks, async (feedback) => {
    const author = await getOneFrom(ctx.db, 'craftUsers', 'by_craftUserId', feedback.userId);
    const comments = await getCommentsForFeedback(ctx, feedback.feedbackId);

    return {
      ...feedback,
      author: author ? { name: author.name } : undefined,
      comments,
    };
  });
}

async function getCommentsForFeedback(ctx: GenericQueryCtx<DataModel>, feedbackId: string): Promise<Comment[]> {
  const comments = await ctx.db
    .query('comments')
    .withIndex('by_feedbackId_and_status', (q) => q.eq('feedbackId', feedbackId).eq('status', 'published'))
    .collect();

  if (comments.length === 0) {
    return [];
  }

  return await asyncMap(comments, async (comment) => {
    const author = await getOneFrom(ctx.db, 'craftUsers', 'by_craftUserId', comment.userId);

    return {
      ...comment,
      author: author ? { name: author.name } : undefined,
    };
  });
}

function generateMarkdown(codeReview: CodeReview): string {
  let markdown = `# ${codeReview.title}\n\n`;

  for (const snippet of codeReview.codeSnippets || []) {
    markdown += `## ${snippet.filename}\n\n`;

    if (!snippet.feedbacks || snippet.feedbacks.length === 0) {
      markdown += 'No feedbacks for this file.\n\n';
      continue;
    }

    const sortedFeedbacks = [...snippet.feedbacks].sort((a, b) => a.startLine - b.startLine);

    for (const feedback of sortedFeedbacks) {
      const lineRange =
        feedback.startLine === feedback.endLine
          ? `Line ${feedback.startLine}`
          : `Lines ${feedback.startLine}-${feedback.endLine}`;

      markdown += `### ${feedback.title}\n\n`;
      markdown += `**Type:** ${formatFeedbackType(feedback.type)}\n\n`;
      markdown += `**Location:** ${lineRange}\n\n`;
      markdown += `**Author:** ${feedback.author?.name || 'Unknown'}\n\n`;
      markdown += `**Description:**\n\n${feedback.content}\n\n`;

      if (feedback.comments && feedback.comments.length > 0) {
        markdown += '**Comments:**\n\n';

        for (const comment of feedback.comments) {
          markdown += `- **${comment.author?.name || 'Unknown'}**: ${comment.content}\n`;
        }

        markdown += '\n';
      }
    }
  }

  return markdown;
}

function formatFeedbackType(type: string): string {
  switch (type) {
    case 'constructive':
      return '💡 Constructive';
    case 'boyscout':
      return '🧹 Boy Scout';
    case 'missing':
      return '❓ Missing';
    case 'question':
      return '❔ Question';
    default:
      return type;
  }
}
