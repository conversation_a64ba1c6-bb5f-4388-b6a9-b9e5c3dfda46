import { query } from "../_generated/server";

export const endpoint = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;

    const convexId = identity.subject.split('|')[0];

    const user = await ctx.db
      .query('users')
      .filter((q) => q.eq(q.field('_id'), convexId))
      .first();

    if (!user) {
      return null;
    }

    return {
      userId: user._id,
      email: user.email,
    };
  },
});