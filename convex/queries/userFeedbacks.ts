import { protectedQuery } from '@/convex/helpers';
import { asyncMap } from 'convex-helpers';
import { getOneFrom } from 'convex-helpers/server/relationships';
import { paginationOptsValidator } from 'convex/server';

export const endpoint = protectedQuery({
  args: { paginationOpts: paginationOptsValidator },
  handler: async (ctx, args) => {
    const paginatedResult = await ctx.db
      .query('feedbacks')
      .withIndex('by_userId_and_deleted', (q) => q.eq('userId', ctx.user.id).eq('deleted', false))
      .order('desc')
      .paginate(args.paginationOpts);

    const feedbacks = await asyncMap(paginatedResult.page, async (feedback) => {
      const codeSnippet = await getOneFrom(ctx.db, 'codeSnippets', 'by_codeSnippetId', feedback.codeSnippetId);
      if (!codeSnippet) throw new Error(`Code snippet with ID ${feedback.codeSnippetId} not found`);

      const codeReview = await getOneFrom(ctx.db, 'codeReviews', 'by_codeReviewId', codeSnippet.codeReviewId);
      if (!codeReview) throw new Error(`Code review with ID ${codeSnippet.codeReviewId} not found`);

      return {
        id: feedback.feedbackId,
        title: feedback.title,
        content: feedback.content,
        subType: feedback.subType,
        createdAt: feedback.createdAt.split('T')[0],
        startLine: feedback.startLine,
        endLine: feedback.endLine,
        language: codeSnippet.language,
        code: codeSnippet.content,
        codeSnippetId: feedback.codeSnippetId,
        codeSnippetFilename: codeSnippet.filename,
        codeReviewTitle: codeReview.title,
        codeReviewId: codeReview.codeReviewId,
      };
    });

    return {
      ...paginatedResult,
      page: feedbacks,
    };
  },
});
