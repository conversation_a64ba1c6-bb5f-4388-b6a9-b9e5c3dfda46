import {v} from "convex/values";
import {query} from "../_generated/server";

export const matchmakingEventsForGame = query({
  args: {
    gameId: v.id("games"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, {gameId, limit = 20}) => {
    return ctx.db
      .query("matchmakingEvents")
      .withIndex("by_gameId", (q) => q.eq("gameId", gameId))
      .order('desc')
      .take(limit);
  },
});

export const matchEventsForMatch = query({
  args: {
    matchId: v.id("matches"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, {matchId, limit = 20}) => {
    return ctx.db
      .query("matchEvents")
      .withIndex("by_matchId", (q) =>
        q.eq("matchId", matchId),
      )
      .order('desc')
      .take(limit);
  },
});