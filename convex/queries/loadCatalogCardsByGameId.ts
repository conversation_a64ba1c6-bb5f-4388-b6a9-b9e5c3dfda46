import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {
  LoadCatalogCardsByGameIdQueryHandler
} from "@/src/server/DeckBuilding/application/queries/LoadCatalogCardsByGameId/LoadCatalogCardsByGameIdQueryHandler";

export const endpoint = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, {gameId}) => {
    const queryHandler = new LoadCatalogCardsByGameIdQueryHandler(ctx);
    return await queryHandler.handle({gameId});
  },
});