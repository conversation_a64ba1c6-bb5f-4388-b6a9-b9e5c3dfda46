import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {Id} from "@/convex/_generated/dataModel";
import {getManyFrom} from "convex-helpers/server/relationships";
import {groupByDataProperty} from "@/src/client/infrastructure/lib/groupByDataProperty";

export const endpoint = protectedQuery({
  args: {
    gameId: v.string(),
  },
  handler: async (ctx, {gameId}) => {
    const availableFiltersDoc = await getManyFrom(ctx.db, "availableFilters", "by_gameId", gameId as Id<"games">);
    const availableFilters = availableFiltersDoc.map((doc) => ({
      id: doc._id,
      text: doc.text,
      name: doc.name,
      dataProperty: doc.dataProperty,
      dataType: doc.dataType as 'boolean' | 'string' | 'number' | 'string[]',
      value: doc.value,
    }));

    const groupedFilters = groupByDataProperty(availableFilters);

    return {
      error: null,
      data: {
        groupedFilters,
        availableFilters
      }
    };
  },
});
