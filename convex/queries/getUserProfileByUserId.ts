import { protectedQuery } from '@/convex/helpers';
import { getOneFrom } from 'convex-helpers/server/relationships';
import { v } from 'convex/values';

export const endpoint = protectedQuery({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, { userId }) => {
    const ownedTeams = await ctx.db
      .query('teams')
      .withIndex('by_ownerId', (q) => q.eq('ownerId', userId))
      .collect();

    const teamMemberships = await ctx.db
      .query('teamMembers')
      .withIndex('by_userId', (q) => q.eq('userId', userId))
      .collect();

    const memberTeamIds = teamMemberships.map((membership) => membership.teamId);
    const memberOfTeamsRaw = await Promise.all(
      memberTeamIds.map((teamId) => getOneFrom(ctx.db, 'teams', 'by_teamId', teamId))
    );

    const memberOfTeams = memberOfTeamsRaw.filter((team) => team && team.ownerId !== userId);

    const getFormattedTeamMembers = async (teamId: string, ownerId: string) => {
      const teamMembers = await ctx.db
        .query('teamMembers')
        .withIndex('by_teamId', (q) => q.eq('teamId', teamId))
        .collect();

      const allMemberIds = [...new Set([ownerId, ...teamMembers.map((member) => member.userId)])];

      const memberDetails = await Promise.all(
        allMemberIds.map((userId) => getOneFrom(ctx.db, 'craftUsers', 'by_craftUserId', userId))
      );

      return memberDetails
        .filter((member) => member !== null)
        .map((member) => ({
          userId: member.craftUserId,
          name: member.name,
          avatar: member.avatar,
          avatarFallback: member.name.substring(0, 2).toUpperCase(),
          isOwner: member.craftUserId === ownerId,
        }));
    };

    const formattedOwnedTeams = await Promise.all(
      ownedTeams.map(async (team) => ({
        teamId: team.teamId,
        teamName: team.name,
        members: await getFormattedTeamMembers(team.teamId, team.ownerId),
        userRole: 'Owner',
      }))
    );

    const formattedMemberOfTeams = await Promise.all(
      memberOfTeams
        .map(async (team) => {
          if (!team) return null;
          return {
            teamId: team.teamId,
            teamName: team.name,
            members: await getFormattedTeamMembers(team.teamId, team.ownerId),
            userRole: 'Member',
          };
        })
        .filter((team): team is NonNullable<typeof team> => team !== null)
    );

    const allTeams = [...formattedOwnedTeams, ...formattedMemberOfTeams];

    const feedbacks = await ctx.db
      .query('feedbacks')
      .withIndex('by_userId_and_deleted', (q) => q.eq('userId', userId).eq('deleted', false))
      .collect();
    const feedbackCount = feedbacks.length;

    const comments = await ctx.db
      .query('comments')
      .withIndex('by_userId_and_status', (q) => q.eq('userId', userId).eq('status', 'published'))
      .collect();
    const commentCount = comments.length;

    const participations = await ctx.db
      .query('codeReviewParticipation')
      .withIndex('by_userId', (q) => q.eq('userId', userId))
      .collect();

    const uniqueCodeReviewIds = new Set(participations.map((p) => p.codeReviewId));
    const codeReviewCount = uniqueCodeReviewIds.size;

    return {
      id: ctx.user.id,
      name: ctx.user.name,
      avatar: ctx.user.image,
      avatarFallback: ctx.user.name.substring(0, 2).toUpperCase(),
      createdAt: new Date(ctx.user.createdAt).toLocaleDateString(),
      teams: allTeams,
      hasTeams: allTeams.length > 0,
      hasOwnedTeams: formattedOwnedTeams.length > 0,
      isMemberOfTeams: formattedMemberOfTeams.length > 0,
      stats: {
        feedbacks: feedbackCount,
        comments: commentCount,
        codeReviews: codeReviewCount,
      },
      socialLinks: {
        linkedin: 'https://linkedin.com/in/johndoe',
        github: 'https://github.com/johndoe',
        blog: 'https://johndoe.medium.com',
        website: 'https://johndoe.dev',
      },
    };
  },
});
