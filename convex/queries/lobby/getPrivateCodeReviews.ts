import { feedbackCounter } from '@/convex/counters/feedbackCounter';
import { protectedQuery } from '@/convex/helpers';
import { asyncMap } from 'convex-helpers';
import { getManyFrom, getOneFrom } from 'convex-helpers/server/relationships';
import { paginationOptsValidator } from 'convex/server';

const url =
  'https://api.dicebear.com/9.x/identicon/svg?rowColor=85E89D,9ECBFF,B392F0,F97583,FFEA7F,F99B15,C9D1D9,0D1117,161B22&seed=';

export const endpoint = protectedQuery({
  args: { paginationOpts: paginationOptsValidator },
  handler: async (ctx, args) => {
    const paginatedResult = await ctx.db
      .query('codeReviews')
      .withIndex('by_status_and_visibility', (q) => q.eq('status', 'open').eq('visibility', 'private'))
      .order('desc')
      .paginate(args.paginationOpts);

    const privateCodeReviews = await asyncMap(paginatedResult.page, async (codeReview) => {
      const author = await getOneFrom(ctx.db, 'craftUsers', 'by_craftUserId', codeReview.authorId);
      if (!author) return null;

      const codeSnippets = await getManyFrom(ctx.db, 'codeSnippets', 'by_codeReviewId', codeReview.codeReviewId);
      const feedbackCounts = await Promise.all(
        codeSnippets.map(({ codeSnippetId }) => feedbackCounter.count(ctx, `feedbackCounter-${codeSnippetId}`))
      );

      const totalFeedbacks = feedbackCounts.reduce((acc, val) => acc + val, 0);

      const languages = new Set(codeSnippets.map((codeSnippet) => codeSnippet.language));

      return {
        id: codeReview.codeReviewId,
        name: codeReview.title,
        image: `${url}${codeReview.codeReviewId}`,
        links: {
          participation: `code-reviews/${codeReview.codeReviewId}`,
          edition: `lobby/code-reviews/${codeReview.codeReviewId}`,
        },
        author: {
          name: author.name,
          avatar: author.avatar,
        },
        participants: codeReview.participantIds.length,
        status: codeReview.status,
        visibility: codeReview.visibility,
        isOwner: codeReview.authorId === ctx.user.id,
        editable: false,
        totalCodeSnippets: codeSnippets.length,
        totalFeedbacks,
        languages: [...languages],
      };
    });

    return {
      ...paginatedResult,
      page: privateCodeReviews.filter((session) => session !== null),
    };
  },
});
