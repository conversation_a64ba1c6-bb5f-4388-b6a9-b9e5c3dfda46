import { protectedQuery } from '@/convex/helpers';
import { getOneFrom } from 'convex-helpers/server/relationships';

export const endpoint = protectedQuery({
  args: {},
  handler: async (ctx) => {
    const ownedTeams = await ctx.db
      .query('teams')
      .withIndex('by_ownerId', (q) => q.eq('ownerId', ctx.user.id))
      .collect();

    const teamMemberships = await ctx.db
      .query('teamMembers')
      .withIndex('by_userId', (q) => q.eq('userId', ctx.user.id))
      .collect();

    const memberTeamIds = teamMemberships.map((membership) => membership.teamId);
    const memberOfTeamsRaw = await Promise.all(
      memberTeamIds.map((teamId) => getOneFrom(ctx.db, 'teams', 'by_teamId', teamId))
    );
    const memberOfTeams = memberOfTeamsRaw.filter((team) => team && team.ownerId !== ctx.user.id);

    const getFormattedTeamMembers = async (teamId: string, ownerId: string) => {
      const teamMembers = await ctx.db
        .query('teamMembers')
        .withIndex('by_teamId', (q) => q.eq('teamId', teamId))
        .collect();

      const allMemberIds = [...new Set([ownerId, ...teamMembers.map((member) => member.userId)])];

      const memberDetails = await Promise.all(
        allMemberIds.map((userId) => getOneFrom(ctx.db, 'craftUsers', 'by_craftUserId', userId))
      );

      return memberDetails
        .filter((member) => member !== null)
        .map((member) => ({
          userId: member.craftUserId,
          name: member.name,
          avatar: member.avatar,
          avatarFallback: member.name.substring(0, 2).toUpperCase(),
          isOwner: member.craftUserId === ownerId,
        }));
    };

    const formattedOwnedTeams = await Promise.all(
      ownedTeams.map(async (team) => ({
        teamId: team.teamId,
        teamName: team.name,
        members: await getFormattedTeamMembers(team.teamId, team.ownerId),
      }))
    );

    const formattedMemberOfTeams = await Promise.all(
      memberOfTeams
        .map(async (team) => {
          if (!team) return null;
          return {
            teamId: team.teamId,
            teamName: team.name,
            members: await getFormattedTeamMembers(team.teamId, team.ownerId),
          };
        })
        .filter((team): team is NonNullable<typeof team> => team !== null)
    );

    return {
      ownedTeams: formattedOwnedTeams,
      memberOfTeams: formattedMemberOfTeams,
    };
  },
});
