# Comprehensive Guide to Feature Implementation

This document provides a detailed, step-by-step guide for implementing new features in the project, following Domain-Driven Design (DDD) principles, Test-Driven Development (TDD), and the project's established conventions and patterns.

## Introduction

Implementing a new feature in this project requires a systematic approach that respects the clean architecture and DDD principles. This guide will walk you through the entire process from initial planning to final testing, ensuring that your implementation is consistent with the project's architecture and coding standards.

## Project Architecture

The project follows a clean architecture approach with distinct layers, each with specific responsibilities and dependencies:

1. **Domain Layer**
   - Contains entities (aggregates), value objects, and domain services
   - Encapsulates business logic and rules
   - Implements domain events and domain-specific exceptions
   - Is independent of other layers and frameworks
   - Located in `src/server/domain/`
   - Examples: `Comment.ts`, `Feedback.ts`, `CodeReview.ts`

2. **Application Layer**
   - Contains use cases implemented as commands
   - Command Handlers: Process commands and modify the domain (write operations)
   - Defines interfaces (ports) that the infrastructure layer must implement
   - Orchestrates the flow of data between the domain and infrastructure layers
   - Located in `src/server/application/usecases/` and `src/server/application/ports/`
   - Examples: `LikeCommentCommandHandler.ts`
   - Note: Query handlers are NOT implemented in the application layer but directly in the Convex API layer

3. **Infrastructure Layer**
   - Contains implementations of repository interfaces defined in the application layer
   - Provides concrete implementations for external services and data access
   - Adapts external frameworks and libraries to the application's needs
   - Implements data persistence using Convex
   - Located in `src/server/infrastructure/`
   - Examples: `ConvexCommentRepository.ts`, `ConvexAuthenticationGateway.ts`

4. **Presentation Layer**
   - Contains UI components and API endpoints
   - API Endpoints: Thin wrappers that delegate to command/query handlers
   - UI Components: Display data and handle user interactions
   - Feature Hooks: React hooks that interact with API endpoints
   - Located in `src/client/presentation/` and `convex/`
   - Examples: `useLikeCommentFeature.ts`, `CommentList.tsx`, `likeComment.ts`

### Key Architectural Principles

1. **Dependency Rule**: Dependencies only point inward. The domain layer has no dependencies on other layers. The application layer depends only on the domain layer. The infrastructure layer depends on the application and domain layers. The presentation layer depends on all other layers.

2. **Separation of Concerns**: Each layer has a specific responsibility and should not be concerned with the responsibilities of other layers.

3. **Dependency Inversion**: High-level modules (application layer) define interfaces that low-level modules (infrastructure layer) implement.

4. **Entity-Boundary-Interactor Pattern**: Entities (domain layer) contain business rules, boundaries (application layer) define interfaces, and interactors (application layer) implement use cases.

## Feature Implementation Process

Based on the analysis of the codebase, particularly the `todo` directory which contains a step-by-step guide for implementing the `likeComment` feature, here's the complete process for implementing a new feature. This process follows Test-Driven Development (TDD) principles, where tests are written before the implementation code.

### Phase 1: Planning and Requirements Analysis

#### 1. Define Feature Requirements

Before writing any code, clearly define what the feature should do:

- **User Stories**: Write user stories that describe the feature from the user's perspective
  ```
  As a [user role], I want to [action], so that [benefit]
  ```

- **Acceptance Criteria**: Define specific conditions that must be met for the feature to be considered complete
  ```
  Given [context], when [action], then [expected result]
  ```

- **Domain Analysis**: Identify the domain concepts and relationships involved in the feature
  - What entities will be affected?
  - What new entities might be needed?
  - What business rules apply?

- **Technical Requirements**: Identify any technical considerations
  - Performance requirements
  - Security considerations
  - Compatibility requirements

#### 2. Design the Feature

- **Domain Model Design**: Design or update the domain model to support the feature
  - Identify aggregates, entities, and value objects
  - Define aggregate boundaries
  - Identify domain events

- **Use Case Design**: Design the use cases (commands and queries) that will implement the feature
  - What commands will modify the domain?
  - What queries will retrieve data?
  - What are the inputs and outputs of each use case?

- **UI/UX Design**: Design the user interface and user experience
  - Sketch wireframes or mockups
  - Define user interactions
  - Plan UI components and their relationships

### Phase 2: Domain Layer Implementation

The domain layer is the heart of the application, containing the business logic and rules. Following DDD principles, we start by implementing the domain layer first.

#### 1. Create or Update Domain Entities

Domain entities are the core business objects of the application. They encapsulate business logic and rules.

**Step 1: Design and Implement Domain Entities**

Design and implement the domain entities with the necessary methods and properties. The domain entities will be tested through use case tests rather than directly:

```typescript
// src/server/domain/TeamInvitation/TeamInvitation.ts
import {Aggregate, Result} from "@evyweb/simple-ddd-toolkit";
import {TeamInvitationId} from "@/src/server/domain/TeamInvitation/valueObjects/TeamInvitationId";

export type TeamInvitationStatus = 'pending' | 'accepted' | 'declined' | 'cancelled';

export type TeamInvitationProperties = {
    id: TeamInvitationId;
    teamId: string;
    email: string;
    status: TeamInvitationStatus;
    invitedBy: string;
};

export class TeamInvitation extends Aggregate<TeamInvitationProperties> {
    private constructor(properties: TeamInvitationProperties) {
        super(properties);
    }

    static create(properties: TeamInvitationProperties): Result<TeamInvitation, Error> {
        return Result.ok(new TeamInvitation(properties));
    }

    static createPendingInvitation(properties: {
        id: TeamInvitationId;
        teamId: string;
        email: string;
        invitedBy: string;
    }): Result<TeamInvitation, Error> {
        return Result.ok(new TeamInvitation({
            ...properties,
            status: 'pending'
        }));
    }

    static fromRaw(rawData: TeamInvitationProperties): TeamInvitation {
        return new TeamInvitation(rawData);
    }

    isPending(): boolean {
        return this.get('status') === 'pending';
    }
}
```

#### 2. Create or Update Value Objects

Value objects represent concepts in the domain that are defined by their attributes rather than their identity.

**Step 1: Design and Implement Value Objects**

Design and implement value objects to represent concepts in the domain. Value objects will be tested through use case tests rather than directly:

Example of an id value object:

```typescript
// src/server/domain/TeamInvitation/valueObjects/TeamInvitationId.ts
import {UUID} from "@evyweb/simple-ddd-toolkit";

export class TeamInvitationId extends UUID {
}
```

#### 3. Create Domain-Specific Errors

Domain-specific errors help to communicate domain rules and constraints.

```typescript
// src/server/domain/TeamInvitation/errors/InvalidTeamInvitationIdError.ts
import {DomainError} from "@evyweb/simple-ddd-toolkit";

export class TeamInvitationAlreadyExistsError extends DomainError {
    readonly __TAG: string = 'TeamInvitationAlreadyExistsError';

    constructor() {
        super('teamInvitation.errors.alreadyExists');
    }
}
```

#### 4. Create Domain Services

Domain services encapsulate domain logic that doesn't naturally fit within a single entity or value object, especially when the operation involves multiple aggregates.

```typescript
// src/server/domain/services/TeamInvitationService.ts
import {Result} from "@evyweb/simple-ddd-toolkit";
import {NotTeamOwnerError} from "@/src/server/domain/Team/errors/NotTeamOwnerError";
import {TeamInvitationAlreadyExistsError} from "@/src/server/domain/TeamInvitation/errors/TeamInvitationAlreadyExistsError";
import {TeamNotFoundError} from "@/src/server/domain/Team/errors/TeamNotFoundError";
import {TeamRepository} from "@/src/server/application/ports/TeamRepository";
import {TeamInvitationRepository} from "@/src/server/application/ports/TeamInvitationRepository";

export class TeamInvitationService {
    private readonly teamRepository: TeamRepository;
    private readonly teamInvitationRepository: TeamInvitationRepository;

    constructor(
        teamRepository: TeamRepository,
        teamInvitationRepository: TeamInvitationRepository
    ) {
        this.teamRepository = teamRepository;
        this.teamInvitationRepository = teamInvitationRepository;
    }

    async canSendInvitation(currentUserId: string, teamId: string, email: string): Promise<Result<void, TeamNotFoundError | NotTeamOwnerError | TeamInvitationAlreadyExistsError>> {
        const teamResult = await this.teamRepository.findById(teamId);

        if (teamResult.isFail()) {
            return Result.fail(teamResult.getError());
        }

        const team = teamResult.getValue();

        if (!team.isOwnedBy(currentUserId)) {
            return Result.fail(new NotTeamOwnerError());
        }

        const existingInvitationResult = await this.teamInvitationRepository.findByEmailAndTeamId(email, teamId);

        if (existingInvitationResult.isOk()) {
            const existingInvitation = existingInvitationResult.getValue();

            if (existingInvitation.isPending()) {
                return Result.fail(new TeamInvitationAlreadyExistsError());
            }
        }

        return Result.ok(void 0);
    }
}
```

#### Key Domain Layer Principles

1. **Rich Domain Model**: Domain entities should encapsulate business logic and rules, not just data.
2. **Immutability**: Value objects should be immutable.
3. **Validation**: Domain objects should validate their state and reject invalid states.
4. **Ubiquitous Language**: Use the same terminology in code as domain experts use.
5. **Encapsulation**: Hide implementation details and expose only what's necessary.
6. **Result Pattern**: Use the Result pattern to handle success and failure cases explicitly.
7. **Domain Services**: Use domain services for operations that involve multiple aggregates or don't naturally fit within an entity.

### The Result Pattern

The project uses the Result pattern from the `@evyweb/simple-ddd-toolkit` library to handle success and failure cases in a type-safe and explicit way. This pattern is particularly useful for domain operations that can fail due to business rules or validation errors.

#### How the Result Pattern Works

1. **Creating Results**

   - `Result.ok(value)`: Creates a successful result with a value
   - `Result.fail(error)`: Creates a failed result with an error

2. **Checking Results**

   - `isOk()`: Returns true if the result is successful
   - `isFail()`: Returns true if the result is a failure

3. **Extracting Values**

   - `getValue()`: Returns the value if the result is successful
   - `getError()`: Returns the error if the result is a failure

   ```typescript
   if (result.isOk()) {
     const commentId = result.getValue();
     // Use commentId
   } else {
     const error = result.getError();
     // Handle error
   }
   ```

#### Benefits of the Result Pattern

1. **Explicit Error Handling**: Forces developers to explicitly handle both success and failure cases.
2. **Type Safety**: Provides type information for both success and failure cases.
3. **No Exceptions**: Avoids using exceptions for control flow, making the code more predictable.
4. **Self-Documenting**: Makes it clear which operations can fail and what types of errors they can produce.
5. **Composability**: Results can be easily composed and chained together.

#### When to Use the Result Pattern

1. **Domain Object Creation**: When creating domain objects that require validation.
2. **Repository Operations**: When retrieving entities that might not exist.
3. **Command Handlers**: When executing commands that can fail due to business rules.
4. **Value Object Creation**: When creating value objects that require validation.

### Phase 3: Application Layer Implementation

The application layer contains the use cases of the application. In this project, commands and queries are handled differently:

- **Commands**: Implemented in the application layer (`src/server/application/usecases/`) with command classes and handlers
- **Queries**: Implemented directly in the Convex API layer (`convex/queries/`), although some complex queries may use query classes and handlers in the application layer

#### 1. Define Repository Interfaces

Repository interfaces define how the application layer interacts with the data storage. They are defined in the application layer but implemented in the infrastructure layer.

```typescript
// src/server/application/ports/TeamInvitationRepository.ts
import {Result} from "@evyweb/simple-ddd-toolkit";
import {TeamInvitation} from "@/src/server/domain/TeamInvitation/TeamInvitation";
import {TeamInvitationNotFoundError} from "@/src/server/domain/TeamInvitation/errors/TeamInvitationNotFoundError";

export interface TeamInvitationRepository {
    findByEmailAndTeamId(email: string, teamId: string): Promise<Result<TeamInvitation, TeamInvitationNotFoundError>>;
    save(invitation: TeamInvitation): Promise<void>;
}
```

#### 2. Create Command Types

Commands represent user intentions to modify the domain. They are simple data structures with no behavior.

```typescript
// src/server/application/usecases/SendTeamInvitation/SendTeamInvitationCommand.ts
export class SendTeamInvitationCommand {
    constructor(
        public readonly currentUserId: string,
        public readonly teamId: string,
        public readonly email: string
    ) {}
}
```

#### 3. Define Command Response Types

Command responses define the possible outcomes of a command execution.

```typescript
// src/server/application/usecases/SendTeamInvitation/SendTeamInvitationResponse.ts
import {Result} from "@evyweb/simple-ddd-toolkit";
import {TeamNotFoundError} from "@/src/server/domain/Team/errors/TeamNotFoundError";
import {NotTeamOwnerError} from "@/src/server/domain/Team/errors/NotTeamOwnerError";
import {TeamInvitationAlreadyExistsError} from "@/src/server/domain/TeamInvitation/errors/TeamInvitationAlreadyExistsError";

export type SendTeamInvitationResponse = Promise<Result<string, TeamNotFoundError | NotTeamOwnerError | TeamInvitationAlreadyExistsError>>;
```

#### 4. Write Tests for Command Handlers

Following TDD, write tests for the command handler before implementing it:

```typescript
// src/server/specs/unit/SendTeamInvitation/SendTeamInvitationCommandHandler.spec.ts
import {createFakeId} from "@/src/server/specs/helpers/factories/fakeIds";
import {SendTeamInvitationSteps} from "@/src/server/specs/unit/SendTeamInvitation/SendTeamInvitationSteps";

describe('Feature: Sending Team Invitation', () => {
    let _: SendTeamInvitationSteps;

    beforeEach(() => {
        _ = new SendTeamInvitationSteps();
    });

    describe('Scenario: Team owner sends an invitation', () => {
        it('should create a new invitation for the recipient', async () => {
            const teamId = createFakeId('1');
            const ownerId = createFakeId('2');
            await _.givenATeamExists(teamId, ownerId);
            await _.whenUserSendsInvitation(ownerId, teamId, '<EMAIL>');
            await _.thenInvitationShouldBeCreated();
        });
    });

    describe('Scenario: Non-owner tries to send an invitation', () => {
        it('should reject the request as only team owners can send invitations', async () => {
            const teamId = createFakeId('3');
            const ownerId = createFakeId('4');
            const nonOwnerId = createFakeId('5');
            await _.givenATeamExists(teamId, ownerId);
            await _.whenUserSendsInvitation(nonOwnerId, teamId, '<EMAIL>');
            await _.thenRequestShouldBeRejectedAsNotTeamOwner();
        });
    });

    describe('Scenario: Team owner tries to send an invitation to an email that already has a pending invitation', () => {
        it('should reject the duplicate invitation as one is already pending', async () => {
            const teamId = createFakeId('6');
            const ownerId = createFakeId('7');
            const email = '<EMAIL>';
            await _.givenATeamExists(teamId, ownerId);
            await _.givenAPendingInvitationExists(teamId, email, ownerId);
            await _.whenUserSendsInvitation(ownerId, teamId, email);
            await _.thenRequestShouldBeRejectedAsDuplicateInvitation();
        });
    });

    describe('Scenario: User tries to send an invitation for a non-existent team', () => {
        it('should reject the request as the team does not exist', async () => {
            const nonExistentTeamId = createFakeId('8');
            const userId = createFakeId('9');
            await _.whenUserSendsInvitation(userId, nonExistentTeamId, '<EMAIL>');
            await _.thenRequestShouldBeRejectedAsTeamNotFound();
        });
    });
});
```

```typescript
// src/server/specs/unit/SendTeamInvitation/SendTeamInvitationSteps.ts
import {Result} from "@evyweb/simple-ddd-toolkit";
import {InMemoryTeamRepository} from "@/src/server/infrastructure/repositories/TeamRepository/InMemoryTeamRepository";
import {InMemoryTeamInvitationRepository} from "@/src/server/infrastructure/repositories/TeamInvitationRepository/InMemoryTeamInvitationRepository";
import {SendTeamInvitationCommandHandler} from "@/src/server/application/usecases/SendTeamInvitation/SendTeamInvitationCommandHandler";
import {SendTeamInvitationCommand} from "@/src/server/application/usecases/SendTeamInvitation/SendTeamInvitationCommand";
import {Team} from "@/src/server/domain/Team/Team";
import {TeamId} from "@/src/server/domain/Team/valueObjects/TeamId";
import {TeamInvitation} from "@/src/server/domain/TeamInvitation/TeamInvitation";
import {TeamInvitationId} from "@/src/server/domain/TeamInvitation/valueObjects/TeamInvitationId";
import {NotTeamOwnerError} from "@/src/server/domain/Team/errors/NotTeamOwnerError";
import {TeamInvitationAlreadyExistsError} from "@/src/server/domain/TeamInvitation/errors/TeamInvitationAlreadyExistsError";
import {TeamNotFoundError} from "@/src/server/domain/Team/errors/TeamNotFoundError";
import {FakeIdentityProvider} from "@/src/server/infrastructure/providers/IdentityProvider/FakeIdentityProvider";
import {createFakeId} from "@/src/server/specs/helpers/factories/fakeIds";
import {TeamInvitationService} from "@/src/server/domain/services/TeamInvitationService";

export class SendTeamInvitationSteps {
    private teamRepository: InMemoryTeamRepository;
    private teamInvitationRepository: InMemoryTeamInvitationRepository;
    private teamInvitationService: TeamInvitationService;
    private handler: SendTeamInvitationCommandHandler;
    private result: Result<string, NotTeamOwnerError | TeamInvitationAlreadyExistsError | TeamNotFoundError> | null = null;
    private identityProvider: FakeIdentityProvider;

    constructor() {
        this.teamRepository = new InMemoryTeamRepository();
        this.teamInvitationRepository = new InMemoryTeamInvitationRepository();
        this.identityProvider = new FakeIdentityProvider();
        this.identityProvider.setNextIds(createFakeId('1'));
        this.teamInvitationService = new TeamInvitationService(
            this.teamRepository,
            this.teamInvitationRepository
        );
        this.handler = new SendTeamInvitationCommandHandler(
            this.teamInvitationService,
            this.teamInvitationRepository,
            this.identityProvider
        );
    }

    async givenATeamExists(teamId: string, ownerId: string): Promise<void> {
        const team = Team.create({
            id: TeamId.createFrom(teamId).getValue(),
            name: 'Test Team',
            ownerId: ownerId,
            createdAt: new Date().toISOString()
        }).getValue();

        await this.teamRepository.save(team);
    }

    async givenAPendingInvitationExists(teamId: string, email: string, invitedBy: string): Promise<void> {
        const invitation = TeamInvitation.createPendingInvitation({
            id: TeamInvitationId.createFrom(createFakeId('2')),
            teamId,
            email,
            invitedBy
        }).getValue();

        await this.teamInvitationRepository.save(invitation);
    }

    async whenUserSendsInvitation(userId: string, teamId: string, email: string): Promise<void> {
        const command = new SendTeamInvitationCommand(userId, teamId, email);
        this.result = await this.handler.handle(command);
    }

    async thenInvitationShouldBeCreated(): Promise<void> {
        expect(this.result?.isOk()).toBe(true);
        const invitationId = this.result?.getValue();
        expect(invitationId).toBe(createFakeId('1'));
    }

    async thenRequestShouldBeRejectedAsNotTeamOwner(): Promise<void> {
        expect(this.result?.isFail()).toBe(true);
        expect(this.result?.getError()).toBeInstanceOf(NotTeamOwnerError);
    }

    async thenRequestShouldBeRejectedAsDuplicateInvitation(): Promise<void> {
        expect(this.result?.isFail()).toBe(true);
        expect(this.result?.getError()).toBeInstanceOf(TeamInvitationAlreadyExistsError);
    }

    async thenRequestShouldBeRejectedAsTeamNotFound(): Promise<void> {
        expect(this.result?.isFail()).toBe(true);
        expect(this.result?.getError()).toBeInstanceOf(TeamNotFoundError);
    }
}
```

#### 5. Implement Command Handlers

Implement the command handler to make the tests pass:

```typescript
// src/server/application/usecases/SendTeamInvitation/SendTeamInvitationCommandHandler.ts
import {SendTeamInvitationCommand} from "./SendTeamInvitationCommand";
import {SendTeamInvitationResponse} from "./SendTeamInvitationResponse";
import {TeamInvitationRepository} from "@/src/server/application/ports/TeamInvitationRepository";
import {IdentityProvider} from "@/src/server/application/ports/IdentityProvider";
import {TeamInvitation} from "@/src/server/domain/TeamInvitation/TeamInvitation";
import {TeamInvitationId} from "@/src/server/domain/TeamInvitation/valueObjects/TeamInvitationId";
import {Result} from "@evyweb/simple-ddd-toolkit";
import {TeamInvitationService} from "@/src/server/domain/services/TeamInvitationService";

export class SendTeamInvitationCommandHandler {
    private readonly teamInvitationService: TeamInvitationService;
    private readonly teamInvitationRepository: TeamInvitationRepository;
    private readonly identityProvider: IdentityProvider;

    constructor(
        teamInvitationService: TeamInvitationService,
        teamInvitationRepository: TeamInvitationRepository,
        identityProvider: IdentityProvider
    ) {
        this.teamInvitationService = teamInvitationService;
        this.teamInvitationRepository = teamInvitationRepository;
        this.identityProvider = identityProvider;
    }

    async handle({currentUserId, teamId, email}: SendTeamInvitationCommand): Promise<SendTeamInvitationResponse> {
        const canSendResult = await this.teamInvitationService.canSendInvitation(currentUserId, teamId, email);

        if (canSendResult.isFail()) {
            return Result.fail(canSendResult.getError());
        }

        const invitationId = this.identityProvider.generateId();

        const invitation = TeamInvitation.createPendingInvitation({
            id: TeamInvitationId.createFrom(invitationId),
            teamId,
            email,
            invitedBy: currentUserId
        }).getValue();

        await this.teamInvitationRepository.save(invitation);

        return Result.ok(invitationId);
    }
}
```

#### 6. Implement Queries

In this project, queries must be implemented directly in the Convex API layer. This is the required approach, not an alternative option.

**Simple Query Implementation (Direct in API Layer)**

```typescript
import {query} from "@/convex/_generated/server";
import {
    ConvexAuthenticationGateway
} from "@/src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway";
import {getOneFrom} from "convex-helpers/server/relationships";

export const endpoint = query({
    args: {},
    handler: async (ctx) => {
        const authenticationGateway = new ConvexAuthenticationGateway(ctx);
        const user = await authenticationGateway.getAuthenticatedUser();

        const ownedTeams = await ctx.db
            .query("teams")
            .withIndex("by_ownerId", q => q.eq('ownerId', user.id))
            .collect();

        const teamMemberships = await ctx.db
            .query("teamMembers")
            .withIndex("by_userId", q => q.eq('userId', user.id))
            .collect();

        const memberTeamIds = teamMemberships.map(membership => membership.teamId);
        const memberOfTeamsRaw = await Promise.all(
            memberTeamIds.map(teamId =>
                getOneFrom(ctx.db, 'teams', 'by_teamId', teamId)
            )
        );
        const memberOfTeams = memberOfTeamsRaw.filter(team => team && team.ownerId !== user.id);

        const getFormattedTeamMembers = async (teamId: string, ownerId: string) => {
            const teamMembers = await ctx.db
                .query("teamMembers")
                .withIndex("by_teamId", q => q.eq('teamId', teamId))
                .collect();

            const allMemberIds = [...new Set([ownerId, ...teamMembers.map(member => member.userId)])];

            const memberDetails = await Promise.all(
                allMemberIds.map(userId =>
                    getOneFrom(ctx.db, 'craftUsers', 'by_craftUserId', userId)
                )
            );

            return memberDetails
                .filter(member => member !== null)
                .map(member => ({
                    userId: member.craftUserId,
                    name: member.name,
                    avatar: member.avatar,
                    avatarFallback: member.name.substring(0, 2).toUpperCase(),
                    isOwner: member.craftUserId === ownerId
                }));
        };

        const formattedOwnedTeams = await Promise.all(
            ownedTeams.map(async team => ({
                teamId: team.teamId,
                teamName: team.name,
                members: await getFormattedTeamMembers(team.teamId, team.ownerId)
            }))
        );

        const formattedMemberOfTeams = await Promise.all(
            memberOfTeams.map(async team => {
                if (!team) return null;
                return {
                    teamId: team.teamId,
                    teamName: team.name,
                    members: await getFormattedTeamMembers(team.teamId, team.ownerId)
                };
            }).filter((team): team is NonNullable<typeof team> => team !== null)
        );

        return {
            ownedTeams: formattedOwnedTeams,
            memberOfTeams: formattedMemberOfTeams
        };
    }
});
```

#### Key Application Layer Principles

1. **Single Responsibility**: Each command or query handler should have a single responsibility.
2. **Dependency Inversion**: The application layer depends on abstractions (interfaces) rather than concrete implementations.
3. **Use Case Isolation**: Each use case should be isolated from others.
4. **Input Validation**: Validate inputs before processing.
5. **Error Handling**: Handle errors gracefully and return appropriate responses.

### Phase 4: Infrastructure Layer Implementation

The infrastructure layer provides concrete implementations of the interfaces defined in the application layer. It's responsible for data persistence, external services, and other technical concerns.

#### 1. Update Database Schema

First, update the Convex schema to support the new data structures required by the feature.

```typescript
// convex/schema.ts
import {defineSchema, defineTable} from "convex/server";
import {v} from "convex/values";

export default defineSchema({
  // Other tables...

  teamInvitations: defineTable({
    teamInvitationId: v.string(),
    teamId: v.string(),
    email: v.string(),
    status: v.string(),
    invitedBy: v.string(),
    createdAt: v.string()
  })
  .index("by_teamInvitationId", ["teamInvitationId"])
  .index("by_teamId", ["teamId"])
  .index("by_teamId_and_email", ["teamId", "email"])
  .index("by_email", ["email"])
});
```

#### 2. Implement In-Memory Repository for Testing

Create an in-memory implementation of the repository interface for testing purposes.

```typescript
// src/server/infrastructure/repositories/TeamInvitationRepository/InMemoryTeamInvitationRepository.ts
import {TeamInvitationRepository} from "@/src/server/application/ports/TeamInvitationRepository";
import {TeamInvitation} from "@/src/server/domain/TeamInvitation/TeamInvitation";
import {Result} from "@evyweb/simple-ddd-toolkit";
import {TeamInvitationNotFoundError} from "@/src/server/domain/TeamInvitation/errors/TeamInvitationNotFoundError";

export class InMemoryTeamInvitationRepository implements TeamInvitationRepository {
    private invitations: TeamInvitation[] = [];

    async findByEmailAndTeamId(email: string, teamId: string): Promise<Result<TeamInvitation, TeamInvitationNotFoundError>> {
        const invitation = this.invitations.find(i =>
            i.get('email') === email && i.get('teamId') === teamId
        );

        if (!invitation) {
            return Result.fail(new TeamInvitationNotFoundError());
        }

        return Result.ok(invitation);
    }

    async save(invitation: TeamInvitation): Promise<void> {
        const index = this.invitations.findIndex(i =>
            i.get('id').get('value') === invitation.get('id').get('value')
        );

        if (index !== -1) {
            this.invitations[index] = invitation;
        } else {
            this.invitations.push(invitation);
        }
    }
}
```

#### 3. Implement Convex Repository

Implement the repository interface using Convex for data persistence.

```typescript
// src/server/infrastructure/repositories/TeamInvitationRepository/ConvexTeamInvitationRepository.ts
import {TeamInvitationRepository} from "@/src/server/application/ports/TeamInvitationRepository";
import {TeamInvitation} from "@/src/server/domain/TeamInvitation/TeamInvitation";
import {Result} from "@evyweb/simple-ddd-toolkit";
import {TeamInvitationNotFoundError} from "@/src/server/domain/TeamInvitation/errors/TeamInvitationNotFoundError";
import {TeamInvitationId} from "@/src/server/domain/TeamInvitation/valueObjects/TeamInvitationId";
import {GenericDatabaseWriter} from "convex/server";
import {DataModel} from "@/convex/_generated/dataModel";
import {getOneFrom} from "convex-helpers/server/relationships";

export class ConvexTeamInvitationRepository implements TeamInvitationRepository {
    private readonly db: GenericDatabaseWriter<DataModel>;

    constructor(db: GenericDatabaseWriter<DataModel>) {
        this.db = db;
    }

    async findByEmailAndTeamId(email: string, teamId: string): Promise<Result<TeamInvitation, TeamInvitationNotFoundError>> {
        const invitationDocument = await this.db
            .query("teamInvitations")
            .withIndex("by_teamId_and_email", q =>
                q.eq("teamId", teamId).eq("email", email)
            )
            .first();

        if (!invitationDocument) {
            return Result.fail(new TeamInvitationNotFoundError());
        }

        return Result.ok(TeamInvitation.create({
            id: TeamInvitationId.createFrom(invitationDocument.teamInvitationId).getValue(),
            teamId: invitationDocument.teamId,
            email: invitationDocument.email,
            status: invitationDocument.status as any,
            invitedBy: invitationDocument.invitedBy
        }).getValue());
    }

    async save(invitation: TeamInvitation): Promise<void> {
        const existingInvitation = await getOneFrom(
            this.db,
            "teamInvitations",
            "by_teamInvitationId",
            invitation.get('id').get('value')
        );

        if (existingInvitation === null) {
            const now = new Date().toISOString();
            await this.db.insert("teamInvitations", {
                teamInvitationId: invitation.get('id').get('value'),
                teamId: invitation.get('teamId'),
                email: invitation.get('email'),
                status: invitation.get('status'),
                invitedBy: invitation.get('invitedBy'),
                createdAt: now
            });
        } else {
            await this.db.patch(existingInvitation._id, {
                status: invitation.get('status')
            });
        }
    }
}
```

#### 4. Write Integration Tests for Repository

Create integration tests to ensure the repository correctly interacts with the database.

```typescript
// src/server/specs/integration/Repositories/TeamInvitationRepository/ConvexTeamInvitationRepository.spec.ts
import {TeamInvitation} from "@/src/server/domain/TeamInvitation/TeamInvitation";
import {TeamInvitationId} from "@/src/server/domain/TeamInvitation/valueObjects/TeamInvitationId";
import {TeamInvitationNotFoundError} from "@/src/server/domain/TeamInvitation/errors/TeamInvitationNotFoundError";
import {ConvexTeamInvitationRepository} from "@/src/server/infrastructure/repositories/TeamInvitationRepository/ConvexTeamInvitationRepository";

describe('ConvexTeamInvitationRepository', () => {
    const TODAY = new Date().toISOString();
    const TEAM_ID = '00000000-0000-0000-0000-000000000001';
    const EMAIL = '<EMAIL>';
    const JOHN = {
        craftUserId: '00000000-0000-0000-0000-000000000002'
    };
    const INVITATION_ID = TeamInvitationId.createFrom('00000000-0000-0000-0000-000000000003');

    let asJohn: any;
    let ctx: any;

    beforeEach(async () => {
        // Setup would be done here in a real test
    });

    describe('findByEmailAndTeamId', () => {
        it('should find the invitation by email and team ID when it exists', async () => {
            await asJohn.run(async ctx => {
                await ctx.db.insert('teamInvitations', {
                    teamInvitationId: INVITATION_ID.get('value'),
                    teamId: TEAM_ID,
                    email: EMAIL,
                    status: 'pending',
                    invitedBy: JOHN.craftUserId,
                    createdAt: TODAY
                });

                const repository = new ConvexTeamInvitationRepository(ctx.db);

                const result = await repository.findByEmailAndTeamId(EMAIL, TEAM_ID);

                expect(result.isOk()).toBe(true);
                const invitation = result.getValue();
                expect(invitation.get('id').get('value')).toBe(INVITATION_ID.get('value'));
                expect(invitation.get('teamId')).toBe(TEAM_ID);
                expect(invitation.get('email')).toBe(EMAIL);
            });
        });

        it('should indicate that no invitation exists for the given email and team', async () => {
            await asJohn.run(async ctx => {
                const repository = new ConvexTeamInvitationRepository(ctx.db);

                const result = await repository.findByEmailAndTeamId('<EMAIL>', TEAM_ID);

                expect(result.isFail()).toBe(true);
                expect(result.getError()).toBeInstanceOf(TeamInvitationNotFoundError);
            });
        });
    });

    describe('save', () => {
        it('should persist a new invitation to the database', async () => {
            await asJohn.run(async ctx => {
                const repository = new ConvexTeamInvitationRepository(ctx.db);
                const invitation = TeamInvitation.createPendingInvitation({
                    id: INVITATION_ID,
                    teamId: TEAM_ID,
                    email: EMAIL,
                    invitedBy: JOHN.craftUserId
                }).getValue();

                await repository.save(invitation);

                const savedInvitation = await ctx.db
                    .query('teamInvitations')
                    .withIndex('by_teamInvitationId', q => q.eq('teamInvitationId', INVITATION_ID.get('value')))
                    .first();

                expect(savedInvitation).not.toBeNull();
                expect(savedInvitation?.teamInvitationId).toBe(INVITATION_ID.get('value'));
                expect(savedInvitation?.teamId).toBe(TEAM_ID);
                expect(savedInvitation?.email).toBe(EMAIL);
                expect(savedInvitation?.status).toBe('pending');
                expect(savedInvitation?.invitedBy).toBe(JOHN.craftUserId);
                expect(savedInvitation?.createdAt).toBeDefined();
            });
        });

        it('should update the status of an existing invitation', async () => {
            await asJohn.run(async ctx => {
                await ctx.db.insert('teamInvitations', {
                    teamInvitationId: INVITATION_ID.get('value'),
                    teamId: TEAM_ID,
                    email: EMAIL,
                    status: 'pending',
                    invitedBy: JOHN.craftUserId,
                    createdAt: TODAY
                });

                const repository = new ConvexTeamInvitationRepository(ctx.db);
                const invitation = TeamInvitation.create({
                    id: INVITATION_ID,
                    teamId: TEAM_ID,
                    email: EMAIL,
                    status: 'accepted',
                    invitedBy: JOHN.craftUserId,
                }).getValue();

                await repository.save(invitation);

                const updatedInvitation = await ctx.db
                    .query('teamInvitations')
                    .withIndex('by_teamInvitationId', q => q.eq('teamInvitationId', INVITATION_ID.get('value')))
                    .first();

                expect(updatedInvitation).not.toBeNull();
                expect(updatedInvitation?.status).toBe('accepted');
            });
        });
    });
});
```

#### Key Infrastructure Layer Principles

1. **Dependency Inversion**: The infrastructure layer implements interfaces defined in the application layer.
2. **Persistence Ignorance**: The domain model should be persistence-ignorant, meaning it doesn't know or care how it's persisted.
3. **Testability**: The infrastructure layer should be designed for testability, with clear separation of concerns.
4. **Adapters**: The infrastructure layer adapts external systems to the application's needs.
5. **Transactions**: The infrastructure layer should handle transactions and ensure data consistency.

### Phase 5: API Layer Implementation

The API layer provides endpoints that the frontend can call to interact with the backend. In this project, we use Convex for the API layer.

#### 1. Create Mutation Endpoint for Command

Create a mutation endpoint that will handle the like/unlike command from the frontend. Mutation endpoints typically delegate to command handlers in the application layer.

```typescript
// convex/mutations/sendTeamInvitation.ts
import {mutation} from "@/convex/_generated/server";
import {v} from "convex/values";
import {ConvexAuthenticationGateway} from "@/src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway";
import {ConvexTeamRepository} from "@/src/server/infrastructure/repositories/TeamRepository/ConvexTeamRepository";
import {ConvexTeamInvitationRepository} from "@/src/server/infrastructure/repositories/TeamInvitationRepository/ConvexTeamInvitationRepository";
import {UuidIdentityProvider} from "@/src/server/infrastructure/providers/IdentityProvider/UuidIdentityProvider";
import {SendTeamInvitationCommandHandler} from "@/src/server/application/usecases/SendTeamInvitation/SendTeamInvitationCommandHandler";
import {SendTeamInvitationCommand} from "@/src/server/application/usecases/SendTeamInvitation/SendTeamInvitationCommand";
import {TeamInvitationService} from "@/src/server/domain/services/TeamInvitationService";

export const endpoint = mutation({
    args: {
        teamId: v.string(),
        email: v.string(),
    },
    handler: async (ctx, {teamId, email}) => {
        const authenticationGateway = new ConvexAuthenticationGateway(ctx);
        const user = await authenticationGateway.getAuthenticatedUser();

        const teamRepository = new ConvexTeamRepository(ctx.db);
        const teamInvitationRepository = new ConvexTeamInvitationRepository(ctx.db);

        const teamInvitationService = new TeamInvitationService(
            teamRepository,
            teamInvitationRepository
        );

        const identityProvider = new UuidIdentityProvider(crypto);

        const sendTeamInvitationCommandHandler = new SendTeamInvitationCommandHandler(
            teamInvitationService,
            teamInvitationRepository,
            identityProvider
        );

        const command = new SendTeamInvitationCommand(user.id, teamId, email);
        const result = await sendTeamInvitationCommandHandler.handle(command);

        if (result.isFail()) {
            throw result.getError();
        }

        return result.getValue();
    }
});
```

#### 3. Write Integration Tests for API Endpoints

Create integration tests to ensure the API endpoints work correctly.

```typescript
// src/server/specs/integration/Api/mutations/sendTeamInvitation.spec.ts
import {ConvexTestCtx} from "@/src/server/specs/helpers/ConvexTestCtx";
import {JOHN, JANE} from "@/src/server/specs/helpers/fakeUserDtos";
import {TeamInvitationAlreadyExistsError} from "@/src/server/domain/TeamInvitation/errors/TeamInvitationAlreadyExistsError";
import {NotTeamOwnerError} from "@/src/server/domain/Team/errors/NotTeamOwnerError";

describe('sendTeamInvitation mutation', () => {
    const asJohn = new ConvexTestCtx(JOHN);
    const asJane = new ConvexTestCtx(JANE);

    beforeEach(async () => {
        // Setup would be done here in a real test
    });

    describe('When the team owner sends an invitation', () => {
        it('should create a new invitation for the recipient', async () => {
            const teamId = '00000000-0000-0000-0000-000000000001';
            const email = '<EMAIL>';

            await asJohn.run(async ctx => {
                await ctx.db.insert('teams', {
                    teamId,
                    name: 'Test Team',
                    ownerId: JOHN.craftUserId,
                    createdAt: new Date().toISOString()
                });

                const invitationId = await ctx.mutation('sendTeamInvitation', {
                    teamId,
                    email
                });

                expect(typeof invitationId).toBe('string');

                const invitation = await ctx.db
                    .query('teamInvitations')
                    .withIndex('by_teamId_and_email', q => q.eq('teamId', teamId).eq('email', email))
                    .first();

                expect(invitation).not.toBeNull();
                expect(invitation?.teamInvitationId).toBe(invitationId);
                expect(invitation?.teamId).toBe(teamId);
                expect(invitation?.email).toBe(email);
                expect(invitation?.status).toBe('pending');
                expect(invitation?.invitedBy).toBe(JOHN.craftUserId);
            });
        });
    });

    describe('When a non-owner tries to send an invitation', () => {
        it('should reject the request as only team owners can send invitations', async () => {
            const teamId = '00000000-0000-0000-0000-000000000001';
            const email = '<EMAIL>';

            await asJohn.run(async ctx => {
                await ctx.db.insert('teams', {
                    teamId,
                    name: 'Test Team',
                    ownerId: JOHN.craftUserId,
                    createdAt: new Date().toISOString()
                });
            });

            await expect(asJane.mutation('sendTeamInvitation', {
                teamId,
                email
            })).rejects.toThrow(NotTeamOwnerError);
        });
    });

    describe('When trying to send an invitation to an email that already has a pending invitation', () => {
        it('should reject the duplicate invitation as one is already pending', async () => {
            const teamId = '00000000-0000-0000-0000-000000000001';
            const email = '<EMAIL>';

            await asJohn.run(async ctx => {
                await ctx.db.insert('teams', {
                    teamId,
                    name: 'Test Team',
                    ownerId: JOHN.craftUserId,
                    createdAt: new Date().toISOString()
                });

                await ctx.db.insert('teamInvitations', {
                    teamInvitationId: '00000000-0000-0000-0000-000000000010',
                    teamId,
                    email,
                    status: 'pending',
                    invitedBy: JOHN.craftUserId,
                    createdAt: new Date().toISOString()
                });

                await expect(ctx.mutation('sendTeamInvitation', {
                    teamId,
                    email
                })).rejects.toThrow(TeamInvitationAlreadyExistsError);
            });
        });
    });
});
```

#### Key API Layer Principles

1. **Thin Controllers**: API endpoints should be thin wrappers around use cases, delegating most of the work to command and query handlers.
2. **Input Validation**: Validate inputs at the API layer to prevent invalid data from reaching the application layer.
3. **Authentication and Authorization**: Handle authentication and authorization at the API layer.
4. **Error Handling**: Catch and handle errors appropriately, returning meaningful error messages to the client.
5. **Consistent Response Format**: Use a consistent format for API responses.

### Phase 6: Presentation Layer Implementation

The presentation layer is responsible for displaying data to the user and handling user interactions. In this project, we use React for the presentation layer.

#### 1. Create Feature Hooks

Feature hooks are React hooks that interact with the API endpoints and provide a clean interface for UI components.

**Command Hook**

```typescript
// src/client/presentation/hooks/features/commands/useSendTeamInvitationFeature.ts
import {useMutation} from "convex/react";
import {api} from "@/convex/_generated/api";
import {useState} from "react";
import {NotTeamOwnerError} from "@/src/server/domain/Team/errors/NotTeamOwnerError";
import {TeamInvitationAlreadyExistsError} from "@/src/server/domain/TeamInvitation/errors/TeamInvitationAlreadyExistsError";
import {TeamNotFoundError} from "@/src/server/domain/Team/errors/TeamNotFoundError";

export const useSendTeamInvitationFeature = () => {
    const sendTeamInvitation = useMutation(api.mutations.user.sendTeamInvitation.endpoint);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const send = async (teamId: string, email: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const invitationId = await sendTeamInvitation({ teamId, email });
            setIsLoading(false);
            return invitationId;
        } catch (e) {
            setIsLoading(false);

            if (e instanceof NotTeamOwnerError) {
                setError('You must be the team owner to send invitations');
            } else if (e instanceof TeamInvitationAlreadyExistsError) {
                setError('An invitation has already been sent to this email');
            } else if (e instanceof TeamNotFoundError) {
                setError('Team not found');
            } else {
                setError('An unexpected error occurred');
                console.error(e);
            }

            return null;
        }
    };

    return {
        send,
        isLoading,
        error
    };
};
```

#### 2. Update UI Components

Update the UI components to use the feature hooks and display the like information.

**Comment Component**

```typescript
// src/client/presentation/components/app/Teams/AddMemberDialog.tsx
import React, {useState} from "react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/src/client/presentation/components/ui/Dialog";
import {Button} from "@/src/client/presentation/components/ui/Button";
import {Input} from "@/src/client/presentation/components/ui/Input";
import {Label} from "@/src/client/presentation/components/ui/Label";
import {useSendTeamInvitationFeature} from "@/src/client/presentation/hooks/features/commands/useSendTeamInvitationFeature";
import {PlusIcon} from "lucide-react";
import {toast} from "sonner";

type Props = {
    teamId: string;
};

export function AddMemberDialog({teamId}: Props) {
    const [email, setEmail] = useState("");
    const [isOpen, setIsOpen] = useState(false);
    const {send, isLoading, error} = useSendTeamInvitationFeature();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!email) return;

        const invitationId = await send(teamId, email);

        if (invitationId) {
            toast.success("Invitation sent successfully");
            setEmail("");
            setIsOpen(false);
        }
    };

  return (
    <Box className="p-3 rounded-md shadow-sm bg-[#11111140]">
      <Flex direction="column" gap="2">
        <Flex gap="3" align="center">
          <Avatar
            src={author.avatar}
            fallback={author.avatarFallback}
            size="2"
          />
          <Box>
            <Text size="2" weight="bold">{author.name}</Text>
            <Text size="1" color="gray">
              {formatDistanceToNow(new Date(createdAt), {addSuffix: true})}
            </Text>
          </Box>
        </Flex>
        <Text>{content}</Text>
        <Flex gap="2" align="center">
          <ThumbsUp
            size={16}
            className={`cursor-pointer ${isLiked ? 'text-blue-500 fill-blue-500' : 'text-gray-500'}`}
            onClick={handleLikeClick}
          />
          {likeCount > 0 && (
            <Text size="1" color="gray">{likeCount}</Text>
          )}
        </Flex>
      </Flex>
    </Box>
  );
};

export default memo(Comment);
```

**Reply Component**

```typescript
// src/client/presentation/components/app/Comments/Reply/Reply.tsx
import React, {FC, memo} from "react";
import {Avatar, Box, Flex, Text} from "@radix-ui/themes";
import {formatDistanceToNow} from "date-fns";
import {ThumbsUp} from "lucide-react";
import {useLikeCommentFeature} from "@/src/client/presentation/hooks/features/commands/useLikeCommentFeature";
import {useCommentLikesFeature} from "@/src/client/presentation/hooks/features/queries/useCommentLikesFeature";

type Props = {
  id: string;
  author: {
    name: string;
    avatar: string;
    avatarFallback: string;
    isCurrentUser: boolean;
  };
  content: string;
  createdAt: string;
};

const Reply: FC<Props> = ({id, author, content, createdAt}) => {
  const {likeComment} = useLikeCommentFeature();
  const {isLiked, likeCount} = useCommentLikesFeature(id);

  const handleLikeClick = async () => {
    await likeComment(id);
  };

  return (
    <Box className="pl-8 mt-2">
      <Box className="p-3 rounded-md shadow-sm bg-[#11111130]">
        <Flex direction="column" gap="2">
          <Flex gap="3" align="center">
            <Avatar
              src={author.avatar}
              fallback={author.avatarFallback}
              size="1"
            />
            <Box>
              <Text size="2" weight="bold">{author.name}</Text>
              <Text size="1" color="gray">
                {formatDistanceToNow(new Date(createdAt), {addSuffix: true})}
              </Text>
            </Box>
          </Flex>
          <Text>{content}</Text>
          <Flex gap="2" align="center">
            <ThumbsUp
              size={14}
              className={`cursor-pointer ${isLiked ? 'text-blue-500 fill-blue-500' : 'text-gray-500'}`}
              onClick={handleLikeClick}
            />
            {likeCount > 0 && (
              <Text size="1" color="gray">{likeCount}</Text>
            )}
          </Flex>
        </Flex>
      </Box>
    </Box>
  );
};

export default memo(Reply);
```

#### 3. Implement Optimistic Updates

Optimistic updates improve the user experience by updating the UI immediately, without waiting for the server response.

```typescript
// src/client/presentation/hooks/features/commands/useLikeCommentFeature.ts (with optimistic updates)
import {useMutation} from "convex/react";
import {api} from "@/convex/_generated/api";

export const useLikeCommentFeature = () => {
  const likeCommentMutation = useMutation(api.mutations.user.likeComment.endpoint)
    .withOptimisticUpdate((localStore, {commentId}) => {
      const currentLikes = localStore.getQuery(api.queries.commentLikes.endpoint, {commentId});
      const currentUser = localStore.getQuery(api.queries.userProfile.endpoint);

      if (!currentLikes || !currentUser) return;

      const isCurrentlyLiked = currentLikes.isLiked;
      const newLikeCount = isCurrentlyLiked ? currentLikes.likeCount - 1 : currentLikes.likeCount + 1;

      localStore.setQuery(api.queries.commentLikes.endpoint, {commentId}, {
        isLiked: !isCurrentlyLiked,
        likeCount: newLikeCount
      });
    });

  return {
    likeComment: async (commentId: string) => {
      await likeCommentMutation({commentId});
    }
  };
};
```

#### 4. Note on UI Testing

UI components and custom hooks are not tested directly in this project at this time. The functionality is tested through the use case tests and integration tests.

#### Key Presentation Layer Principles

1. **Separation of Concerns**: Separate data fetching (hooks) from presentation (components).
2. **Reusability**: Create reusable components and hooks.
3. **Optimistic Updates**: Implement optimistic updates for a better user experience.
4. **Error Handling**: Handle errors gracefully and provide feedback to the user.
5. **Accessibility**: Ensure components are accessible to all users.

### Phase 7: Testing

Testing is a critical part of the development process. In this project, we follow a Test-Driven Development (TDD) approach, where tests are written before the implementation code.

#### 1. Unit Testing Strategy

Unit tests focus on testing individual components in isolation. They should be fast, reliable, and independent of external dependencies.

**Key Unit Testing Principles**

1. **Test Behavior, Not Implementation**: Focus on testing what the code does, not how it does it.
2. **Arrange-Act-Assert Pattern**: Structure tests with clear setup, action, and verification phases.
3. **One Assertion Per Test**: Each test should verify one specific behavior.
4. **Use Descriptive Test Names**: Test names should describe the behavior being tested.
5. **No Conditional Logic in Tests**: Tests should be straightforward and avoid complex logic.

#### 2. Integration Testing Strategy

Integration tests verify that different components work together correctly. They test the integration points between components.

**Key Integration Testing Principles**

1. **Test Real Interactions**: Test how components interact with each other in a realistic environment.
2. **Minimize Mocking**: Use real implementations where possible, mocking only external dependencies.
3. **Focus on Boundaries**: Test the boundaries between components, such as repositories and API endpoints.
4. **Test Error Handling**: Verify that components handle errors correctly.
5. **Test Edge Cases**: Test boundary conditions and edge cases.

#### 3. End-to-End Testing Strategy

End-to-end tests verify that the entire application works correctly from the user's perspective.

**Key End-to-End Testing Principles**

1. **Test User Flows**: Test complete user flows from start to finish.
2. **Use Real Dependencies**: Use real dependencies, including the database and external services.
3. **Minimize Number of Tests**: End-to-end tests are slower and more brittle, so focus on critical paths.
4. **Test in a Production-Like Environment**: Test in an environment that closely resembles production.
5. **Test Performance**: Verify that the application performs acceptably under load.

#### 4. Test Coverage

Aim for high test coverage, but focus on meaningful coverage rather than arbitrary metrics.

**Test Coverage Goals**

1. **Domain Layer**: 90-100% coverage
2. **Application Layer**: 80-90% coverage
3. **Infrastructure Layer**: 70-80% coverage
4. **Presentation Layer**: 60-70% coverage

#### 5. Testing Tools

Use appropriate testing tools for each type of test.

**Unit Testing Tools**

- Jest: For running tests and assertions
- Testing Library: For testing React components
- Mock Service Worker: For mocking API requests

**Integration Testing Tools**

- Convex Test: For testing Convex repositories and API endpoints
- Supertest: For testing HTTP endpoints

**End-to-End Testing Tools**

- Cypress: For testing the application from the user's perspective
- Playwright: For testing across different browsers

#### 6. Test Organization

Organize tests in a way that makes them easy to find and maintain.

**Test File Structure**

```
src/
  server/
    domain/
      Comment/
        Comment.ts
        Comment.spec.ts
    application/
      usecases/
        LikeComment/
          LikeCommentCommandHandler.ts
          LikeCommentCommandHandler.spec.ts
    infrastructure/
      repositories/
        CommentRepository/
          ConvexCommentRepository.ts
    specs/
      unit/
        LikeComment/
          LikeCommentSteps.ts
          LikeCommentCommandHandler.spec.ts
      integration/
        Repositories/
          ConvexCommentRepository.spec.ts
        Api/
          likeComment.spec.ts
  client/
    presentation/
      components/
        app/
          Comments/
            Comment/
              Comment.tsx
              Comment.spec.tsx
```

#### 7. Test Examples

**Unit Test Example**

```typescript
// src/server/domain/Comment/Comment.spec.ts
import {Comment} from "./Comment";
import {CommentId} from "./valueObjects/CommentId";

describe('Comment Entity', () => {
  describe('like method', () => {
    it('should add user to likedBy array when user has not liked the comment', () => {
      // Arrange
      const comment = Comment.create({
        id: CommentId.createFrom('comment-1').getValue(),
        feedbackId: 'feedback-1',
        content: 'Test comment',
        userId: 'user-1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'published',
        likedBy: []
      }).getValue();

      // Act
      comment.like('user-2');

      // Assert
      expect(comment.isLikedBy('user-2')).toBe(true);
      expect(comment.getLikeCount()).toBe(1);
    });
  });
});
```

**Integration Test Example**

```typescript
// src/server/specs/integration/Repositories/ConvexCommentRepository.spec.ts
import {Comment} from "@/src/server/domain/Comment/Comment";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel} from "@/convex/_generated/dataModel";
import schema from "@/convex/schema";
import {ConvexCommentRepository} from "@/src/server/infrastructure/repositories/CommentRepository/ConvexCommentRepository";
import {CommentId} from "@/src/server/domain/Comment/valueObjects/CommentId";

describe('ConvexCommentRepository', () => {
  let convex: TestConvexForDataModel<DataModel>;
  let repository: ConvexCommentRepository;

  beforeAll(async () => {
    convex = await convexTest(schema);
  });

  beforeEach(async () => {
    await convex.clearAll();
    repository = new ConvexCommentRepository(convex.db);
  });

  afterAll(async () => {
    await convex.clearAll();
    await convex.close();
  });

  describe('findById', () => {
    it('should return a comment when it exists', async () => {
      // Arrange
      const commentId = 'comment-1';
      await convex.db.insert('comments', {
        commentId,
        feedbackId: 'feedback-1',
        content: 'Test comment',
        userId: 'user-1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'published',
        likedBy: ['user-2']
      });

      // Act
      const result = await repository.findById(commentId);

      // Assert
      expect(result.isOk()).toBe(true);
      const comment = result.getValue();
      expect(comment.get('id').get('value')).toBe(commentId);
      expect(comment.isLikedBy('user-2')).toBe(true);
    });
  });
});
```

**Note on UI Testing**

UI components and custom hooks are not tested directly in this project at this time. The functionality is tested through the use case tests and integration tests.

## Feature Implementation Patterns

### Command Pattern

Commands follow this structure:
1. **Command Class**: Contains input parameters
   ```typescript
   export class LikeCommentCommand {
       constructor(
           public readonly commentId: string,
           public readonly userId: string
       ) {}
   }
   ```

2. **Command Handler**: Contains business logic
   ```typescript
   export class LikeCommentCommandHandler {
       constructor(private readonly commentRepository: CommentRepository) {}

       async handle(command: LikeCommentCommand): Promise<LikeCommentResponse> {
           // Business logic here
       }
   }
   ```

3. **API Endpoint**: Thin wrapper that delegates to the command handler
   ```typescript
   export const endpoint = mutation({
       args: {
           commentId: v.string(),
       },
       handler: async (ctx, {commentId}) => {
           // Authentication
           const authenticationGateway = new ConvexAuthenticationGateway(ctx);
           const user = await authenticationGateway.getAuthenticatedUser();

           // Create command
           const command = new LikeCommentCommand(commentId, user.id);

           // Create repository
           const commentRepository = new ConvexCommentRepository(ctx.db);

           // Create command handler
           const commandHandler = new LikeCommentCommandHandler(commentRepository);

           // Execute command
           const result = await commandHandler.handle(command);

           // Handle result
           if (result.isFail()) {
               throw result.getError();
           }

           return true;
       }
   });
   ```

### Query Pattern

Queries follow this structure:
1. **Query Class**: Contains input parameters
   ```typescript
   export class CommentLikesQuery {
       constructor(
           public readonly commentId: string,
           public readonly userId: string
       ) {}
   }
   ```

2. **Query Handler**: Contains business logic
   ```typescript
   export class CommentLikesQueryHandler {
       constructor(private readonly commentRepository: CommentRepository) {}

       async handle(query: CommentLikesQuery): Promise<CommentLikesViewModel> {
           // Business logic here
       }
   }
   ```

3. **API Endpoint**: Thin wrapper that delegates to the query handler
   ```typescript
   export const endpoint = query({
       args: {
           commentId: v.string(),
       },
       handler: async (ctx, {commentId}) => {
           // Authentication
           const authenticationGateway = new ConvexAuthenticationGateway(ctx);
           const user = await authenticationGateway.getAuthenticatedUser();

           // Create query
           const query = new CommentLikesQuery(commentId, user.id);

           // Create repository
           const commentRepository = new ConvexCommentRepository(ctx.db);

           // Create query handler
           const queryHandler = new CommentLikesQueryHandler(commentRepository);

           // Execute query
           return await queryHandler.handle(query);
       }
   });
   ```

### Feature Hook Pattern

Feature hooks follow this structure:
1. **Command Hook**: Wraps mutation endpoints
   ```typescript
   export const useLikeCommentFeature = () => {
       const likeComment = useMutation(api.mutations.user.likeComment.endpoint);

       return {
           likeComment: async (commentId: string) => {
               await likeComment({ commentId });
           }
       };
   };
   ```

2. **Query Hook**: Wraps query endpoints
   ```typescript
   export const useCommentLikesFeature = (commentId: string) => {
       const likes = useQuery(api.queries.commentLikes.endpoint, { commentId });

       return {
           isLiked: likes?.isLiked || false,
           likeCount: likes?.likeCount || 0
       };
   };
   ```

## Complete Feature Implementation Checklist

This checklist provides a comprehensive guide for implementing a new feature in the project, following the established patterns and principles.

### Phase 1: Planning and Requirements Analysis

- [ ] **Define User Stories**
  - [ ] Write user stories from the user's perspective
  - [ ] Define acceptance criteria for each user story

- [ ] **Analyze Domain Concepts**
  - [ ] Identify domain entities and their relationships
  - [ ] Identify business rules and constraints
  - [ ] Create a domain model diagram if needed

- [ ] **Design Technical Solution**
  - [ ] Identify changes needed to domain layer
  - [ ] Identify changes needed to application layer
  - [ ] Identify changes needed to infrastructure layer
  - [ ] Identify changes needed to presentation layer
  - [ ] Design database schema changes

### Phase 2: Domain Layer Implementation

- [ ] **Design and Implement Domain Entities**
  - [ ] Create or update entity classes
  - [ ] Implement entity methods
  - [ ] Implement validation rules

- [ ] **Design and Implement Value Objects**
  - [ ] Create value object classes
  - [ ] Implement validation logic

- [ ] **Create Domain-Specific Errors**
  - [ ] Define error classes for domain-specific errors

### Phase 3: Application Layer Implementation

- [ ] **Define Repository Interfaces**
  - [ ] Define methods needed for the feature

- [ ] **Write Tests for Command Handlers**
  - [ ] Write tests for successful command execution
  - [ ] Write tests for error cases
  - [ ] Write tests for edge cases

- [ ] **Implement Command Types and Handlers**
  - [ ] Create command classes
  - [ ] Create command response types
  - [ ] Implement command handlers

- [ ] **Implement Queries**
  - [ ] Implement all queries directly in the Convex API layer (required approach)
  - [ ] Write tests for query endpoints

### Phase 4: Infrastructure Layer Implementation

- [ ] **Update Database Schema**
  - [ ] Add or modify tables
  - [ ] Add or modify indexes
  - [ ] Add or modify constraints

- [ ] **Write Tests for Repository Implementations**
  - [ ] Write tests for finding entities
  - [ ] Write tests for saving entities
  - [ ] Write tests for error cases

- [ ] **Implement Repository Classes**
  - [ ] Create in-memory implementation for testing
  - [ ] Create Convex implementation for production

### Phase 5: API Layer Implementation

- [ ] **Write Tests for API Endpoints**
  - [ ] Write tests for mutation endpoints
  - [ ] Write tests for query endpoints
  - [ ] Write tests for error cases

- [ ] **Implement API Endpoints**
  - [ ] Create mutation endpoints that delegate to command handlers
  - [ ] Create query endpoints directly in the API layer (required approach)
  - [ ] Implement authentication and authorization
  - [ ] Implement error handling

### Phase 6: Presentation Layer Implementation

- [ ] **Implement Feature Hooks**
  - [ ] Create command hooks
  - [ ] Create query hooks
  - [ ] Implement optimistic updates

- [ ] **Implement UI Components**
  - [ ] Create or update components
  - [ ] Implement user interactions
  - [ ] Implement error handling and loading states

### Phase 7: Testing and Finalization

- [ ] **Run All Tests**
  - [ ] Run use case tests
  - [ ] Run integration tests

- [ ] **Manual Testing**
  - [ ] Test the feature manually
  - [ ] Test edge cases
  - [ ] Test error scenarios

- [ ] **Code Review**
  - [ ] Review code for adherence to project standards
  - [ ] Review code for performance issues
  - [ ] Review code for security issues

- [ ] **Documentation**
  - [ ] Update API documentation
  - [ ] Update user documentation
  - [ ] Add comments to complex code

## Project Coding Conventions

This project follows specific coding conventions that should be adhered to when implementing new features:

### General Conventions

1. **UUIDs for Identifiers**: All identifiers in the project are UUIDs, not simple strings.

```typescript
// Good - Using UUIDs
const commentId = '550e8400-e29b-41d4-a716-************';
const userId = '7c9e6679-7425-40de-944b-e07fc1f90ae7';

// Bad - Using simple strings
const commentId = 'comment-1';
const userId = 'user-1';
```

### Repository Conventions

1. **Simple Repositories**: Repositories should remain very simple and focused only on retrieving and saving aggregates. They should not contain business logic.

```typescript
// Good - Simple repository method
async findById(id: string): Promise<Result<Comment, CommentNotFoundError>> {
  const comment = await getOneFrom(this.db, "comments", "by_commentId", id);

  if (!comment) {
    return Result.fail(new CommentNotFoundError());
  }

  return Result.ok(Comment.fromRaw({
    id: CommentId.createFrom(comment.commentId).getValue(),
    feedbackId: comment.feedbackId,
    // Other properties...
  }));
}
```

2. **Result Pattern**: Repositories must use the Result pattern for retrieving entities.

```typescript
// Good - Using Result pattern
async findById(id: string): Promise<Result<Comment, CommentNotFoundError>> {
  // Implementation...
}

// Bad - Not using Result pattern
async findById(id: string): Promise<Comment | null> {
  // Implementation...
}
```

### Result Pattern Conventions

1. **Void Results**: When returning a void Result, use `Result.ok(void 0)` instead of `Result.ok()`.

```typescript
// Good
return Result.ok(void 0);

// Bad
return Result.ok();
```

### Optional Values Conventions

1. **Avoid Optionals**: Avoid using optional properties with default empty arrays or other default values.

```typescript
// Good
type CommentProperties = {
  id: CommentId;
  feedbackId: string;
  likedBy: string[];
};

// Bad
type CommentProperties = {
  id: CommentId;
  feedbackId: string;
  likedBy?: string[]; // Default to []
};
```

### Testing Conventions

1. **No Conditional Logic in Tests**: Tests and test steps should not contain conditional logic (if statements).

```typescript
// Good - No conditional logic
async thenCommentShouldBeLikedByUser(commentId: string, userId: string): Promise<void> {
  expect(this.result?.isOk()).toBe(true);

  const commentResult = await this.repository.findById(commentId);
  expect(commentResult.isOk()).toBe(true);

  const comment = commentResult.getValue();
  expect(comment.isLikedBy(userId)).toBe(true);
}

// Bad - Contains conditional logic
async thenCommentShouldBeLikedByUser(commentId: string, userId: string): Promise<void> {
  if (this.result?.isOk()) {
    const commentResult = await this.repository.findById(commentId);
    if (commentResult.isOk()) {
      const comment = commentResult.getValue();
      expect(comment.isLikedBy(userId)).toBe(true);
    }
  }
}
```

2. **Given/When/Then in Command Handler Tests**: Command handler tests use the Given/When/Then pattern in their naming, so they don't need explicit Arrange/Act/Assert comments.

```typescript
// Good - Using Given/When/Then in method names
describe('Feature: Liking a Comment', () => {
  it('should like the comment when the user has not liked it before', async () => {
    await _.givenACommentExists(commentId);
    await _.whenUserLikesComment(commentId, userId);
    await _.thenCommentShouldBeLikedByUser(commentId, userId);
  });
});

// Bad - Using Arrange/Act/Assert comments with Given/When/Then methods
describe('Feature: Liking a Comment', () => {
  it('should like the comment when the user has not liked it before', async () => {
    // Arrange
    await _.givenACommentExists(commentId);

    // Act
    await _.whenUserLikesComment(commentId, userId);

    // Assert
    await _.thenCommentShouldBeLikedByUser(commentId, userId);
  });
});
```

3. **Separate When and Then Steps**: Always separate the "when" and "then" steps in your tests. Don't combine them into a single method.

```typescript
// Good - Separate when and then steps
async whenUserLikesNonExistentComment(commentId: string, userId: string): Promise<void> {
  const command = new LikeCommentCommand(commentId, userId);
  this.result = await this.handler.handle(command);
}

async thenShouldReturnCommentNotFoundError(): Promise<void> {
  expect(this.result?.isFail()).toBe(true);
  expect(this.result?.getError()).toBeInstanceOf(CommentNotFoundError);
}

// Bad - Combined when and then steps
async whenUserLikesNonExistentCommentThenShouldFail(commentId: string, userId: string): Promise<void> {
  const command = new LikeCommentCommand(commentId, userId);
  this.result = await this.handler.handle(command);
  expect(this.result.isFail()).toBe(true);
  expect(this.result.getError()).toBeInstanceOf(CommentNotFoundError);
}
```

## Conclusion

Implementing a feature in this project requires a systematic approach that follows the established architecture, patterns, and coding conventions. By following the process outlined in this guide, you can ensure that your implementation is consistent, maintainable, and aligned with the project's principles.

### Key Takeaways

1. **Follow the Clean Architecture**: Respect the separation of concerns and dependencies between layers.
2. **Use Domain-Driven Design**: Model the domain accurately and encapsulate business logic in the domain layer.
3. **Apply Test-Driven Development**: Write tests before implementation to ensure correctness and maintainability.
4. **Use Command and Query Responsibility Segregation (CQRS)**: Separate commands (write operations) from queries (read operations). Commands are implemented in the application layer, while queries can be implemented directly in the API layer or in the application layer for complex cases.
5. **Create Thin API Endpoints**: Keep API endpoints thin and delegate to command and query handlers.
6. **Implement Feature Hooks**: Create React hooks that provide a clean interface for UI components.
7. **Write Comprehensive Tests**: Test all layers of the application to ensure correctness and prevent regressions.

### Best Practices

1. **Start with the Domain Layer**: Begin implementation from the domain layer and work outward.
2. **Use Value Objects**: Use value objects for complex properties to encapsulate validation and behavior.
3. **Keep Entities Focused**: Entities should focus on their core responsibilities and not take on too many concerns.
4. **Use Repository Pattern**: Use repositories to abstract data access and make it testable.
5. **Implement Optimistic Updates**: Use optimistic updates to improve the user experience.
6. **Handle Errors Gracefully**: Provide meaningful error messages and handle errors at the appropriate layer.
7. **Follow Naming Conventions**: Use consistent naming conventions throughout the codebase.
