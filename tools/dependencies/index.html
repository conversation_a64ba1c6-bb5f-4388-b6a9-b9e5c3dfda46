<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <title>dependency graph</title>
    <style>
      .node:active path,
.node:hover path,
.node.current path,
.node:active polygon,
.node:hover polygon,
.node.current polygon {
  stroke: fuchsia;
  stroke-width: 2;
}

.edge:active path,
.edge:hover path,
.edge.current path,
.edge:active ellipse,
.edge:hover ellipse,
.edge.current ellipse {
  stroke: url(#edgeGradient);
  stroke-width: 3;
  stroke-opacity: 1;
}

.edge:active polygon,
.edge:hover polygon,
.edge.current polygon {
  stroke: fuchsia;
  stroke-width: 3;
  fill: fuchsia;
  stroke-opacity: 1;
  fill-opacity: 1;
}

.edge:active text,
.edge:hover text {
  fill: fuchsia;
}

.cluster path {
  stroke-width: 3;
}

.cluster:active path,
.cluster:hover path {
  fill: #ffff0011;
}

div.hint {
  background-color: #000000aa;
  color: white;
  font-family: Arial, Helvetica, sans-serif;
  border-radius: 1rem;
  position: fixed;
  top: calc(50% - 4em);
  right: calc(50% - 10em);
  border: none;
  padding: 1em 3em 1em 1em;
}

.hint button {
  position: absolute;
  font-weight: bolder;
  right: 0.6em;
  top: 0.6em;
  color: inherit;
  background-color: inherit;
  border: 1px solid currentColor;
  border-radius: 1em;
  margin-left: 0.6em;
}

.hint a {
  color: inherit;
}

#button_help {
  color: white;
  background-color: #00000011;
  border-radius: 1em;
  position: fixed;
  top: 1em;
  right: 1em;
  font-size: 24pt;
  font-weight: bolder;
  width: 2em;
  height: 2em;
  border: none;
}

#button_help:hover {
  cursor: pointer;
  background-color: #00000077;
}

@media print {
  #button_help {
    display: none;
  }

  div.hint {
    display: none;
  }
}

    </style>
  </head>
  <body>
    <button id="button_help">?</button>
    <div id="hints" class="hint" style="display: none">
      <button id="close-hints">x</button>
      <span id="hint-text"></span>
      <ul>
        <li><b>Hover</b> - highlight</li>
        <li><b>Right-click</b> - pin highlight</li>
        <li><b>ESC</b> - clear</li>
      </ul>
    </div>
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.0 (20241103.1931)
 -->
<!-- Title: dependency&#45;cruiser output Pages: 1 -->
<svg width="2230pt" height="7974pt"
 viewBox="0.00 0.00 2229.75 7974.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 7970)">
<title>dependency&#45;cruiser output</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-7970 2225.75,-7970 2225.75,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M20,-7132C20,-7132 310.38,-7132 310.38,-7132 316.38,-7132 322.38,-7138 322.38,-7144 322.38,-7144 322.38,-7946 322.38,-7946 322.38,-7952 316.38,-7958 310.38,-7958 310.38,-7958 20,-7958 20,-7958 14,-7958 8,-7952 8,-7946 8,-7946 8,-7144 8,-7144 8,-7138 14,-7132 20,-7132"/>
<text text-anchor="middle" x="165.19" y="-7945.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust2" class="cluster">
<title>cluster_app/[locale]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M28,-7197C28,-7197 160.25,-7197 160.25,-7197 166.25,-7197 172.25,-7203 172.25,-7209 172.25,-7209 172.25,-7919 172.25,-7919 172.25,-7925 166.25,-7931 160.25,-7931 160.25,-7931 28,-7931 28,-7931 22,-7931 16,-7925 16,-7919 16,-7919 16,-7209 16,-7209 16,-7203 22,-7197 28,-7197"/>
<text text-anchor="middle" x="94.12" y="-7918.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[locale]</text>
</g>
<g id="clust3" class="cluster">
<title>cluster_app/[locale]/(connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M36,-7414C36,-7414 152.25,-7414 152.25,-7414 158.25,-7414 164.25,-7420 164.25,-7426 164.25,-7426 164.25,-7892 164.25,-7892 164.25,-7898 158.25,-7904 152.25,-7904 152.25,-7904 36,-7904 36,-7904 30,-7904 24,-7898 24,-7892 24,-7892 24,-7426 24,-7426 24,-7420 30,-7414 36,-7414"/>
<text text-anchor="middle" x="94.12" y="-7891.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(connected)</text>
</g>
<g id="clust4" class="cluster">
<title>cluster_app/[locale]/(connected)/games</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M44,-7518C44,-7518 144.25,-7518 144.25,-7518 150.25,-7518 156.25,-7524 156.25,-7530 156.25,-7530 156.25,-7865 156.25,-7865 156.25,-7871 150.25,-7877 144.25,-7877 144.25,-7877 44,-7877 44,-7877 38,-7877 32,-7871 32,-7865 32,-7865 32,-7530 32,-7530 32,-7524 38,-7518 44,-7518"/>
<text text-anchor="middle" x="94.12" y="-7864.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">games</text>
</g>
<g id="clust5" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M52,-7552C52,-7552 136.25,-7552 136.25,-7552 142.25,-7552 148.25,-7558 148.25,-7564 148.25,-7564 148.25,-7838 148.25,-7838 148.25,-7844 142.25,-7850 136.25,-7850 136.25,-7850 52,-7850 52,-7850 46,-7850 40,-7844 40,-7838 40,-7838 40,-7564 40,-7564 40,-7558 46,-7552 52,-7552"/>
<text text-anchor="middle" x="94.12" y="-7837.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[gameId]</text>
</g>
<g id="clust6" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M60,-7647C60,-7647 128.25,-7647 128.25,-7647 134.25,-7647 140.25,-7653 140.25,-7659 140.25,-7659 140.25,-7811 140.25,-7811 140.25,-7817 134.25,-7823 128.25,-7823 128.25,-7823 60,-7823 60,-7823 54,-7823 48,-7817 48,-7811 48,-7811 48,-7659 48,-7659 48,-7653 54,-7647 60,-7647"/>
<text text-anchor="middle" x="94.12" y="-7810.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">deck&#45;builder</text>
</g>
<g id="clust7" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M68,-7712C68,-7712 120.25,-7712 120.25,-7712 126.25,-7712 132.25,-7718 132.25,-7724 132.25,-7724 132.25,-7784 132.25,-7784 132.25,-7790 126.25,-7796 120.25,-7796 120.25,-7796 68,-7796 68,-7796 62,-7796 56,-7790 56,-7784 56,-7784 56,-7724 56,-7724 56,-7718 62,-7712 68,-7712"/>
<text text-anchor="middle" x="94.12" y="-7783.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[deckId]</text>
</g>
<g id="clust8" class="cluster">
<title>cluster_app/[locale]/(connected)/games/[gameId]/play</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-7560C71.12,-7560 117.12,-7560 117.12,-7560 123.12,-7560 129.12,-7566 129.12,-7572 129.12,-7572 129.12,-7601 129.12,-7601 129.12,-7607 123.12,-7613 117.12,-7613 117.12,-7613 71.12,-7613 71.12,-7613 65.12,-7613 59.12,-7607 59.12,-7601 59.12,-7601 59.12,-7572 59.12,-7572 59.12,-7566 65.12,-7560 71.12,-7560"/>
<text text-anchor="middle" x="94.12" y="-7600.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">play</text>
</g>
<g id="clust9" class="cluster">
<title>cluster_app/[locale]/(connected)/matches</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M63.12,-7422C63.12,-7422 125.12,-7422 125.12,-7422 131.12,-7422 137.12,-7428 137.12,-7434 137.12,-7434 137.12,-7498 137.12,-7498 137.12,-7504 131.12,-7510 125.12,-7510 125.12,-7510 63.12,-7510 63.12,-7510 57.12,-7510 51.12,-7504 51.12,-7498 51.12,-7498 51.12,-7434 51.12,-7434 51.12,-7428 57.12,-7422 63.12,-7422"/>
<text text-anchor="middle" x="94.12" y="-7497.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">matches</text>
</g>
<g id="clust10" class="cluster">
<title>cluster_app/[locale]/(connected)/matches/[matchId]</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-7430C71.12,-7430 117.12,-7430 117.12,-7430 123.12,-7430 129.12,-7436 129.12,-7442 129.12,-7442 129.12,-7471 129.12,-7471 129.12,-7477 123.12,-7483 117.12,-7483 117.12,-7483 71.12,-7483 71.12,-7483 65.12,-7483 59.12,-7477 59.12,-7471 59.12,-7471 59.12,-7442 59.12,-7442 59.12,-7436 65.12,-7430 71.12,-7430"/>
<text text-anchor="middle" x="94.12" y="-7470.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">[matchId]</text>
</g>
<g id="clust11" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M63.12,-7231C63.12,-7231 125.12,-7231 125.12,-7231 131.12,-7231 137.12,-7237 137.12,-7243 137.12,-7243 137.12,-7333 137.12,-7333 137.12,-7339 131.12,-7345 125.12,-7345 125.12,-7345 63.12,-7345 63.12,-7345 57.12,-7345 51.12,-7339 51.12,-7333 51.12,-7333 51.12,-7243 51.12,-7243 51.12,-7237 57.12,-7231 63.12,-7231"/>
<text text-anchor="middle" x="94.12" y="-7332.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">(not&#45;connected)</text>
</g>
<g id="clust12" class="cluster">
<title>cluster_app/[locale]/(not&#45;connected)/signin</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M71.12,-7265C71.12,-7265 117.12,-7265 117.12,-7265 123.12,-7265 129.12,-7271 129.12,-7277 129.12,-7277 129.12,-7306 129.12,-7306 129.12,-7312 123.12,-7318 117.12,-7318 117.12,-7318 71.12,-7318 71.12,-7318 65.12,-7318 59.12,-7312 59.12,-7306 59.12,-7306 59.12,-7277 59.12,-7277 59.12,-7271 65.12,-7265 71.12,-7265"/>
<text text-anchor="middle" x="94.12" y="-7305.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">signin</text>
</g>
<g id="clust13" class="cluster">
<title>cluster_app/[locale]/access&#45;denied</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M67.12,-7353C67.12,-7353 122.12,-7353 122.12,-7353 128.12,-7353 134.12,-7359 134.12,-7365 134.12,-7365 134.12,-7394 134.12,-7394 134.12,-7400 128.12,-7406 122.12,-7406 122.12,-7406 67.12,-7406 67.12,-7406 61.12,-7406 55.12,-7400 55.12,-7394 55.12,-7394 55.12,-7365 55.12,-7365 55.12,-7359 61.12,-7353 67.12,-7353"/>
<text text-anchor="middle" x="94.62" y="-7393.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">access&#45;denied</text>
</g>
<g id="clust14" class="cluster">
<title>cluster_src</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M192.25,-8C192.25,-8 2201.75,-8 2201.75,-8 2207.75,-8 2213.75,-14 2213.75,-20 2213.75,-20 2213.75,-7112 2213.75,-7112 2213.75,-7118 2207.75,-7124 2201.75,-7124 2201.75,-7124 192.25,-7124 192.25,-7124 186.25,-7124 180.25,-7118 180.25,-7112 180.25,-7112 180.25,-20 180.25,-20 180.25,-14 186.25,-8 192.25,-8"/>
<text text-anchor="middle" x="1197" y="-7111.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">src</text>
</g>
<g id="clust15" class="cluster">
<title>cluster_src/client</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M200.25,-16C200.25,-16 2193.75,-16 2193.75,-16 2199.75,-16 2205.75,-22 2205.75,-28 2205.75,-28 2205.75,-5255 2205.75,-5255 2205.75,-5261 2199.75,-5267 2193.75,-5267 2193.75,-5267 200.25,-5267 200.25,-5267 194.25,-5267 188.25,-5261 188.25,-5255 188.25,-5255 188.25,-28 188.25,-28 188.25,-22 194.25,-16 200.25,-16"/>
<text text-anchor="middle" x="1197" y="-5254.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">client</text>
</g>
<g id="clust16" class="cluster">
<title>cluster_src/client/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M935.75,-510C935.75,-510 1739.5,-510 1739.5,-510 1745.5,-510 1751.5,-516 1751.5,-522 1751.5,-522 1751.5,-2164 1751.5,-2164 1751.5,-2170 1745.5,-2176 1739.5,-2176 1739.5,-2176 935.75,-2176 935.75,-2176 929.75,-2176 923.75,-2170 923.75,-2164 923.75,-2164 923.75,-522 923.75,-522 923.75,-516 929.75,-510 935.75,-510"/>
<text text-anchor="middle" x="1337.62" y="-2163.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust17" class="cluster">
<title>cluster_src/client/application/commands</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1117.75,-518C1117.75,-518 1462.5,-518 1462.5,-518 1468.5,-518 1474.5,-524 1474.5,-530 1474.5,-530 1474.5,-1326 1474.5,-1326 1474.5,-1332 1468.5,-1338 1462.5,-1338 1462.5,-1338 1117.75,-1338 1117.75,-1338 1111.75,-1338 1105.75,-1332 1105.75,-1326 1105.75,-1326 1105.75,-530 1105.75,-530 1105.75,-524 1111.75,-518 1117.75,-518"/>
<text text-anchor="middle" x="1290.12" y="-1325.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">commands</text>
</g>
<g id="clust18" class="cluster">
<title>cluster_src/client/application/commands/addCardToDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1162.5,-709C1162.5,-709 1439.88,-709 1439.88,-709 1445.88,-709 1451.88,-715 1451.88,-721 1451.88,-721 1451.88,-750 1451.88,-750 1451.88,-756 1445.88,-762 1439.88,-762 1439.88,-762 1162.5,-762 1162.5,-762 1156.5,-762 1150.5,-756 1150.5,-750 1150.5,-750 1150.5,-721 1150.5,-721 1150.5,-715 1156.5,-709 1162.5,-709"/>
<text text-anchor="middle" x="1301.19" y="-749.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">addCardToDeck</text>
</g>
<g id="clust19" class="cluster">
<title>cluster_src/client/application/commands/clearDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1165.5,-1136C1165.5,-1136 1243.25,-1136 1243.25,-1136 1249.25,-1136 1255.25,-1142 1255.25,-1148 1255.25,-1148 1255.25,-1177 1255.25,-1177 1255.25,-1183 1249.25,-1189 1243.25,-1189 1243.25,-1189 1165.5,-1189 1165.5,-1189 1159.5,-1189 1153.5,-1183 1153.5,-1177 1153.5,-1177 1153.5,-1148 1153.5,-1148 1153.5,-1142 1159.5,-1136 1165.5,-1136"/>
<text text-anchor="middle" x="1204.38" y="-1176.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">clearDeckDraft</text>
</g>
<g id="clust20" class="cluster">
<title>cluster_src/client/application/commands/filterCatalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1347.5,-1197C1347.5,-1197 1414,-1197 1414,-1197 1420,-1197 1426,-1203 1426,-1209 1426,-1209 1426,-1238 1426,-1238 1426,-1244 1420,-1250 1414,-1250 1414,-1250 1347.5,-1250 1347.5,-1250 1341.5,-1250 1335.5,-1244 1335.5,-1238 1335.5,-1238 1335.5,-1209 1335.5,-1209 1335.5,-1203 1341.5,-1197 1347.5,-1197"/>
<text text-anchor="middle" x="1380.75" y="-1237.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">filterCatalog</text>
</g>
<g id="clust21" class="cluster">
<title>cluster_src/client/application/commands/hideCardDetails</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1162.5,-526C1162.5,-526 1246.25,-526 1246.25,-526 1252.25,-526 1258.25,-532 1258.25,-538 1258.25,-538 1258.25,-567 1258.25,-567 1258.25,-573 1252.25,-579 1246.25,-579 1246.25,-579 1162.5,-579 1162.5,-579 1156.5,-579 1150.5,-573 1150.5,-567 1150.5,-567 1150.5,-538 1150.5,-538 1150.5,-532 1156.5,-526 1162.5,-526"/>
<text text-anchor="middle" x="1204.38" y="-566.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hideCardDetails</text>
</g>
<g id="clust22" class="cluster">
<title>cluster_src/client/application/commands/initializeDeckBuilderFromLocation</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1125.75,-1197C1125.75,-1197 1283,-1197 1283,-1197 1289,-1197 1295,-1203 1295,-1209 1295,-1209 1295,-1238 1295,-1238 1295,-1244 1289,-1250 1283,-1250 1283,-1250 1125.75,-1250 1125.75,-1250 1119.75,-1250 1113.75,-1244 1113.75,-1238 1113.75,-1238 1113.75,-1209 1113.75,-1209 1113.75,-1203 1119.75,-1197 1125.75,-1197"/>
<text text-anchor="middle" x="1204.38" y="-1237.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">initializeDeckBuilderFromLocation</text>
</g>
<g id="clust23" class="cluster">
<title>cluster_src/client/application/commands/loadCatalogCards</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1158.38,-770C1158.38,-770 1444,-770 1444,-770 1450,-770 1456,-776 1456,-782 1456,-782 1456,-811 1456,-811 1456,-817 1450,-823 1444,-823 1444,-823 1158.38,-823 1158.38,-823 1152.38,-823 1146.38,-817 1146.38,-811 1146.38,-811 1146.38,-782 1146.38,-782 1146.38,-776 1152.38,-770 1158.38,-770"/>
<text text-anchor="middle" x="1301.19" y="-810.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadCatalogCards</text>
</g>
<g id="clust24" class="cluster">
<title>cluster_src/client/application/commands/loadDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1166.62,-1014C1166.62,-1014 1242.12,-1014 1242.12,-1014 1248.12,-1014 1254.12,-1020 1254.12,-1026 1254.12,-1026 1254.12,-1055 1254.12,-1055 1254.12,-1061 1248.12,-1067 1242.12,-1067 1242.12,-1067 1166.62,-1067 1166.62,-1067 1160.62,-1067 1154.62,-1061 1154.62,-1055 1154.62,-1055 1154.62,-1026 1154.62,-1026 1154.62,-1020 1160.62,-1014 1166.62,-1014"/>
<text text-anchor="middle" x="1204.38" y="-1054.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadDeckDraft</text>
</g>
<g id="clust25" class="cluster">
<title>cluster_src/client/application/commands/loadDeckIntoBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1154.62,-1075C1154.62,-1075 1254.12,-1075 1254.12,-1075 1260.12,-1075 1266.12,-1081 1266.12,-1087 1266.12,-1087 1266.12,-1116 1266.12,-1116 1266.12,-1122 1260.12,-1128 1254.12,-1128 1254.12,-1128 1154.62,-1128 1154.62,-1128 1148.62,-1128 1142.62,-1122 1142.62,-1116 1142.62,-1116 1142.62,-1087 1142.62,-1087 1142.62,-1081 1148.62,-1075 1154.62,-1075"/>
<text text-anchor="middle" x="1204.38" y="-1115.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadDeckIntoBuilder</text>
</g>
<g id="clust26" class="cluster">
<title>cluster_src/client/application/commands/loadGameSettings</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1158,-831C1158,-831 1444.38,-831 1444.38,-831 1450.38,-831 1456.38,-837 1456.38,-843 1456.38,-843 1456.38,-872 1456.38,-872 1456.38,-878 1450.38,-884 1444.38,-884 1444.38,-884 1158,-884 1158,-884 1152,-884 1146,-878 1146,-872 1146,-872 1146,-843 1146,-843 1146,-837 1152,-831 1158,-831"/>
<text text-anchor="middle" x="1301.19" y="-871.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">loadGameSettings</text>
</g>
<g id="clust27" class="cluster">
<title>cluster_src/client/application/commands/removeCardFromDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1149.38,-892C1149.38,-892 1453,-892 1453,-892 1459,-892 1465,-898 1465,-904 1465,-904 1465,-933 1465,-933 1465,-939 1459,-945 1453,-945 1453,-945 1149.38,-945 1149.38,-945 1143.38,-945 1137.38,-939 1137.38,-933 1137.38,-933 1137.38,-904 1137.38,-904 1137.38,-898 1143.38,-892 1149.38,-892"/>
<text text-anchor="middle" x="1301.19" y="-932.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">removeCardFromDeck</text>
</g>
<g id="clust28" class="cluster">
<title>cluster_src/client/application/commands/saveDeckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1165.88,-1258C1165.88,-1258 1242.88,-1258 1242.88,-1258 1248.88,-1258 1254.88,-1264 1254.88,-1270 1254.88,-1270 1254.88,-1299 1254.88,-1299 1254.88,-1305 1248.88,-1311 1242.88,-1311 1242.88,-1311 1165.88,-1311 1165.88,-1311 1159.88,-1311 1153.88,-1305 1153.88,-1299 1153.88,-1299 1153.88,-1270 1153.88,-1270 1153.88,-1264 1159.88,-1258 1165.88,-1258"/>
<text text-anchor="middle" x="1204.38" y="-1298.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">saveDeckDraft</text>
</g>
<g id="clust29" class="cluster">
<title>cluster_src/client/application/commands/search</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1357.75,-1258C1357.75,-1258 1403.75,-1258 1403.75,-1258 1409.75,-1258 1415.75,-1264 1415.75,-1270 1415.75,-1270 1415.75,-1299 1415.75,-1299 1415.75,-1305 1409.75,-1311 1403.75,-1311 1403.75,-1311 1357.75,-1311 1357.75,-1311 1351.75,-1311 1345.75,-1305 1345.75,-1299 1345.75,-1299 1345.75,-1270 1345.75,-1270 1345.75,-1264 1351.75,-1258 1357.75,-1258"/>
<text text-anchor="middle" x="1380.75" y="-1298.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">search</text>
</g>
<g id="clust30" class="cluster">
<title>cluster_src/client/application/commands/showCardDetails</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1160.62,-953C1160.62,-953 1441.75,-953 1441.75,-953 1447.75,-953 1453.75,-959 1453.75,-965 1453.75,-965 1453.75,-994 1453.75,-994 1453.75,-1000 1447.75,-1006 1441.75,-1006 1441.75,-1006 1160.62,-1006 1160.62,-1006 1154.62,-1006 1148.62,-1000 1148.62,-994 1148.62,-994 1148.62,-965 1148.62,-965 1148.62,-959 1154.62,-953 1160.62,-953"/>
<text text-anchor="middle" x="1301.19" y="-993.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">showCardDetails</text>
</g>
<g id="clust31" class="cluster">
<title>cluster_src/client/application/commands/switchDeckBuilderView</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1148.25,-587C1148.25,-587 1454.5,-587 1454.5,-587 1460.5,-587 1466.5,-593 1466.5,-599 1466.5,-599 1466.5,-628 1466.5,-628 1466.5,-634 1460.5,-640 1454.5,-640 1454.5,-640 1148.25,-640 1148.25,-640 1142.25,-640 1136.25,-634 1136.25,-628 1136.25,-628 1136.25,-599 1136.25,-599 1136.25,-593 1142.25,-587 1148.25,-587"/>
<text text-anchor="middle" x="1301.38" y="-627.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">switchDeckBuilderView</text>
</g>
<g id="clust32" class="cluster">
<title>cluster_src/client/application/commands/updateAvailableFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1150.12,-648C1150.12,-648 1452.25,-648 1452.25,-648 1458.25,-648 1464.25,-654 1464.25,-660 1464.25,-660 1464.25,-689 1464.25,-689 1464.25,-695 1458.25,-701 1452.25,-701 1452.25,-701 1150.12,-701 1150.12,-701 1144.12,-701 1138.12,-695 1138.12,-689 1138.12,-689 1138.12,-660 1138.12,-660 1138.12,-654 1144.12,-648 1150.12,-648"/>
<text text-anchor="middle" x="1301.19" y="-688.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">updateAvailableFilters</text>
</g>
<g id="clust33" class="cluster">
<title>cluster_src/client/application/queries</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1323.12,-1346C1323.12,-1346 1602.75,-1346 1602.75,-1346 1608.75,-1346 1614.75,-1352 1614.75,-1358 1614.75,-1358 1614.75,-1945 1614.75,-1945 1614.75,-1951 1608.75,-1957 1602.75,-1957 1602.75,-1957 1323.12,-1957 1323.12,-1957 1317.12,-1957 1311.12,-1951 1311.12,-1945 1311.12,-1945 1311.12,-1358 1311.12,-1358 1311.12,-1352 1317.12,-1346 1323.12,-1346"/>
<text text-anchor="middle" x="1462.94" y="-1944.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">queries</text>
</g>
<g id="clust34" class="cluster">
<title>cluster_src/client/application/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1643.25,-2011C1643.25,-2011 1731.5,-2011 1731.5,-2011 1737.5,-2011 1743.5,-2017 1743.5,-2023 1743.5,-2023 1743.5,-2083 1743.5,-2083 1743.5,-2089 1737.5,-2095 1731.5,-2095 1731.5,-2095 1643.25,-2095 1643.25,-2095 1637.25,-2095 1631.25,-2089 1631.25,-2083 1631.25,-2083 1631.25,-2023 1631.25,-2023 1631.25,-2017 1637.25,-2011 1643.25,-2011"/>
<text text-anchor="middle" x="1687.38" y="-2082.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust35" class="cluster">
<title>cluster_src/client/application/subscribers</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M943.75,-1677C943.75,-1677 1050.75,-1677 1050.75,-1677 1056.75,-1677 1062.75,-1683 1062.75,-1689 1062.75,-1689 1062.75,-1718 1062.75,-1718 1062.75,-1724 1056.75,-1730 1050.75,-1730 1050.75,-1730 943.75,-1730 943.75,-1730 937.75,-1730 931.75,-1724 931.75,-1718 931.75,-1718 931.75,-1689 931.75,-1689 931.75,-1683 937.75,-1677 943.75,-1677"/>
<text text-anchor="middle" x="997.25" y="-1717.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">subscribers</text>
</g>
<g id="clust36" class="cluster">
<title>cluster_src/client/domain</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1494.5,-161C1494.5,-161 2185.75,-161 2185.75,-161 2191.75,-161 2197.75,-167 2197.75,-173 2197.75,-173 2197.75,-490 2197.75,-490 2197.75,-496 2191.75,-502 2185.75,-502 2185.75,-502 1494.5,-502 1494.5,-502 1488.5,-502 1482.5,-496 1482.5,-490 1482.5,-490 1482.5,-173 1482.5,-173 1482.5,-167 1488.5,-161 1494.5,-161"/>
<text text-anchor="middle" x="1840.12" y="-489.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">domain</text>
</g>
<g id="clust37" class="cluster">
<title>cluster_src/client/domain/CardData</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M2118.75,-422C2118.75,-422 2177.75,-422 2177.75,-422 2183.75,-422 2189.75,-428 2189.75,-434 2189.75,-434 2189.75,-463 2189.75,-463 2189.75,-469 2183.75,-475 2177.75,-475 2177.75,-475 2118.75,-475 2118.75,-475 2112.75,-475 2106.75,-469 2106.75,-463 2106.75,-463 2106.75,-434 2106.75,-434 2106.75,-428 2112.75,-422 2118.75,-422"/>
<text text-anchor="middle" x="2148.25" y="-462.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CardData</text>
</g>
<g id="clust38" class="cluster">
<title>cluster_src/client/domain/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1502.5,-319C1502.5,-319 1954.5,-319 1954.5,-319 1960.5,-319 1966.5,-325 1966.5,-331 1966.5,-331 1966.5,-463 1966.5,-463 1966.5,-469 1960.5,-475 1954.5,-475 1954.5,-475 1502.5,-475 1502.5,-475 1496.5,-475 1490.5,-469 1490.5,-463 1490.5,-463 1490.5,-331 1490.5,-331 1490.5,-325 1496.5,-319 1502.5,-319"/>
<text text-anchor="middle" x="1728.5" y="-462.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust39" class="cluster">
<title>cluster_src/client/domain/DeckBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1654.12,-169C1654.12,-169 2086.75,-169 2086.75,-169 2092.75,-169 2098.75,-175 2098.75,-181 2098.75,-181 2098.75,-224 2098.75,-224 2098.75,-230 2092.75,-236 2086.75,-236 2086.75,-236 1654.12,-236 1654.12,-236 1648.12,-236 1642.12,-230 1642.12,-224 1642.12,-224 1642.12,-181 1642.12,-181 1642.12,-175 1648.12,-169 1654.12,-169"/>
<text text-anchor="middle" x="1870.44" y="-223.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilder</text>
</g>
<g id="clust40" class="cluster">
<title>cluster_src/client/domain/GameSettings</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1504,-244C1504,-244 1851,-244 1851,-244 1857,-244 1863,-250 1863,-256 1863,-256 1863,-299 1863,-299 1863,-305 1857,-311 1851,-311 1851,-311 1504,-311 1504,-311 1498,-311 1492,-305 1492,-299 1492,-299 1492,-256 1492,-256 1492,-250 1498,-244 1504,-244"/>
<text text-anchor="middle" x="1677.5" y="-298.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameSettings</text>
</g>
<g id="clust41" class="cluster">
<title>cluster_src/client/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M208.25,-2184C208.25,-2184 1281,-2184 1281,-2184 1287,-2184 1293,-2190 1293,-2196 1293,-2196 1293,-5161 1293,-5161 1293,-5167 1287,-5173 1281,-5173 1281,-5173 208.25,-5173 208.25,-5173 202.25,-5173 196.25,-5167 196.25,-5161 196.25,-5161 196.25,-2196 196.25,-2196 196.25,-2190 202.25,-2184 208.25,-2184"/>
<text text-anchor="middle" x="744.62" y="-5160.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust42" class="cluster">
<title>cluster_src/client/infrastructure/builders</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M969.25,-4997C969.25,-4997 1025.25,-4997 1025.25,-4997 1031.25,-4997 1037.25,-5003 1037.25,-5009 1037.25,-5009 1037.25,-5038 1037.25,-5038 1037.25,-5044 1031.25,-5050 1025.25,-5050 1025.25,-5050 969.25,-5050 969.25,-5050 963.25,-5050 957.25,-5044 957.25,-5038 957.25,-5038 957.25,-5009 957.25,-5009 957.25,-5003 963.25,-4997 969.25,-4997"/>
<text text-anchor="middle" x="997.25" y="-5037.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">builders</text>
</g>
<g id="clust43" class="cluster">
<title>cluster_src/client/infrastructure/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M430,-2759C430,-2759 1261.12,-2759 1261.12,-2759 1267.12,-2759 1273.12,-2765 1273.12,-2771 1273.12,-2771 1273.12,-4244 1273.12,-4244 1273.12,-4250 1267.12,-4256 1261.12,-4256 1261.12,-4256 430,-4256 430,-4256 424,-4256 418,-4250 418,-4244 418,-4244 418,-2771 418,-2771 418,-2765 424,-2759 430,-2759"/>
<text text-anchor="middle" x="845.56" y="-4243.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust44" class="cluster">
<title>cluster_src/client/infrastructure/components/app</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M438,-3081C438,-3081 831.38,-3081 831.38,-3081 837.38,-3081 843.38,-3087 843.38,-3093 843.38,-3093 843.38,-4217 843.38,-4217 843.38,-4223 837.38,-4229 831.38,-4229 831.38,-4229 438,-4229 438,-4229 432,-4229 426,-4223 426,-4217 426,-4217 426,-3093 426,-3093 426,-3087 432,-3081 438,-3081"/>
<text text-anchor="middle" x="634.69" y="-4216.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">app</text>
</g>
<g id="clust45" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M467.25,-3089C467.25,-3089 810,-3089 810,-3089 816,-3089 822,-3095 822,-3101 822,-3101 822,-3231 822,-3231 822,-3237 816,-3243 810,-3243 810,-3243 467.25,-3243 467.25,-3243 461.25,-3243 455.25,-3237 455.25,-3231 455.25,-3231 455.25,-3101 455.25,-3101 455.25,-3095 461.25,-3089 467.25,-3089"/>
<text text-anchor="middle" x="638.62" y="-3230.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Auth</text>
</g>
<g id="clust46" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth/SignIn</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M475.25,-3097C475.25,-3097 802,-3097 802,-3097 808,-3097 814,-3103 814,-3109 814,-3109 814,-3204 814,-3204 814,-3210 808,-3216 802,-3216 802,-3216 475.25,-3216 475.25,-3216 469.25,-3216 463.25,-3210 463.25,-3204 463.25,-3204 463.25,-3109 463.25,-3109 463.25,-3103 469.25,-3097 475.25,-3097"/>
<text text-anchor="middle" x="638.62" y="-3203.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignIn</text>
</g>
<g id="clust47" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth/SignIn/SignInButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M483.25,-3105C483.25,-3105 557.25,-3105 557.25,-3105 563.25,-3105 569.25,-3111 569.25,-3117 569.25,-3117 569.25,-3146 569.25,-3146 569.25,-3152 563.25,-3158 557.25,-3158 557.25,-3158 483.25,-3158 483.25,-3158 477.25,-3158 471.25,-3152 471.25,-3146 471.25,-3146 471.25,-3117 471.25,-3117 471.25,-3111 477.25,-3105 483.25,-3105"/>
<text text-anchor="middle" x="520.25" y="-3145.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInButton</text>
</g>
<g id="clust48" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Auth/SignIn/SignInForm</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M710.25,-3105C710.25,-3105 794,-3105 794,-3105 800,-3105 806,-3111 806,-3117 806,-3117 806,-3177 806,-3177 806,-3183 800,-3189 794,-3189 794,-3189 710.25,-3189 710.25,-3189 704.25,-3189 698.25,-3183 698.25,-3177 698.25,-3177 698.25,-3117 698.25,-3117 698.25,-3111 704.25,-3105 710.25,-3105"/>
<text text-anchor="middle" x="752.12" y="-3176.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInForm</text>
</g>
<g id="clust49" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M461.75,-3835C461.75,-3835 578.75,-3835 578.75,-3835 584.75,-3835 590.75,-3841 590.75,-3847 590.75,-3847 590.75,-3972 590.75,-3972 590.75,-3978 584.75,-3984 578.75,-3984 578.75,-3984 461.75,-3984 461.75,-3984 455.75,-3984 449.75,-3978 449.75,-3972 449.75,-3972 449.75,-3847 449.75,-3847 449.75,-3841 455.75,-3835 461.75,-3835"/>
<text text-anchor="middle" x="520.25" y="-3971.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust50" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Catalog/GameDetailsButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M469.75,-3904C469.75,-3904 570.75,-3904 570.75,-3904 576.75,-3904 582.75,-3910 582.75,-3916 582.75,-3916 582.75,-3945 582.75,-3945 582.75,-3951 576.75,-3957 570.75,-3957 570.75,-3957 469.75,-3957 469.75,-3957 463.75,-3957 457.75,-3951 457.75,-3945 457.75,-3945 457.75,-3916 457.75,-3916 457.75,-3910 463.75,-3904 469.75,-3904"/>
<text text-anchor="middle" x="520.25" y="-3944.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameDetailsButton</text>
</g>
<g id="clust51" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Catalog/PlayGameButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M475,-3843C475,-3843 565.5,-3843 565.5,-3843 571.5,-3843 577.5,-3849 577.5,-3855 577.5,-3855 577.5,-3884 577.5,-3884 577.5,-3890 571.5,-3896 565.5,-3896 565.5,-3896 475,-3896 475,-3896 469,-3896 463,-3890 463,-3884 463,-3884 463,-3855 463,-3855 463,-3849 469,-3843 475,-3843"/>
<text text-anchor="middle" x="520.25" y="-3883.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">PlayGameButton</text>
</g>
<g id="clust52" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M446,-3251C446,-3251 823.38,-3251 823.38,-3251 829.38,-3251 835.38,-3257 835.38,-3263 835.38,-3263 835.38,-3815 835.38,-3815 835.38,-3821 829.38,-3827 823.38,-3827 823.38,-3827 446,-3827 446,-3827 440,-3827 434,-3821 434,-3815 434,-3815 434,-3263 434,-3263 434,-3257 440,-3251 446,-3251"/>
<text text-anchor="middle" x="634.69" y="-3814.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilding</text>
</g>
<g id="clust53" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M463.38,-3747C463.38,-3747 577.12,-3747 577.12,-3747 583.12,-3747 589.12,-3753 589.12,-3759 589.12,-3759 589.12,-3788 589.12,-3788 589.12,-3794 583.12,-3800 577.12,-3800 577.12,-3800 463.38,-3800 463.38,-3800 457.38,-3800 451.38,-3794 451.38,-3788 451.38,-3788 451.38,-3759 451.38,-3759 451.38,-3753 457.38,-3747 463.38,-3747"/>
<text text-anchor="middle" x="520.25" y="-3787.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderCardsGrid</text>
</g>
<g id="clust54" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M468.62,-3686C468.62,-3686 571.88,-3686 571.88,-3686 577.88,-3686 583.88,-3692 583.88,-3698 583.88,-3698 583.88,-3727 583.88,-3727 583.88,-3733 577.88,-3739 571.88,-3739 571.88,-3739 468.62,-3739 468.62,-3739 462.62,-3739 456.62,-3733 456.62,-3727 456.62,-3727 456.62,-3698 456.62,-3698 456.62,-3692 462.62,-3686 468.62,-3686"/>
<text text-anchor="middle" x="520.25" y="-3726.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderInitializer</text>
</g>
<g id="clust55" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M472.38,-3625C472.38,-3625 792.5,-3625 792.5,-3625 798.5,-3625 804.5,-3631 804.5,-3637 804.5,-3637 804.5,-3666 804.5,-3666 804.5,-3672 798.5,-3678 792.5,-3678 792.5,-3678 472.38,-3678 472.38,-3678 466.38,-3678 460.38,-3672 460.38,-3666 460.38,-3666 460.38,-3637 460.38,-3637 460.38,-3631 466.38,-3625 472.38,-3625"/>
<text text-anchor="middle" x="632.44" y="-3665.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuilderPanel</text>
</g>
<g id="clust56" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M703.88,-3747C703.88,-3747 800.38,-3747 800.38,-3747 806.38,-3747 812.38,-3753 812.38,-3759 812.38,-3759 812.38,-3788 812.38,-3788 812.38,-3794 806.38,-3800 800.38,-3800 800.38,-3800 703.88,-3800 703.88,-3800 697.88,-3800 691.88,-3794 691.88,-3788 691.88,-3788 691.88,-3759 691.88,-3759 691.88,-3753 697.88,-3747 703.88,-3747"/>
<text text-anchor="middle" x="752.12" y="-3787.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingCard</text>
</g>
<g id="clust57" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M469.75,-3442C469.75,-3442 781.62,-3442 781.62,-3442 787.62,-3442 793.62,-3448 793.62,-3454 793.62,-3454 793.62,-3483 793.62,-3483 793.62,-3489 787.62,-3495 781.62,-3495 781.62,-3495 469.75,-3495 469.75,-3495 463.75,-3495 457.75,-3489 457.75,-3483 457.75,-3483 457.75,-3454 457.75,-3454 457.75,-3448 463.75,-3442 469.75,-3442"/>
<text text-anchor="middle" x="625.69" y="-3482.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingFilters</text>
</g>
<g id="clust58" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M454,-3381C454,-3381 586.5,-3381 586.5,-3381 592.5,-3381 598.5,-3387 598.5,-3393 598.5,-3393 598.5,-3422 598.5,-3422 598.5,-3428 592.5,-3434 586.5,-3434 586.5,-3434 454,-3434 454,-3434 448,-3434 442,-3428 442,-3422 442,-3422 442,-3393 442,-3393 442,-3387 448,-3381 454,-3381"/>
<text text-anchor="middle" x="520.25" y="-3421.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckBuildingSkeletonCard</text>
</g>
<g id="clust59" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M693,-3503C693,-3503 811.25,-3503 811.25,-3503 817.25,-3503 823.25,-3509 823.25,-3515 823.25,-3515 823.25,-3544 823.25,-3544 823.25,-3550 817.25,-3556 811.25,-3556 811.25,-3556 693,-3556 693,-3556 687,-3556 681,-3550 681,-3544 681,-3544 681,-3515 681,-3515 681,-3509 687,-3503 693,-3503"/>
<text text-anchor="middle" x="752.12" y="-3543.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckCardDetailsDialog</text>
</g>
<g id="clust60" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M471.25,-3259C471.25,-3259 569.25,-3259 569.25,-3259 575.25,-3259 581.25,-3265 581.25,-3271 581.25,-3271 581.25,-3300 581.25,-3300 581.25,-3306 575.25,-3312 569.25,-3312 569.25,-3312 471.25,-3312 471.25,-3312 465.25,-3312 459.25,-3306 459.25,-3300 459.25,-3300 459.25,-3271 459.25,-3271 459.25,-3265 465.25,-3259 471.25,-3259"/>
<text text-anchor="middle" x="520.25" y="-3299.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">DeckDraftInitializer</text>
</g>
<g id="clust61" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M473.12,-3320C473.12,-3320 567.38,-3320 567.38,-3320 573.38,-3320 579.38,-3326 579.38,-3332 579.38,-3332 579.38,-3361 579.38,-3361 579.38,-3367 573.38,-3373 567.38,-3373 567.38,-3373 473.12,-3373 473.12,-3373 467.12,-3373 461.12,-3367 461.12,-3361 461.12,-3361 461.12,-3332 461.12,-3332 461.12,-3326 467.12,-3320 473.12,-3320"/>
<text text-anchor="middle" x="520.25" y="-3360.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">EditDeckInitializer</text>
</g>
<g id="clust62" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M706.88,-3564C706.88,-3564 797.38,-3564 797.38,-3564 803.38,-3564 809.38,-3570 809.38,-3576 809.38,-3576 809.38,-3605 809.38,-3605 809.38,-3611 803.38,-3617 797.38,-3617 797.38,-3617 706.88,-3617 706.88,-3617 700.88,-3617 694.88,-3611 694.88,-3605 694.88,-3605 694.88,-3576 694.88,-3576 694.88,-3570 700.88,-3564 706.88,-3564"/>
<text text-anchor="middle" x="752.12" y="-3604.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SaveDeckDialog</text>
</g>
<g id="clust63" class="cluster">
<title>cluster_src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M688.88,-3686C688.88,-3686 815.38,-3686 815.38,-3686 821.38,-3686 827.38,-3692 827.38,-3698 827.38,-3698 827.38,-3727 827.38,-3727 827.38,-3733 821.38,-3739 815.38,-3739 815.38,-3739 688.88,-3739 688.88,-3739 682.88,-3739 676.88,-3733 676.88,-3727 676.88,-3727 676.88,-3698 676.88,-3698 676.88,-3692 682.88,-3686 688.88,-3686"/>
<text text-anchor="middle" x="752.12" y="-3726.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">TotalCardsInDeckCounter</text>
</g>
<g id="clust64" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M463.25,-3992C463.25,-3992 577.25,-3992 577.25,-3992 583.25,-3992 589.25,-3998 589.25,-4004 589.25,-4004 589.25,-4190 589.25,-4190 589.25,-4196 583.25,-4202 577.25,-4202 577.25,-4202 463.25,-4202 463.25,-4202 457.25,-4202 451.25,-4196 451.25,-4190 451.25,-4190 451.25,-4004 451.25,-4004 451.25,-3998 457.25,-3992 463.25,-3992"/>
<text text-anchor="middle" x="520.25" y="-4189.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Gaming</text>
</g>
<g id="clust65" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming/LeaveMatchButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M471.25,-4000C471.25,-4000 569.25,-4000 569.25,-4000 575.25,-4000 581.25,-4006 581.25,-4012 581.25,-4012 581.25,-4041 581.25,-4041 581.25,-4047 575.25,-4053 569.25,-4053 569.25,-4053 471.25,-4053 471.25,-4053 465.25,-4053 459.25,-4047 459.25,-4041 459.25,-4041 459.25,-4012 459.25,-4012 459.25,-4006 465.25,-4000 471.25,-4000"/>
<text text-anchor="middle" x="520.25" y="-4040.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">LeaveMatchButton</text>
</g>
<g id="clust66" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M497.12,-4122C497.12,-4122 543.38,-4122 543.38,-4122 549.38,-4122 555.38,-4128 555.38,-4134 555.38,-4134 555.38,-4163 555.38,-4163 555.38,-4169 549.38,-4175 543.38,-4175 543.38,-4175 497.12,-4175 497.12,-4175 491.12,-4175 485.12,-4169 485.12,-4163 485.12,-4163 485.12,-4134 485.12,-4134 485.12,-4128 491.12,-4122 497.12,-4122"/>
<text text-anchor="middle" x="520.25" y="-4162.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust67" class="cluster">
<title>cluster_src/client/infrastructure/components/app/Gaming/StartGameButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M474.62,-4061C474.62,-4061 565.88,-4061 565.88,-4061 571.88,-4061 577.88,-4067 577.88,-4073 577.88,-4073 577.88,-4102 577.88,-4102 577.88,-4108 571.88,-4114 565.88,-4114 565.88,-4114 474.62,-4114 474.62,-4114 468.62,-4114 462.62,-4108 462.62,-4102 462.62,-4102 462.62,-4073 462.62,-4073 462.62,-4067 468.62,-4061 474.62,-4061"/>
<text text-anchor="middle" x="520.25" y="-4101.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">StartGameButton</text>
</g>
<g id="clust68" class="cluster">
<title>cluster_src/client/infrastructure/components/debug</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M443,-2863C443,-2863 597.5,-2863 597.5,-2863 603.5,-2863 609.5,-2869 609.5,-2875 609.5,-2875 609.5,-3061 609.5,-3061 609.5,-3067 603.5,-3073 597.5,-3073 597.5,-3073 443,-3073 443,-3073 437,-3073 431,-3067 431,-3061 431,-3061 431,-2875 431,-2875 431,-2869 437,-2863 443,-2863"/>
<text text-anchor="middle" x="520.25" y="-3060.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">debug</text>
</g>
<g id="clust69" class="cluster">
<title>cluster_src/client/infrastructure/components/debug/JsonObjectViewer</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M472.75,-2871C472.75,-2871 567.75,-2871 567.75,-2871 573.75,-2871 579.75,-2877 579.75,-2883 579.75,-2883 579.75,-2912 579.75,-2912 579.75,-2918 573.75,-2924 567.75,-2924 567.75,-2924 472.75,-2924 472.75,-2924 466.75,-2924 460.75,-2918 460.75,-2912 460.75,-2912 460.75,-2883 460.75,-2883 460.75,-2877 466.75,-2871 472.75,-2871"/>
<text text-anchor="middle" x="520.25" y="-2911.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">JsonObjectViewer</text>
</g>
<g id="clust70" class="cluster">
<title>cluster_src/client/infrastructure/components/debug/MatchConsoleEvents</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M466,-2932C466,-2932 574.5,-2932 574.5,-2932 580.5,-2932 586.5,-2938 586.5,-2944 586.5,-2944 586.5,-2973 586.5,-2973 586.5,-2979 580.5,-2985 574.5,-2985 574.5,-2985 466,-2985 466,-2985 460,-2985 454,-2979 454,-2973 454,-2973 454,-2944 454,-2944 454,-2938 460,-2932 466,-2932"/>
<text text-anchor="middle" x="520.25" y="-2972.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchConsoleEvents</text>
</g>
<g id="clust71" class="cluster">
<title>cluster_src/client/infrastructure/components/debug/MatchMakingConsoleEvents</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M451,-2993C451,-2993 589.5,-2993 589.5,-2993 595.5,-2993 601.5,-2999 601.5,-3005 601.5,-3005 601.5,-3034 601.5,-3034 601.5,-3040 595.5,-3046 589.5,-3046 589.5,-3046 451,-3046 451,-3046 445,-3046 439,-3040 439,-3034 439,-3034 439,-3005 439,-3005 439,-2999 445,-2993 451,-2993"/>
<text text-anchor="middle" x="520.25" y="-3033.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchMakingConsoleEvents</text>
</g>
<g id="clust72" class="cluster">
<title>cluster_src/client/infrastructure/components/redirections</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M459.88,-2767C459.88,-2767 580.62,-2767 580.62,-2767 586.62,-2767 592.62,-2773 592.62,-2779 592.62,-2779 592.62,-2843 592.62,-2843 592.62,-2849 586.62,-2855 580.62,-2855 580.62,-2855 459.88,-2855 459.88,-2855 453.88,-2855 447.88,-2849 447.88,-2843 447.88,-2843 447.88,-2779 447.88,-2779 447.88,-2773 453.88,-2767 459.88,-2767"/>
<text text-anchor="middle" x="520.25" y="-2842.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">redirections</text>
</g>
<g id="clust73" class="cluster">
<title>cluster_src/client/infrastructure/components/redirections/RedirectToGameList</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M467.88,-2775C467.88,-2775 572.62,-2775 572.62,-2775 578.62,-2775 584.62,-2781 584.62,-2787 584.62,-2787 584.62,-2816 584.62,-2816 584.62,-2822 578.62,-2828 572.62,-2828 572.62,-2828 467.88,-2828 467.88,-2828 461.88,-2828 455.88,-2822 455.88,-2816 455.88,-2816 455.88,-2787 455.88,-2787 455.88,-2781 461.88,-2775 467.88,-2775"/>
<text text-anchor="middle" x="520.25" y="-2815.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">RedirectToGameList</text>
</g>
<g id="clust74" class="cluster">
<title>cluster_src/client/infrastructure/components/ui</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M947,-2980C947,-2980 1253.12,-2980 1253.12,-2980 1259.12,-2980 1265.12,-2986 1265.12,-2992 1265.12,-2992 1265.12,-3300 1265.12,-3300 1265.12,-3306 1259.12,-3312 1253.12,-3312 1253.12,-3312 947,-3312 947,-3312 941,-3312 935,-3306 935,-3300 935,-3300 935,-2992 935,-2992 935,-2986 941,-2980 947,-2980"/>
<text text-anchor="middle" x="1100.06" y="-3299.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ui</text>
</g>
<g id="clust75" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/Background</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M961.75,-2988C961.75,-2988 1032.75,-2988 1032.75,-2988 1038.75,-2988 1044.75,-2994 1044.75,-3000 1044.75,-3000 1044.75,-3029 1044.75,-3029 1044.75,-3035 1038.75,-3041 1032.75,-3041 1032.75,-3041 961.75,-3041 961.75,-3041 955.75,-3041 949.75,-3035 949.75,-3029 949.75,-3029 949.75,-3000 949.75,-3000 949.75,-2994 955.75,-2988 961.75,-2988"/>
<text text-anchor="middle" x="997.25" y="-3028.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Background</text>
</g>
<g id="clust76" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/ShiningButton</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M957.62,-3049C957.62,-3049 1245.12,-3049 1245.12,-3049 1251.12,-3049 1257.12,-3055 1257.12,-3061 1257.12,-3061 1257.12,-3090 1257.12,-3090 1257.12,-3096 1251.12,-3102 1245.12,-3102 1245.12,-3102 957.62,-3102 957.62,-3102 951.62,-3102 945.62,-3096 945.62,-3090 945.62,-3090 945.62,-3061 945.62,-3061 945.62,-3055 951.62,-3049 957.62,-3049"/>
<text text-anchor="middle" x="1101.38" y="-3089.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ShiningButton</text>
</g>
<g id="clust77" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/ShiningCard</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M960.62,-3110C960.62,-3110 1242.12,-3110 1242.12,-3110 1248.12,-3110 1254.12,-3116 1254.12,-3122 1254.12,-3122 1254.12,-3151 1254.12,-3151 1254.12,-3157 1248.12,-3163 1242.12,-3163 1242.12,-3163 960.62,-3163 960.62,-3163 954.62,-3163 948.62,-3157 948.62,-3151 948.62,-3151 948.62,-3122 948.62,-3122 948.62,-3116 954.62,-3110 960.62,-3110"/>
<text text-anchor="middle" x="1101.38" y="-3150.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ShiningCard</text>
</g>
<g id="clust78" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/SkeletonHelper</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M955,-3171C955,-3171 1039.5,-3171 1039.5,-3171 1045.5,-3171 1051.5,-3177 1051.5,-3183 1051.5,-3183 1051.5,-3212 1051.5,-3212 1051.5,-3218 1045.5,-3224 1039.5,-3224 1039.5,-3224 955,-3224 955,-3224 949,-3224 943,-3218 943,-3212 943,-3212 943,-3183 943,-3183 943,-3177 949,-3171 955,-3171"/>
<text text-anchor="middle" x="997.25" y="-3211.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SkeletonHelper</text>
</g>
<g id="clust79" class="cluster">
<title>cluster_src/client/infrastructure/components/ui/Sparkles</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M968.5,-3232C968.5,-3232 1026,-3232 1026,-3232 1032,-3232 1038,-3238 1038,-3244 1038,-3244 1038,-3273 1038,-3273 1038,-3279 1032,-3285 1026,-3285 1026,-3285 968.5,-3285 968.5,-3285 962.5,-3285 956.5,-3279 956.5,-3273 956.5,-3273 956.5,-3244 956.5,-3244 956.5,-3238 962.5,-3232 968.5,-3232"/>
<text text-anchor="middle" x="997.25" y="-3272.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Sparkles</text>
</g>
<g id="clust80" class="cluster">
<title>cluster_src/client/infrastructure/hooks</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M903.12,-4387C903.12,-4387 1249.75,-4387 1249.75,-4387 1255.75,-4387 1261.75,-4393 1261.75,-4399 1261.75,-4399 1261.75,-4977 1261.75,-4977 1261.75,-4983 1255.75,-4989 1249.75,-4989 1249.75,-4989 903.12,-4989 903.12,-4989 897.12,-4989 891.12,-4983 891.12,-4977 891.12,-4977 891.12,-4399 891.12,-4399 891.12,-4393 897.12,-4387 903.12,-4387"/>
<text text-anchor="middle" x="1076.44" y="-4976.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hooks</text>
</g>
<g id="clust81" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useCatalogCardsByGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M941.75,-4909C941.75,-4909 1053.75,-4909 1053.75,-4909 1059.75,-4909 1065.75,-4915 1065.75,-4921 1065.75,-4921 1065.75,-4950 1065.75,-4950 1065.75,-4956 1059.75,-4962 1053.75,-4962 1053.75,-4962 941.75,-4962 941.75,-4962 935.75,-4962 929.75,-4956 929.75,-4950 929.75,-4950 929.75,-4921 929.75,-4921 929.75,-4915 935.75,-4909 941.75,-4909"/>
<text text-anchor="middle" x="997.75" y="-4949.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useCatalogCardsByGameId</text>
</g>
<g id="clust82" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDebounce</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M974.25,-4482C974.25,-4482 1241.75,-4482 1241.75,-4482 1247.75,-4482 1253.75,-4488 1253.75,-4494 1253.75,-4494 1253.75,-4523 1253.75,-4523 1253.75,-4529 1247.75,-4535 1241.75,-4535 1241.75,-4535 974.25,-4535 974.25,-4535 968.25,-4535 962.25,-4529 962.25,-4523 962.25,-4523 962.25,-4494 962.25,-4494 962.25,-4488 968.25,-4482 974.25,-4482"/>
<text text-anchor="middle" x="1108" y="-4522.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDebounce</text>
</g>
<g id="clust83" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDeckBuilder</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M956.5,-4421C956.5,-4421 1038,-4421 1038,-4421 1044,-4421 1050,-4427 1050,-4433 1050,-4433 1050,-4462 1050,-4462 1050,-4468 1044,-4474 1038,-4474 1038,-4474 956.5,-4474 956.5,-4474 950.5,-4474 944.5,-4468 944.5,-4462 944.5,-4462 944.5,-4433 944.5,-4433 944.5,-4427 950.5,-4421 956.5,-4421"/>
<text text-anchor="middle" x="997.25" y="-4461.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckBuilder</text>
</g>
<g id="clust84" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDeckById</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M959.5,-4543C959.5,-4543 1035,-4543 1035,-4543 1041,-4543 1047,-4549 1047,-4555 1047,-4555 1047,-4584 1047,-4584 1047,-4590 1041,-4596 1035,-4596 1035,-4596 959.5,-4596 959.5,-4596 953.5,-4596 947.5,-4590 947.5,-4584 947.5,-4584 947.5,-4555 947.5,-4555 947.5,-4549 953.5,-4543 959.5,-4543"/>
<text text-anchor="middle" x="997.25" y="-4583.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckById</text>
</g>
<g id="clust85" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useDeckId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M967,-4726C967,-4726 1027.5,-4726 1027.5,-4726 1033.5,-4726 1039.5,-4732 1039.5,-4738 1039.5,-4738 1039.5,-4767 1039.5,-4767 1039.5,-4773 1033.5,-4779 1027.5,-4779 1027.5,-4779 967,-4779 967,-4779 961,-4779 955,-4773 955,-4767 955,-4767 955,-4738 955,-4738 955,-4732 961,-4726 967,-4726"/>
<text text-anchor="middle" x="997.25" y="-4766.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useDeckId</text>
</g>
<g id="clust86" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M965.12,-4665C965.12,-4665 1029.38,-4665 1029.38,-4665 1035.38,-4665 1041.38,-4671 1041.38,-4677 1041.38,-4677 1041.38,-4706 1041.38,-4706 1041.38,-4712 1035.38,-4718 1029.38,-4718 1029.38,-4718 965.12,-4718 965.12,-4718 959.12,-4718 953.12,-4712 953.12,-4706 953.12,-4706 953.12,-4677 953.12,-4677 953.12,-4671 959.12,-4665 965.12,-4665"/>
<text text-anchor="middle" x="997.25" y="-4705.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useGameId</text>
</g>
<g id="clust87" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useGameSettingsByGameId</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M928.75,-4604C928.75,-4604 1065.75,-4604 1065.75,-4604 1071.75,-4604 1077.75,-4610 1077.75,-4616 1077.75,-4616 1077.75,-4645 1077.75,-4645 1077.75,-4651 1071.75,-4657 1065.75,-4657 1065.75,-4657 928.75,-4657 928.75,-4657 922.75,-4657 916.75,-4651 916.75,-4645 916.75,-4645 916.75,-4616 916.75,-4616 916.75,-4610 922.75,-4604 928.75,-4604"/>
<text text-anchor="middle" x="997.25" y="-4644.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useGameSettingsByGameId</text>
</g>
<g id="clust88" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M911.12,-4787C911.12,-4787 1083.38,-4787 1083.38,-4787 1089.38,-4787 1095.38,-4793 1095.38,-4799 1095.38,-4799 1095.38,-4828 1095.38,-4828 1095.38,-4834 1089.38,-4840 1083.38,-4840 1083.38,-4840 911.12,-4840 911.12,-4840 905.12,-4840 899.12,-4834 899.12,-4828 899.12,-4828 899.12,-4799 899.12,-4799 899.12,-4793 905.12,-4787 911.12,-4787"/>
<text text-anchor="middle" x="997.25" y="-4827.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useInitializeDeckBuilderFromLocation</text>
</g>
<g id="clust89" class="cluster">
<title>cluster_src/client/infrastructure/hooks/useLocale</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M967.38,-4848C967.38,-4848 1027.12,-4848 1027.12,-4848 1033.12,-4848 1039.12,-4854 1039.12,-4860 1039.12,-4860 1039.12,-4889 1039.12,-4889 1039.12,-4895 1033.12,-4901 1027.12,-4901 1027.12,-4901 967.38,-4901 967.38,-4901 961.38,-4901 955.38,-4895 955.38,-4889 955.38,-4889 955.38,-4860 955.38,-4860 955.38,-4854 961.38,-4848 967.38,-4848"/>
<text text-anchor="middle" x="997.25" y="-4888.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">useLocale</text>
</g>
<g id="clust90" class="cluster">
<title>cluster_src/client/infrastructure/layouts</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M469.25,-4264C469.25,-4264 571.25,-4264 571.25,-4264 577.25,-4264 583.25,-4270 583.25,-4276 583.25,-4276 583.25,-4401 583.25,-4401 583.25,-4407 577.25,-4413 571.25,-4413 571.25,-4413 469.25,-4413 469.25,-4413 463.25,-4413 457.25,-4407 457.25,-4401 457.25,-4401 457.25,-4276 457.25,-4276 457.25,-4270 463.25,-4264 469.25,-4264"/>
<text text-anchor="middle" x="520.25" y="-4400.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">layouts</text>
</g>
<g id="clust91" class="cluster">
<title>cluster_src/client/infrastructure/layouts/FullPageLayout</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M477.25,-4333C477.25,-4333 563.25,-4333 563.25,-4333 569.25,-4333 575.25,-4339 575.25,-4345 575.25,-4345 575.25,-4374 575.25,-4374 575.25,-4380 569.25,-4386 563.25,-4386 563.25,-4386 477.25,-4386 477.25,-4386 471.25,-4386 465.25,-4380 465.25,-4374 465.25,-4374 465.25,-4345 465.25,-4345 465.25,-4339 471.25,-4333 477.25,-4333"/>
<text text-anchor="middle" x="520.25" y="-4373.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">FullPageLayout</text>
</g>
<g id="clust92" class="cluster">
<title>cluster_src/client/infrastructure/layouts/RootLayout</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M485.88,-4272C485.88,-4272 554.62,-4272 554.62,-4272 560.62,-4272 566.62,-4278 566.62,-4284 566.62,-4284 566.62,-4313 566.62,-4313 566.62,-4319 560.62,-4325 554.62,-4325 554.62,-4325 485.88,-4325 485.88,-4325 479.88,-4325 473.88,-4319 473.88,-4313 473.88,-4313 473.88,-4284 473.88,-4284 473.88,-4278 479.88,-4272 485.88,-4272"/>
<text text-anchor="middle" x="520.25" y="-4312.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">RootLayout</text>
</g>
<g id="clust93" class="cluster">
<title>cluster_src/client/infrastructure/lib</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1152,-4264C1152,-4264 1256.75,-4264 1256.75,-4264 1262.75,-4264 1268.75,-4270 1268.75,-4276 1268.75,-4276 1268.75,-4367 1268.75,-4367 1268.75,-4373 1262.75,-4379 1256.75,-4379 1256.75,-4379 1152,-4379 1152,-4379 1146,-4379 1140,-4373 1140,-4367 1140,-4367 1140,-4276 1140,-4276 1140,-4270 1146,-4264 1152,-4264"/>
<text text-anchor="middle" x="1204.38" y="-4366.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">lib</text>
</g>
<g id="clust94" class="cluster">
<title>cluster_src/client/infrastructure/pages</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M216.25,-2192C216.25,-2192 597.25,-2192 597.25,-2192 603.25,-2192 609.25,-2198 609.25,-2204 609.25,-2204 609.25,-2739 609.25,-2739 609.25,-2745 603.25,-2751 597.25,-2751 597.25,-2751 216.25,-2751 216.25,-2751 210.25,-2751 204.25,-2745 204.25,-2739 204.25,-2739 204.25,-2204 204.25,-2204 204.25,-2198 210.25,-2192 216.25,-2192"/>
<text text-anchor="middle" x="406.75" y="-2738.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">pages</text>
</g>
<g id="clust95" class="cluster">
<title>cluster_src/client/infrastructure/pages/Auth</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M224.25,-2200C224.25,-2200 342.75,-2200 342.75,-2200 348.75,-2200 354.75,-2206 354.75,-2212 354.75,-2212 354.75,-2337 354.75,-2337 354.75,-2343 348.75,-2349 342.75,-2349 342.75,-2349 224.25,-2349 224.25,-2349 218.25,-2349 212.25,-2343 212.25,-2337 212.25,-2337 212.25,-2212 212.25,-2212 212.25,-2206 218.25,-2200 224.25,-2200"/>
<text text-anchor="middle" x="283.5" y="-2336.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Auth</text>
</g>
<g id="clust96" class="cluster">
<title>cluster_src/client/infrastructure/pages/Auth/AccessDeniedPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M232.25,-2269C232.25,-2269 334.75,-2269 334.75,-2269 340.75,-2269 346.75,-2275 346.75,-2281 346.75,-2281 346.75,-2310 346.75,-2310 346.75,-2316 340.75,-2322 334.75,-2322 334.75,-2322 232.25,-2322 232.25,-2322 226.25,-2322 220.25,-2316 220.25,-2310 220.25,-2310 220.25,-2281 220.25,-2281 220.25,-2275 226.25,-2269 232.25,-2269"/>
<text text-anchor="middle" x="283.5" y="-2309.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AccessDeniedPage</text>
</g>
<g id="clust97" class="cluster">
<title>cluster_src/client/infrastructure/pages/Auth/SignInPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M248.75,-2208C248.75,-2208 318.25,-2208 318.25,-2208 324.25,-2208 330.25,-2214 330.25,-2220 330.25,-2220 330.25,-2249 330.25,-2249 330.25,-2255 324.25,-2261 318.25,-2261 318.25,-2261 248.75,-2261 248.75,-2261 242.75,-2261 236.75,-2255 236.75,-2249 236.75,-2249 236.75,-2220 236.75,-2220 236.75,-2214 242.75,-2208 248.75,-2208"/>
<text text-anchor="middle" x="283.5" y="-2248.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SignInPage</text>
</g>
<g id="clust98" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M227.25,-2514C227.25,-2514 339.75,-2514 339.75,-2514 345.75,-2514 351.75,-2520 351.75,-2526 351.75,-2526 351.75,-2712 351.75,-2712 351.75,-2718 345.75,-2724 339.75,-2724 339.75,-2724 227.25,-2724 227.25,-2724 221.25,-2724 215.25,-2718 215.25,-2712 215.25,-2712 215.25,-2526 215.25,-2526 215.25,-2520 221.25,-2514 227.25,-2514"/>
<text text-anchor="middle" x="283.5" y="-2711.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Catalog</text>
</g>
<g id="clust99" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog/GameDetailsPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M235.25,-2644C235.25,-2644 331.75,-2644 331.75,-2644 337.75,-2644 343.75,-2650 343.75,-2656 343.75,-2656 343.75,-2685 343.75,-2685 343.75,-2691 337.75,-2697 331.75,-2697 331.75,-2697 235.25,-2697 235.25,-2697 229.25,-2697 223.25,-2691 223.25,-2685 223.25,-2685 223.25,-2656 223.25,-2656 223.25,-2650 229.25,-2644 235.25,-2644"/>
<text text-anchor="middle" x="283.5" y="-2684.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameDetailsPage</text>
</g>
<g id="clust100" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog/GameListPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M242.38,-2522C242.38,-2522 324.62,-2522 324.62,-2522 330.62,-2522 336.62,-2528 336.62,-2534 336.62,-2534 336.62,-2563 336.62,-2563 336.62,-2569 330.62,-2575 324.62,-2575 324.62,-2575 242.38,-2575 242.38,-2575 236.38,-2575 230.38,-2569 230.38,-2563 230.38,-2563 230.38,-2534 230.38,-2534 230.38,-2528 236.38,-2522 242.38,-2522"/>
<text text-anchor="middle" x="283.5" y="-2562.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">GameListPage</text>
</g>
<g id="clust101" class="cluster">
<title>cluster_src/client/infrastructure/pages/Catalog/PlayGamePage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M240.5,-2583C240.5,-2583 326.5,-2583 326.5,-2583 332.5,-2583 338.5,-2589 338.5,-2595 338.5,-2595 338.5,-2624 338.5,-2624 338.5,-2630 332.5,-2636 326.5,-2636 326.5,-2636 240.5,-2636 240.5,-2636 234.5,-2636 228.5,-2630 228.5,-2624 228.5,-2624 228.5,-2595 228.5,-2595 228.5,-2589 234.5,-2583 240.5,-2583"/>
<text text-anchor="middle" x="283.5" y="-2623.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">PlayGamePage</text>
</g>
<g id="clust102" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M241.5,-2357C241.5,-2357 589.25,-2357 589.25,-2357 595.25,-2357 601.25,-2363 601.25,-2369 601.25,-2369 601.25,-2494 601.25,-2494 601.25,-2500 595.25,-2506 589.25,-2506 589.25,-2506 241.5,-2506 241.5,-2506 235.5,-2506 229.5,-2500 229.5,-2494 229.5,-2494 229.5,-2369 229.5,-2369 229.5,-2363 235.5,-2357 241.5,-2357"/>
<text text-anchor="middle" x="415.38" y="-2493.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Gaming</text>
</g>
<g id="clust103" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M459.25,-2365C459.25,-2365 581.25,-2365 581.25,-2365 587.25,-2365 593.25,-2371 593.25,-2377 593.25,-2377 593.25,-2406 593.25,-2406 593.25,-2412 587.25,-2418 581.25,-2418 581.25,-2418 459.25,-2418 459.25,-2418 453.25,-2418 447.25,-2412 447.25,-2406 447.25,-2406 447.25,-2377 447.25,-2377 447.25,-2371 453.25,-2365 459.25,-2365"/>
<text text-anchor="middle" x="520.25" y="-2405.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ErrorLoadingMatchPage</text>
</g>
<g id="clust104" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming/FinishedMatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M468.62,-2426C468.62,-2426 571.88,-2426 571.88,-2426 577.88,-2426 583.88,-2432 583.88,-2438 583.88,-2438 583.88,-2467 583.88,-2467 583.88,-2473 577.88,-2479 571.88,-2479 571.88,-2479 468.62,-2479 468.62,-2479 462.62,-2479 456.62,-2473 456.62,-2467 456.62,-2467 456.62,-2438 456.62,-2438 456.62,-2432 462.62,-2426 468.62,-2426"/>
<text text-anchor="middle" x="520.25" y="-2466.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">FinishedMatchPage</text>
</g>
<g id="clust105" class="cluster">
<title>cluster_src/client/infrastructure/pages/Gaming/MatchPage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M249.5,-2426C249.5,-2426 317.5,-2426 317.5,-2426 323.5,-2426 329.5,-2432 329.5,-2438 329.5,-2438 329.5,-2467 329.5,-2467 329.5,-2473 323.5,-2479 317.5,-2479 317.5,-2479 249.5,-2479 249.5,-2479 243.5,-2479 237.5,-2473 237.5,-2467 237.5,-2467 237.5,-2438 237.5,-2438 237.5,-2432 243.5,-2426 249.5,-2426"/>
<text text-anchor="middle" x="283.5" y="-2466.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchPage</text>
</g>
<g id="clust106" class="cluster">
<title>cluster_src/client/infrastructure/providers</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M696.75,-4272C696.75,-4272 807.5,-4272 807.5,-4272 813.5,-4272 819.5,-4278 819.5,-4284 819.5,-4284 819.5,-4375 819.5,-4375 819.5,-4381 813.5,-4387 807.5,-4387 807.5,-4387 696.75,-4387 696.75,-4387 690.75,-4387 684.75,-4381 684.75,-4375 684.75,-4375 684.75,-4284 684.75,-4284 684.75,-4278 690.75,-4272 696.75,-4272"/>
<text text-anchor="middle" x="752.12" y="-4374.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">providers</text>
</g>
<g id="clust107" class="cluster">
<title>cluster_src/client/infrastructure/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1135.75,-4997C1135.75,-4997 1273,-4997 1273,-4997 1279,-4997 1285,-5003 1285,-5009 1285,-5009 1285,-5134 1285,-5134 1285,-5140 1279,-5146 1273,-5146 1273,-5146 1135.75,-5146 1135.75,-5146 1129.75,-5146 1123.75,-5140 1123.75,-5134 1123.75,-5134 1123.75,-5009 1123.75,-5009 1123.75,-5003 1129.75,-4997 1135.75,-4997"/>
<text text-anchor="middle" x="1204.38" y="-5133.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust108" class="cluster">
<title>cluster_src/client/infrastructure/services/deckDraft</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1143.75,-5005C1143.75,-5005 1265,-5005 1265,-5005 1271,-5005 1277,-5011 1277,-5017 1277,-5017 1277,-5046 1277,-5046 1277,-5052 1271,-5058 1265,-5058 1265,-5058 1143.75,-5058 1143.75,-5058 1137.75,-5058 1131.75,-5052 1131.75,-5046 1131.75,-5046 1131.75,-5017 1131.75,-5017 1131.75,-5011 1137.75,-5005 1143.75,-5005"/>
<text text-anchor="middle" x="1204.38" y="-5045.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">deckDraft</text>
</g>
<g id="clust109" class="cluster">
<title>cluster_src/client/infrastructure/services/location</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1146.38,-5066C1146.38,-5066 1262.38,-5066 1262.38,-5066 1268.38,-5066 1274.38,-5072 1274.38,-5078 1274.38,-5078 1274.38,-5107 1274.38,-5107 1274.38,-5113 1268.38,-5119 1262.38,-5119 1262.38,-5119 1146.38,-5119 1146.38,-5119 1140.38,-5119 1134.38,-5113 1134.38,-5107 1134.38,-5107 1134.38,-5078 1134.38,-5078 1134.38,-5072 1140.38,-5066 1146.38,-5066"/>
<text text-anchor="middle" x="1204.38" y="-5106.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">location</text>
</g>
<g id="clust110" class="cluster">
<title>cluster_src/server</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M374.75,-5275C374.75,-5275 1278.75,-5275 1278.75,-5275 1284.75,-5275 1290.75,-5281 1290.75,-5287 1290.75,-5287 1290.75,-7085 1290.75,-7085 1290.75,-7091 1284.75,-7097 1278.75,-7097 1278.75,-7097 374.75,-7097 374.75,-7097 368.75,-7097 362.75,-7091 362.75,-7085 362.75,-7085 362.75,-5287 362.75,-5287 362.75,-5281 368.75,-5275 374.75,-5275"/>
<text text-anchor="middle" x="826.75" y="-7084.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">server</text>
</g>
<g id="clust111" class="cluster">
<title>cluster_src/server/application</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M382.75,-5333C382.75,-5333 873.5,-5333 873.5,-5333 879.5,-5333 885.5,-5339 885.5,-5345 885.5,-5345 885.5,-6285 885.5,-6285 885.5,-6291 879.5,-6297 873.5,-6297 873.5,-6297 382.75,-6297 382.75,-6297 376.75,-6297 370.75,-6291 370.75,-6285 370.75,-6285 370.75,-5345 370.75,-5345 370.75,-5339 376.75,-5333 382.75,-5333"/>
<text text-anchor="middle" x="628.12" y="-6284.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">application</text>
</g>
<g id="clust112" class="cluster">
<title>cluster_src/server/application/commands</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M390.75,-5341C390.75,-5341 865.5,-5341 865.5,-5341 871.5,-5341 877.5,-5347 877.5,-5353 877.5,-5353 877.5,-5949 877.5,-5949 877.5,-5955 871.5,-5961 865.5,-5961 865.5,-5961 390.75,-5961 390.75,-5961 384.75,-5961 378.75,-5955 378.75,-5949 378.75,-5949 378.75,-5353 378.75,-5353 378.75,-5347 384.75,-5341 390.75,-5341"/>
<text text-anchor="middle" x="628.12" y="-5948.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">commands</text>
</g>
<g id="clust113" class="cluster">
<title>cluster_src/server/application/commands/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M445.25,-5349C445.25,-5349 811,-5349 811,-5349 817,-5349 823,-5355 823,-5361 823,-5361 823,-5425 823,-5425 823,-5431 817,-5437 811,-5437 811,-5437 445.25,-5437 445.25,-5437 439.25,-5437 433.25,-5431 433.25,-5425 433.25,-5425 433.25,-5361 433.25,-5361 433.25,-5355 439.25,-5349 445.25,-5349"/>
<text text-anchor="middle" x="628.12" y="-5424.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust114" class="cluster">
<title>cluster_src/server/application/commands/Deck/SaveDeck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M453.25,-5357C453.25,-5357 803,-5357 803,-5357 809,-5357 815,-5363 815,-5369 815,-5369 815,-5398 815,-5398 815,-5404 809,-5410 803,-5410 803,-5410 453.25,-5410 453.25,-5410 447.25,-5410 441.25,-5404 441.25,-5398 441.25,-5398 441.25,-5369 441.25,-5369 441.25,-5363 447.25,-5357 453.25,-5357"/>
<text text-anchor="middle" x="628.12" y="-5397.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">SaveDeck</text>
</g>
<g id="clust115" class="cluster">
<title>cluster_src/server/application/commands/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M441.12,-5445C441.12,-5445 815.12,-5445 815.12,-5445 821.12,-5445 827.12,-5451 827.12,-5457 827.12,-5457 827.12,-5521 827.12,-5521 827.12,-5527 821.12,-5533 815.12,-5533 815.12,-5533 441.12,-5533 441.12,-5533 435.12,-5533 429.12,-5527 429.12,-5521 429.12,-5521 429.12,-5457 429.12,-5457 429.12,-5451 435.12,-5445 441.12,-5445"/>
<text text-anchor="middle" x="628.12" y="-5520.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust116" class="cluster">
<title>cluster_src/server/application/commands/Match/LeaveMatch</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M449.12,-5453C449.12,-5453 807.12,-5453 807.12,-5453 813.12,-5453 819.12,-5459 819.12,-5465 819.12,-5465 819.12,-5494 819.12,-5494 819.12,-5500 813.12,-5506 807.12,-5506 807.12,-5506 449.12,-5506 449.12,-5506 443.12,-5506 437.12,-5500 437.12,-5494 437.12,-5494 437.12,-5465 437.12,-5465 437.12,-5459 443.12,-5453 449.12,-5453"/>
<text text-anchor="middle" x="628.12" y="-5493.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">LeaveMatch</text>
</g>
<g id="clust117" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M398.75,-5541C398.75,-5541 857.5,-5541 857.5,-5541 863.5,-5541 869.5,-5547 869.5,-5553 869.5,-5553 869.5,-5922 869.5,-5922 869.5,-5928 863.5,-5934 857.5,-5934 857.5,-5934 398.75,-5934 398.75,-5934 392.75,-5934 386.75,-5928 386.75,-5922 386.75,-5922 386.75,-5553 386.75,-5553 386.75,-5547 392.75,-5541 398.75,-5541"/>
<text text-anchor="middle" x="628.12" y="-5921.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchMaking</text>
</g>
<g id="clust118" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M406.75,-5549C406.75,-5549 849.5,-5549 849.5,-5549 855.5,-5549 861.5,-5555 861.5,-5561 861.5,-5561 861.5,-5590 861.5,-5590 861.5,-5596 855.5,-5602 849.5,-5602 849.5,-5602 406.75,-5602 406.75,-5602 400.75,-5602 394.75,-5596 394.75,-5590 394.75,-5590 394.75,-5561 394.75,-5561 394.75,-5555 400.75,-5549 406.75,-5549"/>
<text text-anchor="middle" x="628.12" y="-5589.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AddPlayerToMatchMakingQueue</text>
</g>
<g id="clust119" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/CancelMatchRegistration</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M422.5,-5610C422.5,-5610 833.75,-5610 833.75,-5610 839.75,-5610 845.75,-5616 845.75,-5622 845.75,-5622 845.75,-5651 845.75,-5651 845.75,-5657 839.75,-5663 833.75,-5663 833.75,-5663 422.5,-5663 422.5,-5663 416.5,-5663 410.5,-5657 410.5,-5651 410.5,-5651 410.5,-5622 410.5,-5622 410.5,-5616 416.5,-5610 422.5,-5610"/>
<text text-anchor="middle" x="628.12" y="-5650.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CancelMatchRegistration</text>
</g>
<g id="clust120" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M414.62,-5671C414.62,-5671 841.62,-5671 841.62,-5671 847.62,-5671 853.62,-5677 853.62,-5683 853.62,-5683 853.62,-5712 853.62,-5712 853.62,-5718 847.62,-5724 841.62,-5724 841.62,-5724 414.62,-5724 414.62,-5724 408.62,-5724 402.62,-5718 402.62,-5712 402.62,-5712 402.62,-5683 402.62,-5683 402.62,-5677 408.62,-5671 414.62,-5671"/>
<text text-anchor="middle" x="628.12" y="-5711.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">CleanUpMatchMakingQueue</text>
</g>
<g id="clust121" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/MakeMatch</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M450.62,-5732C450.62,-5732 805.62,-5732 805.62,-5732 811.62,-5732 817.62,-5738 817.62,-5744 817.62,-5744 817.62,-5773 817.62,-5773 817.62,-5779 811.62,-5785 805.62,-5785 805.62,-5785 450.62,-5785 450.62,-5785 444.62,-5785 438.62,-5779 438.62,-5773 438.62,-5773 438.62,-5744 438.62,-5744 438.62,-5738 444.62,-5732 450.62,-5732"/>
<text text-anchor="middle" x="628.12" y="-5772.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MakeMatch</text>
</g>
<g id="clust122" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/UpdatePlayersStatus</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M431.12,-5793C431.12,-5793 825.12,-5793 825.12,-5793 831.12,-5793 837.12,-5799 837.12,-5805 837.12,-5805 837.12,-5834 837.12,-5834 837.12,-5840 831.12,-5846 825.12,-5846 825.12,-5846 431.12,-5846 431.12,-5846 425.12,-5846 419.12,-5840 419.12,-5834 419.12,-5834 419.12,-5805 419.12,-5805 419.12,-5799 425.12,-5793 431.12,-5793"/>
<text text-anchor="middle" x="628.12" y="-5833.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">UpdatePlayersStatus</text>
</g>
<g id="clust123" class="cluster">
<title>cluster_src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M420.25,-5854C420.25,-5854 836,-5854 836,-5854 842,-5854 848,-5860 848,-5866 848,-5866 848,-5895 848,-5895 848,-5901 842,-5907 836,-5907 836,-5907 420.25,-5907 420.25,-5907 414.25,-5907 408.25,-5901 408.25,-5895 408.25,-5895 408.25,-5866 408.25,-5866 408.25,-5860 414.25,-5854 420.25,-5854"/>
<text text-anchor="middle" x="628.12" y="-5894.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">UpdateSinglePlayerStatus</text>
</g>
<g id="clust124" class="cluster">
<title>cluster_src/server/application/ports</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M680.62,-5969C680.62,-5969 823.62,-5969 823.62,-5969 829.62,-5969 835.62,-5975 835.62,-5981 835.62,-5981 835.62,-6258 835.62,-6258 835.62,-6264 829.62,-6270 823.62,-6270 823.62,-6270 680.62,-6270 680.62,-6270 674.62,-6270 668.62,-6264 668.62,-6258 668.62,-6258 668.62,-5981 668.62,-5981 668.62,-5975 674.62,-5969 680.62,-5969"/>
<text text-anchor="middle" x="752.12" y="-6257.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ports</text>
</g>
<g id="clust125" class="cluster">
<title>cluster_src/server/application/queries</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M439.75,-5969C439.75,-5969 600.75,-5969 600.75,-5969 606.75,-5969 612.75,-5975 612.75,-5981 612.75,-5981 612.75,-6134 612.75,-6134 612.75,-6140 606.75,-6146 600.75,-6146 600.75,-6146 439.75,-6146 439.75,-6146 433.75,-6146 427.75,-6140 427.75,-6134 427.75,-6134 427.75,-5981 427.75,-5981 427.75,-5975 433.75,-5969 439.75,-5969"/>
<text text-anchor="middle" x="520.25" y="-6133.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">queries</text>
</g>
<g id="clust126" class="cluster">
<title>cluster_src/server/domain</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M917.25,-6070C917.25,-6070 1270.75,-6070 1270.75,-6070 1276.75,-6070 1282.75,-6076 1282.75,-6082 1282.75,-6082 1282.75,-6644 1282.75,-6644 1282.75,-6650 1276.75,-6656 1270.75,-6656 1270.75,-6656 917.25,-6656 917.25,-6656 911.25,-6656 905.25,-6650 905.25,-6644 905.25,-6644 905.25,-6082 905.25,-6082 905.25,-6076 911.25,-6070 917.25,-6070"/>
<text text-anchor="middle" x="1094" y="-6643.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">domain</text>
</g>
<g id="clust127" class="cluster">
<title>cluster_src/server/domain/AppUser</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M971.12,-6414C971.12,-6414 1242.62,-6414 1242.62,-6414 1248.62,-6414 1254.62,-6420 1254.62,-6426 1254.62,-6426 1254.62,-6490 1254.62,-6490 1254.62,-6496 1248.62,-6502 1242.62,-6502 1242.62,-6502 971.12,-6502 971.12,-6502 965.12,-6502 959.12,-6496 959.12,-6490 959.12,-6490 959.12,-6426 959.12,-6426 959.12,-6420 965.12,-6414 971.12,-6414"/>
<text text-anchor="middle" x="1106.88" y="-6489.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AppUser</text>
</g>
<g id="clust128" class="cluster">
<title>cluster_src/server/domain/AppUser/valueObjects</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1174.12,-6422C1174.12,-6422 1234.62,-6422 1234.62,-6422 1240.62,-6422 1246.62,-6428 1246.62,-6434 1246.62,-6434 1246.62,-6463 1246.62,-6463 1246.62,-6469 1240.62,-6475 1234.62,-6475 1234.62,-6475 1174.12,-6475 1174.12,-6475 1168.12,-6475 1162.12,-6469 1162.12,-6463 1162.12,-6463 1162.12,-6434 1162.12,-6434 1162.12,-6428 1168.12,-6422 1174.12,-6422"/>
<text text-anchor="middle" x="1204.38" y="-6462.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">valueObjects</text>
</g>
<g id="clust129" class="cluster">
<title>cluster_src/server/domain/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M938.75,-6231C938.75,-6231 1055.75,-6231 1055.75,-6231 1061.75,-6231 1067.75,-6237 1067.75,-6243 1067.75,-6243 1067.75,-6333 1067.75,-6333 1067.75,-6339 1061.75,-6345 1055.75,-6345 1055.75,-6345 938.75,-6345 938.75,-6345 932.75,-6345 926.75,-6339 926.75,-6333 926.75,-6333 926.75,-6243 926.75,-6243 926.75,-6237 932.75,-6231 938.75,-6231"/>
<text text-anchor="middle" x="997.25" y="-6332.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust130" class="cluster">
<title>cluster_src/server/domain/Deck/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M946.75,-6239C946.75,-6239 1047.75,-6239 1047.75,-6239 1053.75,-6239 1059.75,-6245 1059.75,-6251 1059.75,-6251 1059.75,-6280 1059.75,-6280 1059.75,-6286 1053.75,-6292 1047.75,-6292 1047.75,-6292 946.75,-6292 946.75,-6292 940.75,-6292 934.75,-6286 934.75,-6280 934.75,-6280 934.75,-6251 934.75,-6251 934.75,-6245 940.75,-6239 946.75,-6239"/>
<text text-anchor="middle" x="997.25" y="-6279.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust131" class="cluster">
<title>cluster_src/server/domain/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M925.25,-6078C925.25,-6078 1069.25,-6078 1069.25,-6078 1075.25,-6078 1081.25,-6084 1081.25,-6090 1081.25,-6090 1081.25,-6211 1081.25,-6211 1081.25,-6217 1075.25,-6223 1069.25,-6223 1069.25,-6223 925.25,-6223 925.25,-6223 919.25,-6223 913.25,-6217 913.25,-6211 913.25,-6211 913.25,-6090 913.25,-6090 913.25,-6084 919.25,-6078 925.25,-6078"/>
<text text-anchor="middle" x="997.25" y="-6210.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust132" class="cluster">
<title>cluster_src/server/domain/Match/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M933.25,-6086C933.25,-6086 1061.25,-6086 1061.25,-6086 1067.25,-6086 1073.25,-6092 1073.25,-6098 1073.25,-6098 1073.25,-6158 1073.25,-6158 1073.25,-6164 1067.25,-6170 1061.25,-6170 1061.25,-6170 933.25,-6170 933.25,-6170 927.25,-6170 921.25,-6164 921.25,-6158 921.25,-6158 921.25,-6098 921.25,-6098 921.25,-6092 927.25,-6086 933.25,-6086"/>
<text text-anchor="middle" x="997.25" y="-6157.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust133" class="cluster">
<title>cluster_src/server/domain/MatchmakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M947.5,-6353C947.5,-6353 1262.75,-6353 1262.75,-6353 1268.75,-6353 1274.75,-6359 1274.75,-6365 1274.75,-6365 1274.75,-6394 1274.75,-6394 1274.75,-6400 1268.75,-6406 1262.75,-6406 1262.75,-6406 947.5,-6406 947.5,-6406 941.5,-6406 935.5,-6400 935.5,-6394 935.5,-6394 935.5,-6365 935.5,-6365 935.5,-6359 941.5,-6353 947.5,-6353"/>
<text text-anchor="middle" x="1105.12" y="-6393.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchmakingQueue</text>
</g>
<g id="clust134" class="cluster">
<title>cluster_src/server/domain/User</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M926,-6510C926,-6510 1068.5,-6510 1068.5,-6510 1074.5,-6510 1080.5,-6516 1080.5,-6522 1080.5,-6522 1080.5,-6617 1080.5,-6617 1080.5,-6623 1074.5,-6629 1068.5,-6629 1068.5,-6629 926,-6629 926,-6629 920,-6629 914,-6623 914,-6617 914,-6617 914,-6522 914,-6522 914,-6516 920,-6510 926,-6510"/>
<text text-anchor="middle" x="997.25" y="-6616.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">User</text>
</g>
<g id="clust135" class="cluster">
<title>cluster_src/server/domain/User/errors</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M934,-6518C934,-6518 1060.5,-6518 1060.5,-6518 1066.5,-6518 1072.5,-6524 1072.5,-6530 1072.5,-6530 1072.5,-6590 1072.5,-6590 1072.5,-6596 1066.5,-6602 1060.5,-6602 1060.5,-6602 934,-6602 934,-6602 928,-6602 922,-6596 922,-6590 922,-6590 922,-6530 922,-6530 922,-6524 928,-6518 934,-6518"/>
<text text-anchor="middle" x="997.25" y="-6589.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">errors</text>
</g>
<g id="clust136" class="cluster">
<title>cluster_src/server/infrastructure</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M412.5,-6305C412.5,-6305 628,-6305 628,-6305 634,-6305 640,-6311 640,-6317 640,-6317 640,-7032 640,-7032 640,-7038 634,-7044 628,-7044 628,-7044 412.5,-7044 412.5,-7044 406.5,-7044 400.5,-7038 400.5,-7032 400.5,-7032 400.5,-6317 400.5,-6317 400.5,-6311 406.5,-6305 412.5,-6305"/>
<text text-anchor="middle" x="520.25" y="-7031.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">infrastructure</text>
</g>
<g id="clust137" class="cluster">
<title>cluster_src/server/infrastructure/IdentityProvider</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M470.5,-6313C470.5,-6313 570,-6313 570,-6313 576,-6313 582,-6319 582,-6325 582,-6325 582,-6354 582,-6354 582,-6360 576,-6366 570,-6366 570,-6366 470.5,-6366 470.5,-6366 464.5,-6366 458.5,-6360 458.5,-6354 458.5,-6354 458.5,-6325 458.5,-6325 458.5,-6319 464.5,-6313 470.5,-6313"/>
<text text-anchor="middle" x="520.25" y="-6353.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">IdentityProvider</text>
</g>
<g id="clust138" class="cluster">
<title>cluster_src/server/infrastructure/gateways</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M441.12,-6374C441.12,-6374 599.38,-6374 599.38,-6374 605.38,-6374 611.38,-6380 611.38,-6386 611.38,-6386 611.38,-6572 611.38,-6572 611.38,-6578 605.38,-6584 599.38,-6584 599.38,-6584 441.12,-6584 441.12,-6584 435.12,-6584 429.12,-6578 429.12,-6572 429.12,-6572 429.12,-6386 429.12,-6386 429.12,-6380 435.12,-6374 441.12,-6374"/>
<text text-anchor="middle" x="520.25" y="-6571.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">gateways</text>
</g>
<g id="clust139" class="cluster">
<title>cluster_src/server/infrastructure/gateways/AuthenticationGateway</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M449.12,-6504C449.12,-6504 591.38,-6504 591.38,-6504 597.38,-6504 603.38,-6510 603.38,-6516 603.38,-6516 603.38,-6545 603.38,-6545 603.38,-6551 597.38,-6557 591.38,-6557 591.38,-6557 449.12,-6557 449.12,-6557 443.12,-6557 437.12,-6551 437.12,-6545 437.12,-6545 437.12,-6516 437.12,-6516 437.12,-6510 443.12,-6504 449.12,-6504"/>
<text text-anchor="middle" x="520.25" y="-6544.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AuthenticationGateway</text>
</g>
<g id="clust140" class="cluster">
<title>cluster_src/server/infrastructure/gateways/Context</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M480.25,-6382C480.25,-6382 560.25,-6382 560.25,-6382 566.25,-6382 572.25,-6388 572.25,-6394 572.25,-6394 572.25,-6423 572.25,-6423 572.25,-6429 566.25,-6435 560.25,-6435 560.25,-6435 480.25,-6435 480.25,-6435 474.25,-6435 468.25,-6429 468.25,-6423 468.25,-6423 468.25,-6394 468.25,-6394 468.25,-6388 474.25,-6382 480.25,-6382"/>
<text text-anchor="middle" x="520.25" y="-6422.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Context</text>
</g>
<g id="clust141" class="cluster">
<title>cluster_src/server/infrastructure/gateways/Time</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M476.5,-6443C476.5,-6443 564,-6443 564,-6443 570,-6443 576,-6449 576,-6455 576,-6455 576,-6484 576,-6484 576,-6490 570,-6496 564,-6496 564,-6496 476.5,-6496 476.5,-6496 470.5,-6496 464.5,-6490 464.5,-6484 464.5,-6484 464.5,-6455 464.5,-6455 464.5,-6449 470.5,-6443 476.5,-6443"/>
<text text-anchor="middle" x="520.25" y="-6483.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Time</text>
</g>
<g id="clust142" class="cluster">
<title>cluster_src/server/infrastructure/repositories</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M420.5,-6592C420.5,-6592 620,-6592 620,-6592 626,-6592 632,-6598 632,-6604 632,-6604 632,-7005 632,-7005 632,-7011 626,-7017 620,-7017 620,-7017 420.5,-7017 420.5,-7017 414.5,-7017 408.5,-7011 408.5,-7005 408.5,-7005 408.5,-6604 408.5,-6604 408.5,-6598 414.5,-6592 420.5,-6592"/>
<text text-anchor="middle" x="520.25" y="-7004.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">repositories</text>
</g>
<g id="clust143" class="cluster">
<title>cluster_src/server/infrastructure/repositories/AppUser</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M456.25,-6876C456.25,-6876 584.25,-6876 584.25,-6876 590.25,-6876 596.25,-6882 596.25,-6888 596.25,-6888 596.25,-6917 596.25,-6917 596.25,-6923 590.25,-6929 584.25,-6929 584.25,-6929 456.25,-6929 456.25,-6929 450.25,-6929 444.25,-6923 444.25,-6917 444.25,-6917 444.25,-6888 444.25,-6888 444.25,-6882 450.25,-6876 456.25,-6876"/>
<text text-anchor="middle" x="520.25" y="-6916.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">AppUser</text>
</g>
<g id="clust144" class="cluster">
<title>cluster_src/server/infrastructure/repositories/Deck</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M463.75,-6815C463.75,-6815 576.75,-6815 576.75,-6815 582.75,-6815 588.75,-6821 588.75,-6827 588.75,-6827 588.75,-6856 588.75,-6856 588.75,-6862 582.75,-6868 576.75,-6868 576.75,-6868 463.75,-6868 463.75,-6868 457.75,-6868 451.75,-6862 451.75,-6856 451.75,-6856 451.75,-6827 451.75,-6827 451.75,-6821 457.75,-6815 463.75,-6815"/>
<text text-anchor="middle" x="520.25" y="-6855.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Deck</text>
</g>
<g id="clust145" class="cluster">
<title>cluster_src/server/infrastructure/repositories/InMemory</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M428.5,-6661C428.5,-6661 612,-6661 612,-6661 618,-6661 624,-6667 624,-6673 624,-6673 624,-6795 624,-6795 624,-6801 618,-6807 612,-6807 612,-6807 428.5,-6807 428.5,-6807 422.5,-6807 416.5,-6801 416.5,-6795 416.5,-6795 416.5,-6673 416.5,-6673 416.5,-6667 422.5,-6661 428.5,-6661"/>
<text text-anchor="middle" x="520.25" y="-6794.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">InMemory</text>
</g>
<g id="clust146" class="cluster">
<title>cluster_src/server/infrastructure/repositories/Match</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M461.88,-6600C461.88,-6600 578.62,-6600 578.62,-6600 584.62,-6600 590.62,-6606 590.62,-6612 590.62,-6612 590.62,-6641 590.62,-6641 590.62,-6647 584.62,-6653 578.62,-6653 578.62,-6653 461.88,-6653 461.88,-6653 455.88,-6653 449.88,-6647 449.88,-6641 449.88,-6641 449.88,-6612 449.88,-6612 449.88,-6606 455.88,-6600 461.88,-6600"/>
<text text-anchor="middle" x="520.25" y="-6640.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Match</text>
</g>
<g id="clust147" class="cluster">
<title>cluster_src/server/infrastructure/repositories/MatchmakingQueue</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M433,-6937C433,-6937 607.5,-6937 607.5,-6937 613.5,-6937 619.5,-6943 619.5,-6949 619.5,-6949 619.5,-6978 619.5,-6978 619.5,-6984 613.5,-6990 607.5,-6990 607.5,-6990 433,-6990 433,-6990 427,-6990 421,-6984 421,-6978 421,-6978 421,-6949 421,-6949 421,-6943 427,-6937 433,-6937"/>
<text text-anchor="middle" x="520.25" y="-6977.45" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MatchmakingQueue</text>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx -->
<g id="node1" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/loading.tsx</title>
<g id="a_node1"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/loading.tsx" xlink:title="loading.tsx">
<path fill="#ccffcc" stroke="black" d="M118.08,-7738.25C118.08,-7738.25 70.17,-7738.25 70.17,-7738.25 67.08,-7738.25 64,-7735.17 64,-7732.08 64,-7732.08 64,-7725.92 64,-7725.92 64,-7722.83 67.08,-7719.75 70.17,-7719.75 70.17,-7719.75 118.08,-7719.75 118.08,-7719.75 121.17,-7719.75 124.25,-7722.83 124.25,-7725.92 124.25,-7725.92 124.25,-7732.08 124.25,-7732.08 124.25,-7735.17 121.17,-7738.25 118.08,-7738.25"/>
<text text-anchor="start" x="72" y="-7725.7" font-family="Helvetica,sans-Serif" font-size="9.00">loading.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx -->
<g id="node2" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx</title>
<g id="a_node2"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/(connected)/games/[gameId]/deck-builder/[deckId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7769.25C114.96,-7769.25 73.29,-7769.25 73.29,-7769.25 70.21,-7769.25 67.12,-7766.17 67.12,-7763.08 67.12,-7763.08 67.12,-7756.92 67.12,-7756.92 67.12,-7753.83 70.21,-7750.75 73.29,-7750.75 73.29,-7750.75 114.96,-7750.75 114.96,-7750.75 118.04,-7750.75 121.12,-7753.83 121.12,-7756.92 121.12,-7756.92 121.12,-7763.08 121.12,-7763.08 121.12,-7766.17 118.04,-7769.25 114.96,-7769.25"/>
<text text-anchor="start" x="76.88" y="-7756.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="node3" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<g id="a_node3"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx" xlink:title="DeckBuilderCardsGrid.tsx">
<polygon fill="#6cbaff" stroke="black" points="581.12,-3773.25 459.38,-3773.25 459.38,-3754.75 581.12,-3754.75 581.12,-3773.25"/>
<text text-anchor="start" x="467.38" y="-3760.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderCardsGrid.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="edge1" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.51,-7762.2C138.75,-7762.05 160.22,-7758.54 172.25,-7744 189.64,-7722.98 162.33,-7273.57 180.25,-7253 231.81,-7193.83 303.61,-7285.52 354.75,-7226 370.07,-7208.17 350.25,-3859.92 362.75,-3840 384.23,-3805.78 426,-3786.57 460.98,-3776.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="461.15,-3778.16 466.34,-3774.49 460,-3774.13 461.15,-3778.16"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="node4" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<g id="a_node4"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx" xlink:title="DeckBuilderPanel.tsx">
<polygon fill="#6cbaff" stroke="black" points="572.12,-3651.25 468.38,-3651.25 468.38,-3632.75 572.12,-3632.75 572.12,-3651.25"/>
<text text-anchor="start" x="476.38" y="-3638.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderPanel.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="edge2" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.51,-7762.21C138.75,-7762.06 160.23,-7758.54 172.25,-7744 190.55,-7721.86 161.39,-7248.66 180.25,-7227 231.78,-7167.81 303.61,-7259.53 354.75,-7200 370.67,-7181.47 347.81,-3702.32 362.75,-3683 385.07,-3654.14 425.03,-3644.01 458.99,-3641.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="459.07,-3643.14 464.91,-3640.62 458.77,-3638.95 459.07,-3643.14"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="node5" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<g id="a_node5"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx" xlink:title="DeckBuildingFilters.tsx">
<polygon fill="#6cbaff" stroke="black" points="574.75,-3468.25 465.75,-3468.25 465.75,-3449.75 574.75,-3449.75 574.75,-3468.25"/>
<text text-anchor="start" x="473.75" y="-3455.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingFilters.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="edge3" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.52,-7762.21C138.76,-7762.06 160.23,-7758.55 172.25,-7744 191.47,-7720.74 160.45,-7223.76 180.25,-7201 231.76,-7141.79 303.61,-7233.53 354.75,-7174 370.82,-7155.29 353.52,-3644.87 362.75,-3622 390.1,-3554.24 460.49,-3498.8 496.82,-3473.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="497.75,-3475.53 501.52,-3470.41 495.38,-3472.05 497.75,-3475.53"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="node6" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<g id="a_node6"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx" xlink:title="DeckDraftInitializer.tsx">
<polygon fill="#6cbaff" stroke="black" points="573.25,-3285.25 467.25,-3285.25 467.25,-3266.75 573.25,-3266.75 573.25,-3285.25"/>
<text text-anchor="start" x="475.25" y="-3272.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckDraftInitializer.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="edge4" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.52,-7762.21C138.77,-7762.07 160.24,-7758.56 172.25,-7744 177.51,-7737.63 180,-7157.26 180.25,-7149 181.88,-7095.74 329.2,-3358.39 362.75,-3317 385.4,-3289.06 424.55,-3278.81 458.02,-3275.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="458.02,-3277.66 463.83,-3275.08 457.68,-3273.47 458.02,-3277.66"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="node7" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<g id="a_node7"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx" xlink:title="EditDeckInitializer.tsx">
<polygon fill="#6cbaff" stroke="black" points="571.38,-3346.25 469.12,-3346.25 469.12,-3327.75 571.38,-3327.75 571.38,-3346.25"/>
<text text-anchor="start" x="477.12" y="-3333.7" font-family="Helvetica,sans-Serif" font-size="9.00">EditDeckInitializer.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="edge5" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/[deckId]/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.52,-7762.21C138.76,-7762.07 160.24,-7758.55 172.25,-7744 192.38,-7719.62 159.51,-7198.86 180.25,-7175 231.74,-7115.78 303.61,-7207.53 354.75,-7148 371.81,-7128.14 346.73,-3398.71 362.75,-3378 385.23,-3348.94 425.6,-3338.87 459.7,-3335.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="459.82,-3338.07 465.66,-3335.57 459.53,-3333.88 459.82,-3338.07"/>
</g>
<!-- src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="node102" class="node">
<title>src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<g id="a_node102"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/hooks/useLocale/useLocale.ts" xlink:title="useLocale.ts">
<polygon fill="#6cbaff" stroke="black" points="1031.12,-4874.25 963.38,-4874.25 963.38,-4855.75 1031.12,-4855.75 1031.12,-4874.25"/>
<text text-anchor="start" x="971.38" y="-4861.7" font-family="Helvetica,sans-Serif" font-size="9.00">useLocale.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge142" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M550.57,-3773.74C578.94,-3784.87 620.43,-3806.08 640,-3840 653.28,-3863.03 632.33,-4059.83 650.75,-4079 687.02,-4116.75 849.94,-4060.57 885.5,-4099 899.54,-4114.17 878.03,-4827.01 891.12,-4843 906.02,-4861.2 931.76,-4866.8 954.03,-4867.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.94,-4869.91 959.98,-4867.92 954.02,-4865.71 953.94,-4869.91"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx -->
<g id="node105" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx</title>
<g id="a_node105"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx" xlink:title="DeckBuildingCard.tsx">
<polygon fill="#6cbaff" stroke="black" points="804.38,-3773.25 699.88,-3773.25 699.88,-3754.75 804.38,-3754.75 804.38,-3773.25"/>
<text text-anchor="start" x="707.88" y="-3760.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx -->
<g id="edge136" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.35,-3764C615.05,-3764 656.97,-3764 690.72,-3764"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="690.54,-3766.1 696.54,-3764 690.54,-3761.9 690.54,-3766.1"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="node106" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<g id="a_node106"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx" xlink:title="TotalCardsInDeckCounter.tsx">
<polygon fill="#6cbaff" stroke="black" points="819.38,-3712.25 684.88,-3712.25 684.88,-3693.75 819.38,-3693.75 819.38,-3712.25"/>
<text text-anchor="start" x="692.88" y="-3699.7" font-family="Helvetica,sans-Serif" font-size="9.00">TotalCardsInDeckCounter.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="edge137" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.45,-3754.71C600.33,-3751.3 621.16,-3747.02 640,-3742 667.03,-3734.8 696.92,-3724.15 718.88,-3715.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="719.51,-3717.81 724.36,-3713.7 718,-3713.89 719.51,-3717.81"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx -->
<g id="node107" class="node">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx</title>
<g id="a_node107"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx" xlink:title="useCardsByGameId.tsx">
<polygon fill="#6cbaff" stroke="black" points="1053.25,-4935.25 941.25,-4935.25 941.25,-4916.75 1053.25,-4916.75 1053.25,-4935.25"/>
<text text-anchor="start" x="949.25" y="-4922.7" font-family="Helvetica,sans-Serif" font-size="9.00">useCardsByGameId.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx -->
<g id="edge138" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M550.59,-3773.72C578.97,-3784.85 620.47,-3806.05 640,-3840 654.03,-3864.39 631.2,-4072.77 650.75,-4093 687.11,-4130.62 849.81,-4072.74 885.5,-4111 900.53,-4127.11 877.17,-4886.95 891.12,-4904 901.29,-4916.42 916.5,-4922.97 932.21,-4926.21"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="931.72,-4928.25 937.98,-4927.17 932.41,-4924.11 931.72,-4928.25"/>
</g>
<!-- src/client/infrastructure/hooks/useDebounce/index.ts -->
<g id="node108" class="node">
<title>src/client/infrastructure/hooks/useDebounce/index.ts</title>
<g id="a_node108"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/hooks/useDebounce/index.ts" xlink:title="index.ts">
<polygon fill="#6cbaff" stroke="black" points="1024.25,-4508.25 970.25,-4508.25 970.25,-4489.75 1024.25,-4489.75 1024.25,-4508.25"/>
<text text-anchor="start" x="981.5" y="-4495.7" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDebounce/index.ts -->
<g id="edge139" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDebounce/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M550.26,-3773.66C578.6,-3784.78 620.27,-3806.03 640,-3840 651.79,-3860.3 634.44,-4034.12 650.75,-4051 687.13,-4088.65 849.82,-4032.69 885.5,-4071 900.87,-4087.51 876.78,-4459.59 891.12,-4477 907.74,-4497.16 937.65,-4501.84 961.33,-4501.95"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.08,-4504.06 967.04,-4501.85 961,-4499.86 961.08,-4504.06"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="node109" class="node">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<g id="a_node109"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts" xlink:title="useDeckBuilder.ts">
<polygon fill="#6cbaff" stroke="black" points="1042,-4447.25 952.5,-4447.25 952.5,-4428.75 1042,-4428.75 1042,-4447.25"/>
<text text-anchor="start" x="960.5" y="-4434.7" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge140" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M550.23,-3773.67C578.56,-3784.81 620.21,-3806.06 640,-3840 651.04,-3858.94 635.49,-4021.26 650.75,-4037 687.2,-4074.58 849.79,-4018.71 885.5,-4057 899.45,-4071.95 877.47,-4409.78 891.12,-4425 904.01,-4439.35 923.98,-4444.13 943.07,-4444.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="943.02,-4446.86 949.02,-4444.77 943.02,-4442.66 943.02,-4446.86"/>
</g>
<!-- src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="node110" class="node">
<title>src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<g id="a_node110"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/hooks/useGameId/useGameId.ts" xlink:title="useGameId.ts">
<polygon fill="#6cbaff" stroke="black" points="1033.38,-4691.25 961.12,-4691.25 961.12,-4672.75 1033.38,-4672.75 1033.38,-4691.25"/>
<text text-anchor="start" x="969.12" y="-4678.7" font-family="Helvetica,sans-Serif" font-size="9.00">useGameId.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge141" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M550.55,-3773.75C578.9,-3784.89 620.38,-3806.1 640,-3840 652.54,-3861.66 633.38,-4046.98 650.75,-4065 687.08,-4102.7 849.9,-4046.61 885.5,-4085 896.36,-4096.71 880.99,-4647.65 891.12,-4660 905.57,-4677.61 930.17,-4683.42 951.95,-4684.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="951.66,-4686.78 957.72,-4684.87 951.79,-4682.58 951.66,-4686.78"/>
</g>
<!-- src/client/infrastructure/hooks/useResponsiveColumnCount.tsx -->
<g id="node111" class="node">
<title>src/client/infrastructure/hooks/useResponsiveColumnCount.tsx</title>
<g id="a_node111"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/hooks/useResponsiveColumnCount.tsx" xlink:title="useResponsiveColumnCount.tsx">
<polygon fill="#6cbaff" stroke="black" points="1072.38,-4413.25 922.12,-4413.25 922.12,-4394.75 1072.38,-4394.75 1072.38,-4413.25"/>
<text text-anchor="start" x="930.12" y="-4400.7" font-family="Helvetica,sans-Serif" font-size="9.00">useResponsiveColumnCount.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useResponsiveColumnCount.tsx -->
<g id="edge143" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/hooks/useResponsiveColumnCount.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M550.2,-3773.69C578.5,-3784.84 620.14,-3806.11 640,-3840 650.3,-3857.57 636.54,-4008.41 650.75,-4023 687.28,-4060.5 844.76,-4010.12 885.5,-4043 941.17,-4087.93 982.42,-4318.67 993.37,-4385.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="991.26,-4385.79 994.28,-4391.38 995.4,-4385.13 991.26,-4385.79"/>
</g>
<!-- src/client/infrastructure/lib/chunk.ts -->
<g id="node112" class="node">
<title>src/client/infrastructure/lib/chunk.ts</title>
<g id="a_node112"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/lib/chunk.ts" xlink:title="chunk.ts">
<polygon fill="#6cbaff" stroke="black" points="1231.38,-4321.25 1177.38,-4321.25 1177.38,-4302.75 1231.38,-4302.75 1231.38,-4321.25"/>
<text text-anchor="start" x="1187.5" y="-4308.7" font-family="Helvetica,sans-Serif" font-size="9.00">chunk.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/lib/chunk.ts -->
<g id="edge144" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx&#45;&gt;src/client/infrastructure/lib/chunk.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M537.24,-3773.72C560.28,-3787.83 603.99,-3815.04 640,-3840 752.06,-3917.69 787.29,-3929.41 885.5,-4024 997.53,-4131.91 971.25,-4217.86 1105.75,-4296 1124.6,-4306.95 1148.75,-4310.97 1168.31,-4312.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1168.11,-4314.34 1174.2,-4312.52 1168.3,-4310.15 1168.11,-4314.34"/>
</g>
<!-- src/client/application/appStore.ts -->
<g id="node33" class="node">
<title>src/client/application/appStore.ts</title>
<g id="a_node33"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/appStore.ts" xlink:title="appStore.ts">
<polygon fill="#fb6969" stroke="black" points="1589.12,-1983.25 1526.62,-1983.25 1526.62,-1964.75 1589.12,-1964.75 1589.12,-1983.25"/>
<text text-anchor="start" x="1534.62" y="-1970.7" font-family="Helvetica,sans-Serif" font-size="9.00">appStore.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/appStore.ts -->
<g id="edge147" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M533.37,-3632.45C558.96,-3611.44 617.92,-3558.24 640,-3498 655.66,-3455.28 634.31,-2721.43 650.75,-2679 734.3,-2463.39 765.01,-2290 996.25,-2290 996.25,-2290 996.25,-2290 1205.38,-2290 1331.5,-2290 1388.9,-2292.62 1474.5,-2200 1482.13,-2191.74 1478.78,-2186.61 1482.5,-2176 1506.45,-2107.69 1536.74,-2027.11 1550.06,-1991.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1551.97,-1992.82 1552.13,-1986.46 1548.04,-1991.33 1551.97,-1992.82"/>
</g>
<!-- src/client/application/commands/clearDeckDraft/clearDeckDraft.ts -->
<g id="node46" class="node">
<title>src/client/application/commands/clearDeckDraft/clearDeckDraft.ts</title>
<g id="a_node46"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/clearDeckDraft/clearDeckDraft.ts" xlink:title="clearDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1247.25,-1162.25 1161.5,-1162.25 1161.5,-1143.75 1247.25,-1143.75 1247.25,-1162.25"/>
<text text-anchor="start" x="1169.5" y="-1149.7" font-family="Helvetica,sans-Serif" font-size="9.00">clearDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/commands/clearDeckDraft/clearDeckDraft.ts -->
<g id="edge148" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/commands/clearDeckDraft/clearDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M533.39,-3632.46C559.01,-3611.46 618.04,-3558.28 640,-3498 651.25,-3467.12 634.52,-2343.58 650.75,-2315 711.07,-2208.76 788.46,-2246.22 885.5,-2172 987.6,-2093.9 1043.82,-2090.75 1095.38,-1973 1112.73,-1933.35 1081.55,-1229.89 1105.75,-1194 1116.58,-1177.94 1135.03,-1168.16 1153,-1162.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1153.15,-1164.35 1158.29,-1160.6 1151.94,-1160.33 1153.15,-1164.35"/>
</g>
<!-- src/client/application/queries/hasDeckDraft.ts -->
<g id="node90" class="node">
<title>src/client/application/queries/hasDeckDraft.ts</title>
<g id="a_node90"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/hasDeckDraft.ts" xlink:title="hasDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1421,-1403.25 1340.5,-1403.25 1340.5,-1384.75 1421,-1384.75 1421,-1403.25"/>
<text text-anchor="start" x="1348.5" y="-1390.7" font-family="Helvetica,sans-Serif" font-size="9.00">hasDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/queries/hasDeckDraft.ts -->
<g id="edge149" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/application/queries/hasDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M533.31,-3632.43C558.77,-3611.37 617.5,-3558.08 640,-3498 655.44,-3456.78 637.23,-3143.89 650.75,-3102 761.2,-2759.8 871.12,-2705.49 1105.75,-2433 1180.5,-2346.19 1248.6,-2362.74 1295,-2258 1304.54,-2236.46 1288.05,-1428.21 1303,-1410 1310.09,-1401.37 1320.52,-1396.62 1331.42,-1394.17"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1331.67,-1396.25 1337.22,-1393.17 1330.96,-1392.11 1331.67,-1396.25"/>
</g>
<!-- src/client/infrastructure/builders/urlBuilder.ts -->
<g id="node96" class="node">
<title>src/client/infrastructure/builders/urlBuilder.ts</title>
<g id="a_node96"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/builders/urlBuilder.ts" xlink:title="urlBuilder.ts">
<polygon fill="#6cbaff" stroke="black" points="1029.25,-5023.25 965.25,-5023.25 965.25,-5004.75 1029.25,-5004.75 1029.25,-5023.25"/>
<text text-anchor="start" x="973.25" y="-5010.7" font-family="Helvetica,sans-Serif" font-size="9.00">urlBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge150" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.38,-3646.5C596.89,-3651.48 624.28,-3661.8 640,-3683 658.81,-3708.36 628.71,-3944.38 650.75,-3967 687.23,-4004.44 849.63,-3943.98 885.5,-3982 894.87,-3991.93 883.76,-4953.51 891.12,-4965 905.49,-4987.4 933.02,-4999.73 956.31,-5006.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="955.73,-5008.43 962.07,-5007.94 956.8,-5004.37 955.73,-5008.43"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge157" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.36,-3646.51C596.87,-3651.5 624.26,-3661.82 640,-3683 657.84,-3707.01 630.13,-3930.33 650.75,-3952 686.86,-3989.94 850.03,-3934.47 885.5,-3973 893.68,-3981.89 883.48,-4833.65 891.12,-4843 906.01,-4861.2 931.75,-4866.81 954.03,-4867.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.94,-4869.91 959.98,-4867.93 954.02,-4865.71 953.94,-4869.91"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx -->
<g id="edge153" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M551.16,-3651.72C577.29,-3660.04 616.32,-3672.06 650.75,-3681 664.83,-3684.66 680.13,-3688.22 694.44,-3691.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="693.94,-3693.41 700.25,-3692.64 694.83,-3689.31 693.94,-3693.41"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge154" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.58,-3646.63C596.99,-3651.66 624.22,-3661.98 640,-3683 654.56,-3702.4 634.21,-3883.27 650.75,-3901 722.42,-3977.83 815.3,-3850.82 885.5,-3929 894.71,-3939.25 881.94,-4414.73 891.12,-4425 903.98,-4439.38 923.95,-4444.16 943.05,-4444.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="942.99,-4446.89 948.99,-4444.8 943,-4442.69 942.99,-4446.89"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge156" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.61,-3646.61C597.03,-3651.63 624.26,-3661.95 640,-3683 655.72,-3704.02 632.76,-3899.89 650.75,-3919 686.71,-3957.2 850.27,-3905.13 885.5,-3944 898.86,-3958.74 878.52,-4644.61 891.12,-4660 905.56,-4677.62 930.16,-4683.44 951.93,-4684.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="951.65,-4686.79 957.71,-4684.88 951.78,-4682.59 951.65,-4686.79"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx -->
<g id="node114" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx</title>
<g id="a_node114"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx" xlink:title="DeckCardRow.tsx">
<polygon fill="#6cbaff" stroke="black" points="796.5,-3651.25 707.75,-3651.25 707.75,-3632.75 796.5,-3632.75 796.5,-3651.25"/>
<text text-anchor="start" x="715.75" y="-3638.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckCardRow.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx -->
<g id="edge146" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.55,-3642C610.04,-3642 660.71,-3642 698.5,-3642"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="698.3,-3644.1 704.3,-3642 698.3,-3639.9 698.3,-3644.1"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx -->
<g id="node115" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx</title>
<g id="a_node115"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx" xlink:title="DeckCardDetailsDialog.tsx">
<polygon fill="#6cbaff" stroke="black" points="815.25,-3529.25 689,-3529.25 689,-3510.75 815.25,-3510.75 815.25,-3529.25"/>
<text text-anchor="start" x="697" y="-3516.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckCardDetailsDialog.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx -->
<g id="edge151" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M534.78,-3632.45C557.86,-3616.41 606.61,-3583.61 650.75,-3561 671.62,-3550.31 695.95,-3540.32 715.42,-3532.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="716.02,-3534.89 720.89,-3530.81 714.54,-3530.96 716.02,-3534.89"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx -->
<g id="node116" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx</title>
<g id="a_node116"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx" xlink:title="SaveDeckDialog.tsx">
<polygon fill="#6cbaff" stroke="black" points="801.38,-3590.25 702.88,-3590.25 702.88,-3571.75 801.38,-3571.75 801.38,-3590.25"/>
<text text-anchor="start" x="710.88" y="-3577.7" font-family="Helvetica,sans-Serif" font-size="9.00">SaveDeckDialog.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx -->
<g id="edge152" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/SaveDeckDialog/SaveDeckDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M557.93,-3632.27C598.13,-3621.6 662.66,-3604.48 705.95,-3592.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="706.39,-3595.04 711.65,-3591.47 705.31,-3590.98 706.39,-3595.04"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckId/useDeckId.ts -->
<g id="node117" class="node">
<title>src/client/infrastructure/hooks/useDeckId/useDeckId.ts</title>
<g id="a_node117"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/hooks/useDeckId/useDeckId.ts" xlink:title="useDeckId.ts">
<polygon fill="#6cbaff" stroke="black" points="1031.5,-4752.25 963,-4752.25 963,-4733.75 1031.5,-4733.75 1031.5,-4752.25"/>
<text text-anchor="start" x="971" y="-4739.7" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckId.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckId/useDeckId.ts -->
<g id="edge155" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckId/useDeckId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.34,-3646.53C596.84,-3651.52 624.23,-3661.84 640,-3683 656.81,-3705.56 631.41,-3915.56 650.75,-3936 686.78,-3974.07 850.15,-3920.3 885.5,-3959 899.78,-3974.63 877.72,-4704.62 891.12,-4721 905.92,-4739.07 931.4,-4744.73 953.57,-4745.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.43,-4747.89 959.48,-4745.92 953.53,-4743.69 953.43,-4747.89"/>
</g>
<!-- src/client/infrastructure/providers/ToastProvider.tsx -->
<g id="node118" class="node">
<title>src/client/infrastructure/providers/ToastProvider.tsx</title>
<g id="a_node118"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/providers/ToastProvider.tsx" xlink:title="ToastProvider.tsx">
<polygon fill="#6cbaff" stroke="black" points="794.62,-4298.25 709.62,-4298.25 709.62,-4279.75 794.62,-4279.75 794.62,-4298.25"/>
<text text-anchor="start" x="717.62" y="-4285.7" font-family="Helvetica,sans-Serif" font-size="9.00">ToastProvider.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx -->
<g id="edge158" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.55,-3646.37C597.11,-3651.32 624.49,-3661.65 640,-3683 659.37,-3709.66 628.78,-4251.45 650.75,-4276 663.04,-4289.73 682.09,-4294.39 700.32,-4295.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="700.12,-4297.21 706.14,-4295.15 700.15,-4293.01 700.12,-4297.21"/>
</g>
<!-- src/client/infrastructure/store.ts -->
<g id="node119" class="node">
<title>src/client/infrastructure/store.ts</title>
<g id="a_node119"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/store.ts" xlink:title="store.ts">
<polygon fill="#6cbaff" stroke="black" points="1024.25,-5076.25 970.25,-5076.25 970.25,-5057.75 1024.25,-5057.75 1024.25,-5076.25"/>
<text text-anchor="start" x="982.62" y="-5063.7" font-family="Helvetica,sans-Serif" font-size="9.00">store.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/store.ts -->
<g id="edge159" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx&#45;&gt;src/client/infrastructure/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M572.39,-3646.49C596.91,-3651.47 624.3,-3661.79 640,-3683 659.58,-3709.45 639.21,-3948.18 650.75,-3979 708.92,-4134.29 829.6,-4112.88 885.5,-4269 889.17,-4279.25 883.92,-5044.83 891.12,-5053 908.01,-5072.14 937.42,-5074.66 960.85,-5072.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.04,-5075.03 966.81,-5072.36 960.64,-5070.85 961.04,-5075.03"/>
</g>
<!-- src/client/domain/Catalog/Filter.ts -->
<g id="node72" class="node">
<title>src/client/domain/Catalog/Filter.ts</title>
<g id="a_node72"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/Catalog/Filter.ts" xlink:title="Filter.ts">
<polygon fill="#fa9f36" stroke="black" points="1958.5,-448.25 1904.5,-448.25 1904.5,-429.75 1958.5,-429.75 1958.5,-448.25"/>
<text text-anchor="start" x="1917.25" y="-435.7" font-family="Helvetica,sans-Serif" font-size="9.00">Filter.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge164" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M531.89,-3468.74C556.63,-3492.21 617.63,-3554.91 640,-3622 653.5,-3662.48 644.26,-4347.83 650.75,-4390 707.31,-4757.79 593.45,-4968.72 891.12,-5192 1127.83,-5369.55 1260.98,-5197 1556.88,-5197 1556.88,-5197 1556.88,-5197 1688.38,-5197 1716.78,-5197 1727.87,-5202.76 1751.5,-5187 1833.54,-5132.28 1840.25,-5092.71 1867.75,-4998 1869.87,-4990.68 1926.06,-772.29 1930.25,-457.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1932.35,-457.73 1930.33,-451.7 1928.15,-457.67 1932.35,-457.73"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge166" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M531.33,-3468.46C555.39,-3491.81 616,-3555.1 640,-3622 653.61,-3659.93 627.49,-3770.1 650.75,-3803 716.13,-3895.45 822.39,-3807.99 885.5,-3902 893.6,-3914.06 881.44,-4414.17 891.12,-4425 903.98,-4439.38 923.95,-4444.16 943.04,-4444.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="942.99,-4446.89 948.99,-4444.8 942.99,-4442.69 942.99,-4446.89"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx -->
<g id="node120" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx</title>
<g id="a_node120"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx" xlink:title="FilterItem.tsx">
<polygon fill="#6cbaff" stroke="black" points="785.62,-3468.25 718.62,-3468.25 718.62,-3449.75 785.62,-3449.75 785.62,-3468.25"/>
<text text-anchor="start" x="726.62" y="-3455.7" font-family="Helvetica,sans-Serif" font-size="9.00">FilterItem.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx -->
<g id="edge165" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M575.15,-3459C616.18,-3459 671.83,-3459 709.48,-3459"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="709.15,-3461.1 715.15,-3459 709.15,-3456.9 709.15,-3461.1"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/appStore.ts -->
<g id="edge170" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M537.42,-3266.46C564.63,-3249.35 618.69,-3210.57 640,-3161 650.91,-3135.63 631.26,-2186.56 650.75,-2167 759.14,-2058.21 842.68,-2162 996.25,-2162 996.25,-2162 996.25,-2162 1205.38,-2162 1325.13,-2162 1374.96,-2215.58 1474.5,-2149 1529.33,-2112.32 1548.52,-2028.89 1554.48,-1992.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1556.52,-1992.6 1555.33,-1986.35 1552.36,-1991.98 1556.52,-1992.6"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts -->
<g id="node56" class="node">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts</title>
<g id="a_node56"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/loadDeckDraft/loadDeckDraft.ts" xlink:title="loadDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1246.12,-1040.25 1162.62,-1040.25 1162.62,-1021.75 1246.12,-1021.75 1246.12,-1040.25"/>
<text text-anchor="start" x="1170.62" y="-1027.7" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckDraft/loadDeckDraft.ts -->
<g id="edge171" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckDraft/loadDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M537.42,-3266.46C564.63,-3249.35 618.67,-3210.56 640,-3161 660.82,-3112.62 631.25,-2261.93 650.75,-2213 758.41,-1942.87 987.13,-2002.9 1095.38,-1733 1109.05,-1698.91 1085.18,-1102.43 1105.75,-1072 1116.78,-1055.69 1135.66,-1045.85 1153.9,-1039.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1154.15,-1042.04 1159.31,-1038.32 1152.96,-1038.01 1154.15,-1042.04"/>
</g>
<!-- src/client/application/queries/isCatalogLoading.ts -->
<g id="node91" class="node">
<title>src/client/application/queries/isCatalogLoading.ts</title>
<g id="a_node91"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/isCatalogLoading.ts" xlink:title="isCatalogLoading.ts">
<polygon fill="#fb6969" stroke="black" points="1429.62,-1434.25 1331.88,-1434.25 1331.88,-1415.75 1429.62,-1415.75 1429.62,-1434.25"/>
<text text-anchor="start" x="1339.88" y="-1421.7" font-family="Helvetica,sans-Serif" font-size="9.00">isCatalogLoading.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading.ts -->
<g id="edge172" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M537.42,-3266.46C564.63,-3249.35 618.67,-3210.56 640,-3161 650.41,-3136.81 633.94,-2233.27 650.75,-2213 718.67,-2131.09 799.08,-2228.08 885.5,-2166 1187.48,-1949.06 1001.88,-1659.14 1303,-1441 1309.02,-1436.64 1315.97,-1433.41 1323.17,-1431.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1323.59,-1433.09 1328.79,-1429.43 1322.44,-1429.05 1323.59,-1433.09"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/appStore.ts -->
<g id="edge173" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M571.57,-3341.32C596.03,-3340.45 623.61,-3334.51 640,-3315 659.53,-3291.75 629.33,-2243.52 650.75,-2222 759.09,-2113.16 842.68,-2217 996.25,-2217 996.25,-2217 996.25,-2217 1205.38,-2217 1326.8,-2217 1379.55,-2245.68 1474.5,-2170 1531.71,-2124.4 1549.71,-2031.92 1554.92,-1992.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1556.99,-1992.88 1555.63,-1986.67 1552.82,-1992.38 1556.99,-1992.88"/>
</g>
<!-- src/client/application/queries/getCatalogCardById.ts -->
<g id="node42" class="node">
<title>src/client/application/queries/getCatalogCardById.ts</title>
<g id="a_node42"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getCatalogCardById.ts" xlink:title="getCatalogCardById.ts">
<polygon fill="#fb6969" stroke="black" points="1434.88,-1372.25 1326.62,-1372.25 1326.62,-1353.75 1434.88,-1353.75 1434.88,-1372.25"/>
<text text-anchor="start" x="1334.62" y="-1359.7" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogCardById.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/getCatalogCardById.ts -->
<g id="edge175" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M571.87,-3341.3C596.26,-3340.4 623.68,-3334.42 640,-3315 659,-3292.39 633.44,-2275.93 650.75,-2252 715.35,-2162.69 791.16,-2229.01 885.5,-2172 995.51,-2105.51 1025.56,-2080.93 1095.38,-1973 1247.26,-1738.18 1092.75,-1563.41 1303,-1379 1307.47,-1375.08 1312.65,-1372.03 1318.16,-1369.67"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1318.66,-1371.73 1323.59,-1367.71 1317.23,-1367.77 1318.66,-1371.73"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts -->
<g id="node57" class="node">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts</title>
<g id="a_node57"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts" xlink:title="loadDeckIntoBuilder.ts">
<polygon fill="#fb6969" stroke="black" points="1258.12,-1101.25 1150.62,-1101.25 1150.62,-1082.75 1258.12,-1082.75 1258.12,-1101.25"/>
<text text-anchor="start" x="1158.62" y="-1088.7" font-family="Helvetica,sans-Serif" font-size="9.00">loadDeckIntoBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts -->
<g id="edge174" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M571.87,-3341.3C596.26,-3340.4 623.68,-3334.42 640,-3315 659.19,-3292.16 636.13,-2267.01 650.75,-2241 710.97,-2133.9 792.28,-2175.03 885.5,-2095 994.29,-2001.6 1044.68,-1984.12 1095.38,-1850 1109.46,-1812.74 1083.46,-1166.02 1105.75,-1133 1114.65,-1119.82 1128.67,-1110.87 1143.35,-1104.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.84,-1106.85 1148.71,-1102.77 1142.35,-1102.92 1143.84,-1106.85"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/hasDeckDraft.ts -->
<g id="edge176" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/hasDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M571.87,-3341.3C596.26,-3340.4 623.68,-3334.42 640,-3315 658.77,-3292.67 630.28,-2285.77 650.75,-2265 675.88,-2239.5 1269.88,-2274.51 1295,-2249 1311.35,-2232.39 1288.21,-1428.01 1303,-1410 1310.09,-1401.37 1320.52,-1396.62 1331.42,-1394.17"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1331.67,-1396.25 1337.23,-1393.17 1330.96,-1392.11 1331.67,-1396.25"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading.ts -->
<g id="edge177" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/application/queries/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M571.87,-3341.3C596.25,-3340.4 623.67,-3334.42 640,-3315 658.39,-3293.12 630.79,-2306.47 650.75,-2286 700.76,-2234.72 1245.01,-2314.3 1295,-2263 1310.94,-2246.65 1288.51,-1458.65 1303,-1441 1308.2,-1434.67 1315.2,-1430.43 1322.87,-1427.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1323.31,-1429.7 1328.49,-1426.02 1322.14,-1425.67 1323.31,-1429.7"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/infrastructure/store.ts -->
<g id="edge178" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx&#45;&gt;src/client/infrastructure/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M571.58,-3341.23C596.35,-3346.13 624.22,-3356.44 640,-3378 667.9,-3416.12 628.92,-3761.11 650.75,-3803 710.66,-3917.93 826.75,-3860.48 885.5,-3976 892.28,-3989.33 881.24,-5041.77 891.12,-5053 907.99,-5072.16 937.41,-5074.67 960.84,-5072.95"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.04,-5075.04 966.8,-5072.37 960.63,-5070.86 961.04,-5075.04"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx -->
<g id="node8" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx</title>
<g id="a_node8"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/(connected)/games/[gameId]/deck-builder/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-7704.25C115.08,-7704.25 73.17,-7704.25 73.17,-7704.25 70.08,-7704.25 67,-7701.17 67,-7698.08 67,-7698.08 67,-7691.92 67,-7691.92 67,-7688.83 70.08,-7685.75 73.17,-7685.75 73.17,-7685.75 115.08,-7685.75 115.08,-7685.75 118.17,-7685.75 121.25,-7688.83 121.25,-7691.92 121.25,-7691.92 121.25,-7698.08 121.25,-7698.08 121.25,-7701.17 118.17,-7704.25 115.08,-7704.25"/>
<text text-anchor="start" x="75" y="-7691.7" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts -->
<g id="node9" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts</title>
<g id="a_node9"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts" xlink:title="DeckBuilderInitializer.ts">
<polygon fill="#6cbaff" stroke="black" points="575.88,-3712.25 464.62,-3712.25 464.62,-3693.75 575.88,-3693.75 575.88,-3712.25"/>
<text text-anchor="start" x="472.62" y="-3699.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderInitializer.ts</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts -->
<g id="edge6" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.52,-7697.21C138.76,-7697.07 160.24,-7693.55 172.25,-7679 182.45,-7666.64 179.67,-7118.02 180.25,-7102 228.27,-5771.72 301.5,-5442.08 354.75,-4112 355.16,-4101.78 356.42,-3752.03 362.75,-3744 384.55,-3716.37 422.51,-3705.97 455.58,-3702.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="455.5,-3704.66 461.29,-3702.05 455.14,-3700.48 455.5,-3704.66"/>
</g>
<!-- src/client/infrastructure/providers/ReduxProvider.tsx -->
<g id="node10" class="node">
<title>src/client/infrastructure/providers/ReduxProvider.tsx</title>
<g id="a_node10"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/providers/ReduxProvider.tsx" xlink:title="ReduxProvider.tsx">
<polygon fill="#6cbaff" stroke="black" points="797.25,-4360.25 707,-4360.25 707,-4341.75 797.25,-4341.75 797.25,-4360.25"/>
<text text-anchor="start" x="715" y="-4347.7" font-family="Helvetica,sans-Serif" font-size="9.00">ReduxProvider.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/providers/ReduxProvider.tsx -->
<g id="edge7" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/layout.tsx&#45;&gt;src/client/infrastructure/providers/ReduxProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.52,-7697.21C138.76,-7697.06 160.23,-7693.55 172.25,-7679 191.71,-7655.44 159.17,-7151.13 180.25,-7129 250.86,-7054.88 569.7,-7174.41 640,-7100 648.33,-7091.19 650.11,-5366.11 650.75,-5354 671.93,-4954.42 735.35,-4468.5 748.66,-4369.22"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="750.72,-4369.6 749.44,-4363.38 746.56,-4369.04 750.72,-4369.6"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts -->
<g id="node113" class="node">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts</title>
<g id="a_node113"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts" xlink:title="useInitializeDeckBuilderFromLocation.ts">
<polygon fill="#6cbaff" stroke="black" points="1087.38,-4813.25 907.12,-4813.25 907.12,-4794.75 1087.38,-4794.75 1087.38,-4813.25"/>
<text text-anchor="start" x="915.12" y="-4800.7" font-family="Helvetica,sans-Serif" font-size="9.00">useInitializeDeckBuilderFromLocation.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts&#45;&gt;src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts -->
<g id="edge145" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderInitializer/DeckBuilderInitializer.ts&#45;&gt;src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M576.22,-3708.44C599.65,-3713.74 624.99,-3724.04 640,-3744 667.83,-3780.99 625.84,-3912.99 650.75,-3952 714.25,-4051.45 824.19,-3975.19 885.5,-4076 895.69,-4092.76 878.7,-4766.83 891.12,-4782 893.58,-4785 896.34,-4787.66 899.32,-4790.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="898.01,-4791.67 904.16,-4793.27 900.36,-4788.18 898.01,-4791.67"/>
</g>
<!-- src/client/infrastructure/providers/ReduxProvider.tsx&#45;&gt;src/client/infrastructure/store.ts -->
<g id="edge238" class="edge">
<title>src/client/infrastructure/providers/ReduxProvider.tsx&#45;&gt;src/client/infrastructure/store.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M753.52,-4360.36C756.93,-4436.37 783.89,-4952.18 891.12,-5053 909.48,-5070.26 938.25,-5073.07 961.05,-5071.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="960.93,-5074.06 966.76,-5071.53 960.63,-5069.87 960.93,-5074.06"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx -->
<g id="node11" class="node">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx</title>
<g id="a_node11"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/(connected)/games/[gameId]/deck-builder/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7673.25C114.96,-7673.25 73.29,-7673.25 73.29,-7673.25 70.21,-7673.25 67.12,-7670.17 67.12,-7667.08 67.12,-7667.08 67.12,-7660.92 67.12,-7660.92 67.12,-7657.83 70.21,-7654.75 73.29,-7654.75 73.29,-7654.75 114.96,-7654.75 114.96,-7654.75 118.04,-7654.75 121.12,-7657.83 121.12,-7660.92 121.12,-7660.92 121.12,-7667.08 121.12,-7667.08 121.12,-7670.17 118.04,-7673.25 114.96,-7673.25"/>
<text text-anchor="start" x="76.88" y="-7660.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx -->
<g id="edge8" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.4,-7667.32C138.59,-7667.89 160.05,-7665.22 172.25,-7651 182.18,-7639.43 179.69,-7117.24 180.25,-7102 228.74,-5771.73 296.4,-5441.87 354.75,-4112 355.41,-4096.9 354.54,-3852.69 362.75,-3840 384.77,-3805.95 426.73,-3786.72 461.66,-3776.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="461.82,-3778.26 467,-3774.57 460.66,-3774.22 461.82,-3778.26"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx -->
<g id="edge9" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7667.37C138.67,-7667.96 160.13,-7665.3 172.25,-7651 188.14,-7632.26 168.13,-4134.37 180.25,-4113 224.89,-4034.26 309.24,-4086.24 354.75,-4008 363.83,-3992.39 351.54,-3697.16 362.75,-3683 385.39,-3654.39 425.35,-3644.26 459.22,-3641.22"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="459.29,-3643.32 465.12,-3640.79 458.98,-3639.13 459.29,-3643.32"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx -->
<g id="edge10" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7667.37C138.67,-7667.96 160.14,-7665.3 172.25,-7651 180.43,-7641.35 177.3,-4021.3 180.25,-4009 220.3,-3841.9 299.35,-3826.66 354.75,-3664 360.88,-3646.01 354.03,-3638.88 362.75,-3622 396.38,-3556.86 464.3,-3499.97 498.53,-3473.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="499.72,-3475.72 503.27,-3470.45 497.21,-3472.36 499.72,-3475.72"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx -->
<g id="edge11" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7667.37C138.67,-7667.96 160.14,-7665.3 172.25,-7651 191.39,-7628.4 163.39,-3410.35 180.25,-3386 242.06,-3296.73 378.43,-3278.16 458.09,-3275.31"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="458.08,-3277.41 464.01,-3275.13 457.95,-3273.21 458.08,-3277.41"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx -->
<g id="edge12" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/deck&#45;builder/page.tsx&#45;&gt;src/client/infrastructure/components/app/DeckBuilding/EditDeckInitializer/EditDeckInitializer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7667.37C138.67,-7667.96 160.14,-7665.3 172.25,-7651 181.2,-7640.44 176.37,-3678.29 180.25,-3665 222.63,-3519.9 240.64,-3467.09 362.75,-3378 390.96,-3357.42 428.67,-3347.16 460.01,-3342.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="460.06,-3344.17 465.67,-3341.19 459.43,-3340.01 460.06,-3344.17"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/page.tsx -->
<g id="node12" class="node">
<title>app/[locale]/(connected)/games/[gameId]/page.tsx</title>
<g id="a_node12"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/(connected)/games/[gameId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7639.25C114.96,-7639.25 73.29,-7639.25 73.29,-7639.25 70.21,-7639.25 67.12,-7636.17 67.12,-7633.08 67.12,-7633.08 67.12,-7626.92 67.12,-7626.92 67.12,-7623.83 70.21,-7620.75 73.29,-7620.75 73.29,-7620.75 114.96,-7620.75 114.96,-7620.75 118.04,-7620.75 121.12,-7623.83 121.12,-7626.92 121.12,-7626.92 121.12,-7633.08 121.12,-7633.08 121.12,-7636.17 118.04,-7639.25 114.96,-7639.25"/>
<text text-anchor="start" x="76.88" y="-7626.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx -->
<g id="node13" class="node">
<title>src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx</title>
<g id="a_node13"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx" xlink:title="GameDetailsPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="335.75,-2670.25 231.25,-2670.25 231.25,-2651.75 335.75,-2651.75 335.75,-2670.25"/>
<text text-anchor="start" x="239.25" y="-2657.7" font-family="Helvetica,sans-Serif" font-size="9.00">GameDetailsPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx -->
<g id="edge13" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.5,-7633C138.73,-7633.35 160.2,-7630.41 172.25,-7616 181.9,-7604.46 179.01,-3297.99 180.25,-3283 200.16,-3041.89 261.94,-2753.35 278.42,-2679.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="280.44,-2679.7 279.7,-2673.38 276.34,-2678.78 280.44,-2679.7"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="node101" class="node">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<g id="a_node101"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx" xlink:title="ShiningCard.tsx">
<polygon fill="#6cbaff" stroke="black" points="1037.88,-3136.25 956.62,-3136.25 956.62,-3117.75 1037.88,-3117.75 1037.88,-3136.25"/>
<text text-anchor="start" x="964.62" y="-3123.7" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge228" class="edge">
<title>src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M336.14,-2653.13C343.38,-2649.86 349.97,-2645.32 354.75,-2639 364.04,-2626.73 351.71,-2372.73 362.75,-2362 451.13,-2276.14 539.76,-2290.34 640,-2362 914.35,-2558.14 793.8,-2755.46 885.5,-3080 888.6,-3090.96 883.01,-3097.01 891.12,-3105 905.81,-3119.47 927.42,-3125.54 947.24,-3127.77"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="947.01,-3129.86 953.17,-3128.29 947.37,-3125.68 947.01,-3129.86"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx -->
<g id="node104" class="node">
<title>src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx</title>
<g id="a_node104"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx" xlink:title="PlayGameButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="569.5,-3869.25 471,-3869.25 471,-3850.75 569.5,-3850.75 569.5,-3869.25"/>
<text text-anchor="start" x="479" y="-3856.7" font-family="Helvetica,sans-Serif" font-size="9.00">PlayGameButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx -->
<g id="edge227" class="edge">
<title>src/client/infrastructure/pages/Catalog/GameDetailsPage/GameDetailsPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M284.53,-2670.65C284.94,-2775.84 290.62,-3707.12 362.75,-3803 385.99,-3833.89 427.63,-3848.06 462.12,-3854.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="461.46,-3856.57 467.73,-3855.53 462.18,-3852.43 461.46,-3856.57"/>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/play/page.tsx -->
<g id="node14" class="node">
<title>app/[locale]/(connected)/games/[gameId]/play/page.tsx</title>
<g id="a_node14"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/(connected)/games/[gameId]/play/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7586.25C114.96,-7586.25 73.29,-7586.25 73.29,-7586.25 70.21,-7586.25 67.12,-7583.17 67.12,-7580.08 67.12,-7580.08 67.12,-7573.92 67.12,-7573.92 67.12,-7570.83 70.21,-7567.75 73.29,-7567.75 73.29,-7567.75 114.96,-7567.75 114.96,-7567.75 118.04,-7567.75 121.12,-7570.83 121.12,-7573.92 121.12,-7573.92 121.12,-7580.08 121.12,-7580.08 121.12,-7583.17 118.04,-7586.25 114.96,-7586.25"/>
<text text-anchor="start" x="76.88" y="-7573.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx -->
<g id="node15" class="node">
<title>src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx</title>
<g id="a_node15"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx" xlink:title="PlayGamePage.tsx">
<polygon fill="#6cbaff" stroke="black" points="330.5,-2609.25 236.5,-2609.25 236.5,-2590.75 330.5,-2590.75 330.5,-2609.25"/>
<text text-anchor="start" x="244.5" y="-2596.7" font-family="Helvetica,sans-Serif" font-size="9.00">PlayGamePage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/[gameId]/play/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx -->
<g id="edge14" class="edge">
<title>app/[locale]/(connected)/games/[gameId]/play/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7580.37C138.67,-7580.96 160.14,-7578.3 172.25,-7564 194.35,-7537.91 161.07,-2669.3 180.25,-2641 191.18,-2624.87 209.61,-2615.08 227.84,-2609.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="228.11,-2611.26 233.26,-2607.54 226.92,-2607.23 228.11,-2611.26"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx -->
<g id="node125" class="node">
<title>src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx</title>
<g id="a_node125"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx" xlink:title="StartGameButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="569.88,-4087.25 470.62,-4087.25 470.62,-4068.75 569.88,-4068.75 569.88,-4087.25"/>
<text text-anchor="start" x="478.62" y="-4074.7" font-family="Helvetica,sans-Serif" font-size="9.00">StartGameButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx -->
<g id="edge231" class="edge">
<title>src/client/infrastructure/pages/Catalog/PlayGamePage/PlayGamePage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M315.01,-2609.68C329.77,-2616.07 346.13,-2626.08 354.75,-2641 364.58,-2658.02 349.81,-4041.2 362.75,-4056 386.54,-4083.21 427.28,-4088.24 461.24,-4086.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="461.28,-4088.82 467.14,-4086.35 461.02,-4084.63 461.28,-4088.82"/>
</g>
<!-- app/[locale]/(connected)/games/page.tsx -->
<g id="node16" class="node">
<title>app/[locale]/(connected)/games/page.tsx</title>
<g id="a_node16"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/(connected)/games/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7544.25C114.96,-7544.25 73.29,-7544.25 73.29,-7544.25 70.21,-7544.25 67.12,-7541.17 67.12,-7538.08 67.12,-7538.08 67.12,-7531.92 67.12,-7531.92 67.12,-7528.83 70.21,-7525.75 73.29,-7525.75 73.29,-7525.75 114.96,-7525.75 114.96,-7525.75 118.04,-7525.75 121.12,-7528.83 121.12,-7531.92 121.12,-7531.92 121.12,-7538.08 121.12,-7538.08 121.12,-7541.17 118.04,-7544.25 114.96,-7544.25"/>
<text text-anchor="start" x="76.88" y="-7531.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx -->
<g id="node17" class="node">
<title>src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx</title>
<g id="a_node17"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx" xlink:title="GameListPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="328.62,-2548.25 238.38,-2548.25 238.38,-2529.75 328.62,-2529.75 328.62,-2548.25"/>
<text text-anchor="start" x="246.38" y="-2535.7" font-family="Helvetica,sans-Serif" font-size="9.00">GameListPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/games/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx -->
<g id="edge15" class="edge">
<title>app/[locale]/(connected)/games/page.tsx&#45;&gt;src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.3,-7526.08C139.16,-7518.47 161.46,-7505.59 172.25,-7486 188.68,-7456.15 161.14,-2608.2 180.25,-2580 191.51,-2563.39 210.71,-2553.5 229.46,-2547.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="229.91,-2549.68 235.1,-2546.02 228.76,-2545.64 229.91,-2549.68"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx -->
<g id="node103" class="node">
<title>src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx</title>
<g id="a_node103"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx" xlink:title="GameDetailsButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="574.75,-3930.25 465.75,-3930.25 465.75,-3911.75 574.75,-3911.75 574.75,-3930.25"/>
<text text-anchor="start" x="473.75" y="-3917.7" font-family="Helvetica,sans-Serif" font-size="9.00">GameDetailsButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx -->
<g id="edge229" class="edge">
<title>src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M315,-2548.68C329.77,-2555.08 346.13,-2565.09 354.75,-2580 363.92,-2595.86 350.69,-3885.21 362.75,-3899 385.47,-3924.97 423.63,-3930.74 456.59,-3929.88"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="456.35,-3931.99 462.26,-3929.63 456.17,-3927.79 456.35,-3931.99"/>
</g>
<!-- src/server/application/queries/loadGameList.ts -->
<g id="node138" class="node">
<title>src/server/application/queries/loadGameList.ts</title>
<g id="a_node138"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/queries/loadGameList.ts" xlink:title="loadGameList.ts">
<polygon fill="#dd1c1c" stroke="black" points="561.25,-6026.25 479.25,-6026.25 479.25,-6007.75 561.25,-6007.75 561.25,-6026.25"/>
<text text-anchor="start" x="487.25" y="-6013.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadGameList.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/server/application/queries/loadGameList.ts -->
<g id="edge230" class="edge">
<title>src/client/infrastructure/pages/Catalog/GameListPage/GameListPage.tsx&#45;&gt;src/server/application/queries/loadGameList.ts</title>
<g id="a_edge230"><a xlink:title="no&#45;cross&#45;boundary&#45;client&#45;server">
<path fill="none" stroke="red" stroke-width="2" d="M315.03,-2548.67C329.8,-2555.06 346.15,-2565.07 354.75,-2580 366.6,-2600.59 346.81,-5983.39 362.75,-6001 389.02,-6030.03 434.94,-6031.56 470.27,-6027.29"/>
<polygon fill="red" stroke="red" stroke-width="2" points="470.35,-6029.4 476.01,-6026.51 469.78,-6025.24 470.35,-6029.4"/>
</a>
</g>
<text text-anchor="middle" x="291.97" y="-4278.71" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">no&#45;cross&#45;boundary&#45;client&#45;server</text>
</g>
<!-- app/[locale]/(connected)/matches/[matchId]/page.tsx -->
<g id="node18" class="node">
<title>app/[locale]/(connected)/matches/[matchId]/page.tsx</title>
<g id="a_node18"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/(connected)/matches/[matchId]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7456.25C114.96,-7456.25 73.29,-7456.25 73.29,-7456.25 70.21,-7456.25 67.12,-7453.17 67.12,-7450.08 67.12,-7450.08 67.12,-7443.92 67.12,-7443.92 67.12,-7440.83 70.21,-7437.75 73.29,-7437.75 73.29,-7437.75 114.96,-7437.75 114.96,-7437.75 118.04,-7437.75 121.12,-7440.83 121.12,-7443.92 121.12,-7443.92 121.12,-7450.08 121.12,-7450.08 121.12,-7453.17 118.04,-7456.25 114.96,-7456.25"/>
<text text-anchor="start" x="76.88" y="-7443.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx -->
<g id="node19" class="node">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx</title>
<g id="a_node19"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx" xlink:title="MatchPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="321.5,-2452.25 245.5,-2452.25 245.5,-2433.75 321.5,-2433.75 321.5,-2452.25"/>
<text text-anchor="start" x="253.5" y="-2439.7" font-family="Helvetica,sans-Serif" font-size="9.00">MatchPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(connected)/matches/[matchId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx -->
<g id="edge16" class="edge">
<title>app/[locale]/(connected)/matches/[matchId]/page.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.47,-7441.59C139.17,-7436.44 161.2,-7426.74 172.25,-7409 190.21,-7380.18 164.97,-2549.33 180.25,-2519 195,-2489.72 226.95,-2468.86 251.24,-2456.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="251.96,-2458.48 256.43,-2453.96 250.12,-2454.71 251.96,-2458.48"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx -->
<g id="node123" class="node">
<title>src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx</title>
<g id="a_node123"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx" xlink:title="LeaveMatchButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="573.25,-4026.25 467.25,-4026.25 467.25,-4007.75 573.25,-4007.75 573.25,-4026.25"/>
<text text-anchor="start" x="475.25" y="-4013.7" font-family="Helvetica,sans-Serif" font-size="9.00">LeaveMatchButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx -->
<g id="edge232" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/LeaveMatchButton/LeaveMatchButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.27,-2452.67C315.27,-2465.54 343.34,-2490.09 354.75,-2519 362.1,-2537.62 351.23,-3943.64 362.75,-3960 384.39,-3990.73 424.4,-4004.95 458.55,-4011.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="457.84,-4013.51 464.11,-4012.48 458.57,-4009.37 457.84,-4013.51"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/Match/Match.tsx -->
<g id="node124" class="node">
<title>src/client/infrastructure/components/app/Gaming/Match/Match.tsx</title>
<g id="a_node124"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/Gaming/Match/Match.tsx" xlink:title="Match.tsx">
<polygon fill="#6cbaff" stroke="black" points="547.38,-4148.25 493.12,-4148.25 493.12,-4129.75 547.38,-4129.75 547.38,-4148.25"/>
<text text-anchor="start" x="501.12" y="-4135.7" font-family="Helvetica,sans-Serif" font-size="9.00">Match.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/Match/Match.tsx -->
<g id="edge233" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/app/Gaming/Match/Match.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.27,-2452.67C315.28,-2465.54 343.35,-2490.08 354.75,-2519 362.89,-2539.65 348.15,-4100.29 362.75,-4117 392.21,-4150.72 447.69,-4150.39 484.06,-4145.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="484.23,-4147.82 489.87,-4144.89 483.63,-4143.66 484.23,-4147.82"/>
</g>
<!-- src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx -->
<g id="node127" class="node">
<title>src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx</title>
<g id="a_node127"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx" xlink:title="MatchConsoleEvents.tsx">
<polygon fill="#6cbaff" stroke="black" points="578.5,-2958.25 462,-2958.25 462,-2939.75 578.5,-2939.75 578.5,-2958.25"/>
<text text-anchor="start" x="470" y="-2945.7" font-family="Helvetica,sans-Serif" font-size="9.00">MatchConsoleEvents.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx -->
<g id="edge234" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/components/debug/MatchConsoleEvents/MatchConsoleEvents.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.2,-2452.69C315.14,-2465.6 343.14,-2490.17 354.75,-2519 363.22,-2540.03 347.71,-2910.04 362.75,-2927 384.71,-2951.77 420.74,-2958.08 452.69,-2957.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="452.61,-2959.93 458.55,-2957.67 452.5,-2955.73 452.61,-2959.93"/>
</g>
<!-- src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx -->
<g id="node139" class="node">
<title>src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx</title>
<g id="a_node139"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx" xlink:title="ErrorLoadingMatchPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="585.25,-2391.25 455.25,-2391.25 455.25,-2372.75 585.25,-2372.75 585.25,-2391.25"/>
<text text-anchor="start" x="463.25" y="-2378.7" font-family="Helvetica,sans-Serif" font-size="9.00">ErrorLoadingMatchPage.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx -->
<g id="edge235" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/ErrorLoadingMatchPage/ErrorLoadingMatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M321.96,-2433.27C363.1,-2422.58 429.18,-2405.4 473.39,-2393.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="473.72,-2396 479,-2392.46 472.66,-2391.94 473.72,-2396"/>
</g>
<!-- src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx -->
<g id="node140" class="node">
<title>src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx</title>
<g id="a_node140"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx" xlink:title="FinishedMatchPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="575.88,-2452.25 464.62,-2452.25 464.62,-2433.75 575.88,-2433.75 575.88,-2452.25"/>
<text text-anchor="start" x="472.62" y="-2439.7" font-family="Helvetica,sans-Serif" font-size="9.00">FinishedMatchPage.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx -->
<g id="edge236" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/client/infrastructure/pages/Gaming/FinishedMatchPage/FinishedMatchPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M321.96,-2443C357.85,-2443 412.73,-2443 455.5,-2443"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="455.41,-2445.1 461.41,-2443 455.41,-2440.9 455.41,-2445.1"/>
</g>
<!-- src/server/application/queries/loadMatchById.ts -->
<g id="node141" class="node">
<title>src/server/application/queries/loadMatchById.ts</title>
<g id="a_node141"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/queries/loadMatchById.ts" xlink:title="loadMatchById.ts">
<polygon fill="#dd1c1c" stroke="black" points="563.12,-5995.25 477.38,-5995.25 477.38,-5976.75 563.12,-5976.75 563.12,-5995.25"/>
<text text-anchor="start" x="485.38" y="-5982.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadMatchById.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/server/application/queries/loadMatchById.ts -->
<g id="edge237" class="edge">
<title>src/client/infrastructure/pages/Gaming/MatchPage/MatchPage.tsx&#45;&gt;src/server/application/queries/loadMatchById.ts</title>
<g id="a_edge237"><a xlink:title="no&#45;cross&#45;boundary&#45;client&#45;server">
<path fill="none" stroke="red" stroke-width="2" d="M298.28,-2452.66C315.3,-2465.53 343.39,-2490.07 354.75,-2519 363.36,-2540.92 350.23,-5890.05 362.75,-5910 385.79,-5946.71 432.17,-5966.14 468.49,-5976.14"/>
<polygon fill="red" stroke="red" stroke-width="2" points="467.75,-5978.11 474.09,-5977.6 468.81,-5974.05 467.75,-5978.11"/>
</a>
</g>
<text text-anchor="middle" x="292.03" y="-4207.31" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">no&#45;cross&#45;boundary&#45;client&#45;server</text>
</g>
<!-- app/[locale]/(not&#45;connected)/layout.tsx -->
<g id="node20" class="node">
<title>app/[locale]/(not&#45;connected)/layout.tsx</title>
<g id="a_node20"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/(not-connected)/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-7257.25C115.08,-7257.25 73.17,-7257.25 73.17,-7257.25 70.08,-7257.25 67,-7254.17 67,-7251.08 67,-7251.08 67,-7244.92 67,-7244.92 67,-7241.83 70.08,-7238.75 73.17,-7238.75 73.17,-7238.75 115.08,-7238.75 115.08,-7238.75 118.17,-7238.75 121.25,-7241.83 121.25,-7244.92 121.25,-7244.92 121.25,-7251.08 121.25,-7251.08 121.25,-7254.17 118.17,-7257.25 115.08,-7257.25"/>
<text text-anchor="start" x="75" y="-7244.7" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx -->
<g id="node21" class="node">
<title>src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx</title>
<g id="a_node21"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx" xlink:title="FullPageLayout.tsx">
<polygon fill="#6cbaff" stroke="black" points="567.25,-4359.25 473.25,-4359.25 473.25,-4340.75 567.25,-4340.75 567.25,-4359.25"/>
<text text-anchor="start" x="481.25" y="-4346.7" font-family="Helvetica,sans-Serif" font-size="9.00">FullPageLayout.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/layout.tsx&#45;&gt;src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx -->
<g id="edge17" class="edge">
<title>app/[locale]/(not&#45;connected)/layout.tsx&#45;&gt;src/client/infrastructure/layouts/FullPageLayout/FullPageLayout.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7251.37C138.67,-7251.96 160.14,-7249.3 172.25,-7235 183.54,-7221.67 167.89,-2217.34 180.25,-2205 235.13,-2150.2 299.81,-2150.26 354.75,-2205 375.64,-2225.81 343.36,-4305.78 362.75,-4328 387.28,-4356.11 429.9,-4360.57 464.48,-4358.56"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="464.23,-4360.69 470.06,-4358.15 463.92,-4356.5 464.23,-4360.69"/>
</g>
<!-- app/[locale]/(not&#45;connected)/signin/page.tsx -->
<g id="node22" class="node">
<title>app/[locale]/(not&#45;connected)/signin/page.tsx</title>
<g id="a_node22"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/(not-connected)/signin/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7291.25C114.96,-7291.25 73.29,-7291.25 73.29,-7291.25 70.21,-7291.25 67.12,-7288.17 67.12,-7285.08 67.12,-7285.08 67.12,-7278.92 67.12,-7278.92 67.12,-7275.83 70.21,-7272.75 73.29,-7272.75 73.29,-7272.75 114.96,-7272.75 114.96,-7272.75 118.04,-7272.75 121.12,-7275.83 121.12,-7278.92 121.12,-7278.92 121.12,-7285.08 121.12,-7285.08 121.12,-7288.17 118.04,-7291.25 114.96,-7291.25"/>
<text text-anchor="start" x="76.88" y="-7278.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx -->
<g id="node23" class="node">
<title>src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx</title>
<g id="a_node23"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx" xlink:title="SignInPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="322.25,-2234.25 244.75,-2234.25 244.75,-2215.75 322.25,-2215.75 322.25,-2234.25"/>
<text text-anchor="start" x="252.75" y="-2221.7" font-family="Helvetica,sans-Serif" font-size="9.00">SignInPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/(not&#45;connected)/signin/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx -->
<g id="edge18" class="edge">
<title>app/[locale]/(not&#45;connected)/signin/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7285.37C138.67,-7285.96 160.14,-7283.3 172.25,-7269 183.48,-7255.74 170.51,-2280.38 180.25,-2266 192.74,-2247.57 215,-2237.43 235.57,-2231.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="235.98,-2233.9 241.31,-2230.44 234.98,-2229.82 235.98,-2233.9"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx -->
<g id="node100" class="node">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx</title>
<g id="a_node100"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx" xlink:title="SignInForm.tsx">
<polygon fill="#6cbaff" stroke="black" points="790.5,-3131.25 713.75,-3131.25 713.75,-3112.75 790.5,-3112.75 790.5,-3131.25"/>
<text text-anchor="start" x="721.75" y="-3118.7" font-family="Helvetica,sans-Serif" font-size="9.00">SignInForm.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx -->
<g id="edge225" class="edge">
<title>src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M314.45,-2234.64C329.26,-2241.05 345.82,-2251.1 354.75,-2266 367.09,-2286.6 353.4,-2459.88 362.75,-2482 432.18,-2646.24 549.06,-2618.62 640,-2772 708.41,-2887.38 739.43,-3049.53 748.32,-3103.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="746.22,-3103.85 749.24,-3109.45 750.37,-3103.19 746.22,-3103.85"/>
</g>
<!-- src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx -->
<g id="node133" class="node">
<title>src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx</title>
<g id="a_node133"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx" xlink:title="Sparkles.tsx">
<polygon fill="#6cbaff" stroke="black" points="1030,-3258.25 964.5,-3258.25 964.5,-3239.75 1030,-3239.75 1030,-3258.25"/>
<text text-anchor="start" x="972.5" y="-3245.7" font-family="Helvetica,sans-Serif" font-size="9.00">Sparkles.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx -->
<g id="edge226" class="edge">
<title>src/client/infrastructure/pages/Auth/SignInPage/SignInPage.tsx&#45;&gt;src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M322.49,-2229.02C394.58,-2238.66 551.9,-2269.62 640,-2362 879.14,-2612.77 822.56,-2761.25 885.5,-3102 888.03,-3115.67 882.12,-3216.41 891.12,-3227 906.63,-3245.23 933,-3250.71 955.45,-3251.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="955.41,-3253.74 961.44,-3251.74 955.48,-3249.54 955.41,-3253.74"/>
</g>
<!-- app/[locale]/access&#45;denied/page.tsx -->
<g id="node24" class="node">
<title>app/[locale]/access&#45;denied/page.tsx</title>
<g id="a_node24"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/access-denied/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7379.25C114.96,-7379.25 73.29,-7379.25 73.29,-7379.25 70.21,-7379.25 67.12,-7376.17 67.12,-7373.08 67.12,-7373.08 67.12,-7366.92 67.12,-7366.92 67.12,-7363.83 70.21,-7360.75 73.29,-7360.75 73.29,-7360.75 114.96,-7360.75 114.96,-7360.75 118.04,-7360.75 121.12,-7363.83 121.12,-7366.92 121.12,-7366.92 121.12,-7373.08 121.12,-7373.08 121.12,-7376.17 118.04,-7379.25 114.96,-7379.25"/>
<text text-anchor="start" x="76.88" y="-7366.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx -->
<g id="node25" class="node">
<title>src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx</title>
<g id="a_node25"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx" xlink:title="AccessDeniedPage.tsx">
<polygon fill="#6cbaff" stroke="black" points="338.75,-2295.25 228.25,-2295.25 228.25,-2276.75 338.75,-2276.75 338.75,-2295.25"/>
<text text-anchor="start" x="236.25" y="-2282.7" font-family="Helvetica,sans-Serif" font-size="9.00">AccessDeniedPage.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/access&#45;denied/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx -->
<g id="edge19" class="edge">
<title>app/[locale]/access&#45;denied/page.tsx&#45;&gt;src/client/infrastructure/pages/Auth/AccessDeniedPage/AccessDeniedPage.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.3,-7361.08C139.16,-7353.47 161.46,-7340.59 172.25,-7321 188.65,-7291.2 169.51,-2455.27 180.25,-2423 197.13,-2372.29 240.85,-2325.14 265.31,-2301.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="266.53,-2303.36 269.45,-2297.71 263.65,-2300.3 266.53,-2303.36"/>
</g>
<!-- app/[locale]/page.tsx -->
<g id="node26" class="node">
<title>app/[locale]/page.tsx</title>
<g id="a_node26"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/[locale]/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7223.25C114.96,-7223.25 73.29,-7223.25 73.29,-7223.25 70.21,-7223.25 67.12,-7220.17 67.12,-7217.08 67.12,-7217.08 67.12,-7210.92 67.12,-7210.92 67.12,-7207.83 70.21,-7204.75 73.29,-7204.75 73.29,-7204.75 114.96,-7204.75 114.96,-7204.75 118.04,-7204.75 121.12,-7207.83 121.12,-7210.92 121.12,-7210.92 121.12,-7217.08 121.12,-7217.08 121.12,-7220.17 118.04,-7223.25 114.96,-7223.25"/>
<text text-anchor="start" x="76.88" y="-7210.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="node27" class="node">
<title>src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<g id="a_node27"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx" xlink:title="RedirectToGameList.tsx">
<polygon fill="#6cbaff" stroke="black" points="576.62,-2801.25 463.88,-2801.25 463.88,-2782.75 576.62,-2782.75 576.62,-2801.25"/>
<text text-anchor="start" x="471.88" y="-2788.7" font-family="Helvetica,sans-Serif" font-size="9.00">RedirectToGameList.tsx</text>
</a>
</g>
</g>
<!-- app/[locale]/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="edge20" class="edge">
<title>app/[locale]/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.46,-7217.37C138.67,-7217.96 160.14,-7215.3 172.25,-7201 183.56,-7187.65 167.87,-2174.36 180.25,-2162 235.13,-2107.2 299.23,-2107.85 354.75,-2162 367.48,-2174.42 358.41,-2464.75 362.75,-2482 393.11,-2602.8 476.39,-2730.52 507.41,-2775.32"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="505.51,-2776.27 510.67,-2779.99 508.96,-2773.87 505.51,-2776.27"/>
</g>
<!-- app/globals.css -->
<g id="node28" class="node">
<title>app/globals.css</title>
<g id="a_node28"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/globals.css" xlink:title="globals.css">
<path fill="#ffffcc" stroke="black" d="M308.21,-7279.25C308.21,-7279.25 258.79,-7279.25 258.79,-7279.25 255.71,-7279.25 252.62,-7276.17 252.62,-7273.08 252.62,-7273.08 252.62,-7266.92 252.62,-7266.92 252.62,-7263.83 255.71,-7260.75 258.79,-7260.75 258.79,-7260.75 308.21,-7260.75 308.21,-7260.75 311.29,-7260.75 314.38,-7263.83 314.38,-7266.92 314.38,-7266.92 314.38,-7273.08 314.38,-7273.08 314.38,-7276.17 311.29,-7279.25 308.21,-7279.25"/>
<text text-anchor="start" x="260.62" y="-7266.7" font-family="Helvetica,sans-Serif" font-size="9.00">globals.css</text>
</a>
</g>
</g>
<!-- app/layout.tsx -->
<g id="node29" class="node">
<title>app/layout.tsx</title>
<g id="a_node29"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/layout.tsx" xlink:title="layout.tsx">
<path fill="#bbfeff" stroke="black" d="M115.08,-7189.25C115.08,-7189.25 73.17,-7189.25 73.17,-7189.25 70.08,-7189.25 67,-7186.17 67,-7183.08 67,-7183.08 67,-7176.92 67,-7176.92 67,-7173.83 70.08,-7170.75 73.17,-7170.75 73.17,-7170.75 115.08,-7170.75 115.08,-7170.75 118.17,-7170.75 121.25,-7173.83 121.25,-7176.92 121.25,-7176.92 121.25,-7183.08 121.25,-7183.08 121.25,-7186.17 118.17,-7189.25 115.08,-7189.25"/>
<text text-anchor="start" x="75" y="-7176.7" font-family="Helvetica,sans-Serif" font-size="9.00">layout.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;app/globals.css -->
<g id="edge21" class="edge">
<title>app/layout.tsx&#45;&gt;app/globals.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.61,-7182.01C136.87,-7183.85 156.15,-7187.35 172.25,-7194 208.39,-7208.92 244.62,-7237.03 265.23,-7254.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="263.65,-7255.97 269.56,-7258.31 266.39,-7252.8 263.65,-7255.97"/>
</g>
<!-- src/client/infrastructure/components/ui/Background/Background.tsx -->
<g id="node30" class="node">
<title>src/client/infrastructure/components/ui/Background/Background.tsx</title>
<g id="a_node30"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/ui/Background/Background.tsx" xlink:title="Background.tsx">
<polygon fill="#6cbaff" stroke="black" points="1036.75,-3014.25 957.75,-3014.25 957.75,-2995.75 1036.75,-2995.75 1036.75,-3014.25"/>
<text text-anchor="start" x="965.75" y="-3001.7" font-family="Helvetica,sans-Serif" font-size="9.00">Background.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;src/client/infrastructure/components/ui/Background/Background.tsx -->
<g id="edge22" class="edge">
<title>app/layout.tsx&#45;&gt;src/client/infrastructure/components/ui/Background/Background.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.58,-7182.26C138.85,-7182.13 160.33,-7178.63 172.25,-7164 183.31,-7150.43 167.86,-2135.37 180.25,-2123 416.33,-1887.29 696.22,-2105.29 885.5,-2380 956.46,-2482.99 988.65,-2894.72 995.04,-2986.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="992.94,-2986.81 995.44,-2992.65 997.13,-2986.53 992.94,-2986.81"/>
</g>
<!-- src/client/infrastructure/layouts/RootLayout/RootLayout.tsx -->
<g id="node31" class="node">
<title>src/client/infrastructure/layouts/RootLayout/RootLayout.tsx</title>
<g id="a_node31"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/layouts/RootLayout/RootLayout.tsx" xlink:title="RootLayout.tsx">
<polygon fill="#6cbaff" stroke="black" points="558.62,-4298.25 481.88,-4298.25 481.88,-4279.75 558.62,-4279.75 558.62,-4298.25"/>
<text text-anchor="start" x="489.88" y="-4285.7" font-family="Helvetica,sans-Serif" font-size="9.00">RootLayout.tsx</text>
</a>
</g>
</g>
<!-- app/layout.tsx&#45;&gt;src/client/infrastructure/layouts/RootLayout/RootLayout.tsx -->
<g id="edge23" class="edge">
<title>app/layout.tsx&#45;&gt;src/client/infrastructure/layouts/RootLayout/RootLayout.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M121.58,-7182.26C138.85,-7182.13 160.33,-7178.63 172.25,-7164 183.21,-7150.55 167.97,-2179.26 180.25,-2167 235.13,-2112.2 299.8,-2112.27 354.75,-2167 364.64,-2176.86 356.31,-4165.61 362.75,-4178 388.3,-4227.2 446.76,-4259.34 484.55,-4275.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="483.62,-4277.69 489.96,-4278.1 485.25,-4273.82 483.62,-4277.69"/>
</g>
<!-- src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx -->
<g id="edge223" class="edge">
<title>src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ToastProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M558.95,-4289C597.6,-4289 657.93,-4289 700.67,-4289"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="700.48,-4291.1 706.48,-4289 700.48,-4286.9 700.48,-4291.1"/>
</g>
<!-- src/client/infrastructure/providers/ConvexClientProvider.tsx -->
<g id="node136" class="node">
<title>src/client/infrastructure/providers/ConvexClientProvider.tsx</title>
<g id="a_node136"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/providers/ConvexClientProvider.tsx" xlink:title="ConvexClientProvider.tsx">
<polygon fill="#6cbaff" stroke="black" points="811.5,-4329.25 692.75,-4329.25 692.75,-4310.75 811.5,-4310.75 811.5,-4329.25"/>
<text text-anchor="start" x="700.75" y="-4316.7" font-family="Helvetica,sans-Serif" font-size="9.00">ConvexClientProvider.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ConvexClientProvider.tsx -->
<g id="edge222" class="edge">
<title>src/client/infrastructure/layouts/RootLayout/RootLayout.tsx&#45;&gt;src/client/infrastructure/providers/ConvexClientProvider.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M558.95,-4294.08C592.65,-4298.63 642.84,-4305.4 683.48,-4310.88"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="683.03,-4312.94 689.26,-4311.66 683.59,-4308.77 683.03,-4312.94"/>
</g>
<!-- app/page.tsx -->
<g id="node32" class="node">
<title>app/page.tsx</title>
<g id="a_node32"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/app/page.tsx" xlink:title="page.tsx">
<path fill="#bbfeff" stroke="black" d="M114.96,-7158.25C114.96,-7158.25 73.29,-7158.25 73.29,-7158.25 70.21,-7158.25 67.12,-7155.17 67.12,-7152.08 67.12,-7152.08 67.12,-7145.92 67.12,-7145.92 67.12,-7142.83 70.21,-7139.75 73.29,-7139.75 73.29,-7139.75 114.96,-7139.75 114.96,-7139.75 118.04,-7139.75 121.12,-7142.83 121.12,-7145.92 121.12,-7145.92 121.12,-7152.08 121.12,-7152.08 121.12,-7155.17 118.04,-7158.25 114.96,-7158.25"/>
<text text-anchor="start" x="76.88" y="-7145.7" font-family="Helvetica,sans-Serif" font-size="9.00">page.tsx</text>
</a>
</g>
</g>
<!-- app/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx -->
<g id="edge24" class="edge">
<title>app/page.tsx&#45;&gt;src/client/infrastructure/components/redirections/RedirectToGameList/RedirectToGameList.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M95.1,-7139.27C94.45,-6897.17 83.26,-2218.36 180.25,-2123 235.55,-2068.63 299.3,-2068.77 354.75,-2123 369.01,-2136.95 357.91,-2462.65 362.75,-2482 392.95,-2602.84 476.33,-2730.53 507.39,-2775.32"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="505.5,-2776.28 510.66,-2779.99 508.94,-2773.87 505.5,-2776.28"/>
</g>
<!-- src/client/application/services/DeckDraftService.ts -->
<g id="node34" class="node">
<title>src/client/application/services/DeckDraftService.ts</title>
<g id="a_node34"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/services/DeckDraftService.ts" xlink:title="DeckDraftService.ts">
<polygon fill="#fb6969" stroke="black" points="1735.5,-2037.25 1639.25,-2037.25 1639.25,-2018.75 1735.5,-2018.75 1735.5,-2037.25"/>
<text text-anchor="start" x="1647.25" y="-2024.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckDraftService.ts</text>
</a>
</g>
</g>
<!-- src/client/application/appStore.ts&#45;&gt;src/client/application/services/DeckDraftService.ts -->
<g id="edge25" class="edge">
<title>src/client/application/appStore.ts&#45;&gt;src/client/application/services/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1572.74,-1983.62C1586.69,-1992.89 1609.1,-2006.69 1630.25,-2015 1631.11,-2015.34 1631.99,-2015.67 1632.87,-2016"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1632.01,-2017.92 1638.37,-2017.86 1633.36,-2013.94 1632.01,-2017.92"/>
</g>
<!-- src/client/application/services/LocationService.ts -->
<g id="node35" class="node">
<title>src/client/application/services/LocationService.ts</title>
<g id="a_node35"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/services/LocationService.ts" xlink:title="LocationService.ts">
<polygon fill="#fb6969" stroke="black" points="1732.88,-2068.25 1641.88,-2068.25 1641.88,-2049.75 1732.88,-2049.75 1732.88,-2068.25"/>
<text text-anchor="start" x="1649.88" y="-2055.7" font-family="Helvetica,sans-Serif" font-size="9.00">LocationService.ts</text>
</a>
</g>
</g>
<!-- src/client/application/appStore.ts&#45;&gt;src/client/application/services/LocationService.ts -->
<g id="edge26" class="edge">
<title>src/client/application/appStore.ts&#45;&gt;src/client/application/services/LocationService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1566.12,-1983.54C1577.92,-1998.44 1602.78,-2027.16 1630.25,-2043 1632.49,-2044.29 1634.84,-2045.48 1637.25,-2046.58"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1636.03,-2048.35 1642.38,-2048.71 1637.64,-2044.47 1636.03,-2048.35"/>
</g>
<!-- src/client/domain/Catalog/catalogFiltersReducer.ts -->
<g id="node36" class="node">
<title>src/client/domain/Catalog/catalogFiltersReducer.ts</title>
<g id="a_node36"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/Catalog/catalogFiltersReducer.ts" xlink:title="catalogFiltersReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1744.5,-448.25 1630.25,-448.25 1630.25,-429.75 1744.5,-429.75 1744.5,-448.25"/>
<text text-anchor="start" x="1638.25" y="-435.7" font-family="Helvetica,sans-Serif" font-size="9.00">catalogFiltersReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts -->
<g id="edge27" class="edge">
<title>src/client/application/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1589.34,-1973.98C1599.81,-1972.31 1610.57,-1968.4 1617.25,-1960 1642.38,-1928.42 1616.43,-544.92 1630.25,-507 1637.84,-486.18 1654.79,-467.16 1668.09,-454.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1669.39,-456.22 1672.41,-450.63 1666.56,-453.12 1669.39,-456.22"/>
</g>
<!-- src/client/domain/Catalog/catalogReducer.ts -->
<g id="node37" class="node">
<title>src/client/domain/Catalog/catalogReducer.ts</title>
<g id="a_node37"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/Catalog/catalogReducer.ts" xlink:title="catalogReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1602.62,-367.25 1513.12,-367.25 1513.12,-348.75 1602.62,-348.75 1602.62,-367.25"/>
<text text-anchor="start" x="1521.12" y="-354.7" font-family="Helvetica,sans-Serif" font-size="9.00">catalogReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogReducer.ts -->
<g id="edge28" class="edge">
<title>src/client/application/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1526.45,-1977.13C1506.8,-1977.12 1484.18,-1972.77 1478.5,-1954 1475.35,-1943.6 1475.35,-399.4 1478.5,-389 1482.19,-376.81 1492.38,-369.31 1504.28,-364.73"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1504.79,-366.77 1509.86,-362.93 1503.5,-362.77 1504.79,-366.77"/>
</g>
<!-- src/client/domain/Catalog/catalogSearchReducer.ts -->
<g id="node38" class="node">
<title>src/client/domain/Catalog/catalogSearchReducer.ts</title>
<g id="a_node38"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/Catalog/catalogSearchReducer.ts" xlink:title="catalogSearchReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1617.25,-440.25 1498.5,-440.25 1498.5,-421.75 1617.25,-421.75 1617.25,-440.25"/>
<text text-anchor="start" x="1506.5" y="-427.7" font-family="Helvetica,sans-Serif" font-size="9.00">catalogSearchReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogSearchReducer.ts -->
<g id="edge29" class="edge">
<title>src/client/application/appStore.ts&#45;&gt;src/client/domain/Catalog/catalogSearchReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1526.45,-1977.13C1506.8,-1977.12 1484.18,-1972.77 1478.5,-1954 1473.63,-1937.92 1477.78,-1365.79 1478.5,-1349 1494.17,-985.85 1544.55,-544.15 1555.69,-449.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1557.77,-449.69 1556.39,-443.48 1553.6,-449.2 1557.77,-449.69"/>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="node39" class="node">
<title>src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<g id="a_node39"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/DeckBuilder/deckBuilderReducer.ts" xlink:title="deckBuilderReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1867.75,-209.25 1759.5,-209.25 1759.5,-190.75 1867.75,-190.75 1867.75,-209.25"/>
<text text-anchor="start" x="1767.5" y="-196.7" font-family="Helvetica,sans-Serif" font-size="9.00">deckBuilderReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/appStore.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge30" class="edge">
<title>src/client/application/appStore.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1589.59,-1973.58C1599.83,-1971.82 1610.37,-1967.96 1617.25,-1960 1833.52,-1709.84 1731,-808.05 1751.5,-478 1753.13,-451.7 1747.48,-264.45 1759.5,-241 1765.13,-230.01 1775.21,-221.09 1785.08,-214.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1785.98,-216.33 1789.95,-211.36 1783.75,-212.77 1785.98,-216.33"/>
</g>
<!-- src/client/domain/GameSettings/gameSettingsReducer.ts -->
<g id="node40" class="node">
<title>src/client/domain/GameSettings/gameSettingsReducer.ts</title>
<g id="a_node40"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/GameSettings/gameSettingsReducer.ts" xlink:title="gameSettingsReducer.ts">
<polygon fill="#fa9f36" stroke="black" points="1615.75,-284.25 1500,-284.25 1500,-265.75 1615.75,-265.75 1615.75,-284.25"/>
<text text-anchor="start" x="1508" y="-271.7" font-family="Helvetica,sans-Serif" font-size="9.00">gameSettingsReducer.ts</text>
</a>
</g>
</g>
<!-- src/client/application/appStore.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsReducer.ts -->
<g id="edge31" class="edge">
<title>src/client/application/appStore.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1526.45,-1977.13C1506.8,-1977.12 1484.18,-1972.77 1478.5,-1954 1475.22,-1943.15 1475.22,-332.85 1478.5,-322 1483.36,-305.95 1497.53,-295.16 1512.58,-288.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1513.03,-290.11 1517.71,-285.81 1511.36,-286.26 1513.03,-290.11"/>
</g>
<!-- src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="node71" class="node">
<title>src/client/domain/Catalog/catalogFilterEvents.ts</title>
<g id="a_node71"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/Catalog/catalogFilterEvents.ts" xlink:title="catalogFilterEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1865.12,-415.25 1762.12,-415.25 1762.12,-396.75 1865.12,-396.75 1865.12,-415.25"/>
<text text-anchor="start" x="1770.12" y="-402.7" font-family="Helvetica,sans-Serif" font-size="9.00">catalogFilterEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge111" class="edge">
<title>src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1727.14,-429.33C1735.2,-427.27 1743.63,-425.09 1751.5,-423 1757.5,-421.41 1763.82,-419.69 1770.03,-417.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1770.25,-420.1 1775.48,-416.48 1769.13,-416.05 1770.25,-420.1"/>
</g>
<!-- src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge112" class="edge">
<title>src/client/domain/Catalog/catalogFiltersReducer.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1744.84,-439C1791.28,-439 1855.66,-439 1895.12,-439"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1895.08,-441.1 1901.08,-439 1895.08,-436.9 1895.08,-441.1"/>
</g>
<!-- src/client/domain/Catalog/catalogEvents.ts -->
<g id="node54" class="node">
<title>src/client/domain/Catalog/catalogEvents.ts</title>
<g id="a_node54"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/Catalog/catalogEvents.ts" xlink:title="catalogEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1728.75,-345.25 1646,-345.25 1646,-326.75 1728.75,-326.75 1728.75,-345.25"/>
<text text-anchor="start" x="1654" y="-332.7" font-family="Helvetica,sans-Serif" font-size="9.00">catalogEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts -->
<g id="edge113" class="edge">
<title>src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1603.11,-350.37C1614.01,-348.49 1625.75,-346.46 1636.87,-344.54"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1637.1,-346.63 1642.66,-343.54 1636.39,-342.49 1637.1,-346.63"/>
</g>
<!-- src/client/domain/Catalog/CatalogCard.ts -->
<g id="node55" class="node">
<title>src/client/domain/Catalog/CatalogCard.ts</title>
<g id="a_node55"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/Catalog/CatalogCard.ts" xlink:title="CatalogCard.ts">
<polygon fill="#fa9f36" stroke="black" points="1852.38,-359.25 1774.88,-359.25 1774.88,-340.75 1852.38,-340.75 1852.38,-359.25"/>
<text text-anchor="start" x="1782.88" y="-346.7" font-family="Helvetica,sans-Serif" font-size="9.00">CatalogCard.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge114" class="edge">
<title>src/client/domain/Catalog/catalogReducer.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1603.07,-356.61C1648.6,-355.17 1719.28,-352.94 1765.76,-351.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1765.77,-353.58 1771.7,-351.29 1765.63,-349.38 1765.77,-353.58"/>
</g>
<!-- src/client/domain/Catalog/catalogSearchEvents.ts -->
<g id="node64" class="node">
<title>src/client/domain/Catalog/catalogSearchEvents.ts</title>
<g id="a_node64"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/Catalog/catalogSearchEvents.ts" xlink:title="catalogSearchEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1743.38,-417.25 1631.38,-417.25 1631.38,-398.75 1743.38,-398.75 1743.38,-417.25"/>
<text text-anchor="start" x="1639.38" y="-404.7" font-family="Helvetica,sans-Serif" font-size="9.00">catalogSearchEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/Catalog/catalogSearchReducer.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts -->
<g id="edge115" class="edge">
<title>src/client/domain/Catalog/catalogSearchReducer.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1612.58,-421.31C1616.25,-420.65 1619.97,-419.98 1623.69,-419.31"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1623.82,-421.42 1629.35,-418.29 1623.07,-417.29 1623.82,-421.42"/>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="node45" class="node">
<title>src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<g id="a_node45"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/DeckBuilder/deckBuilderEvents.ts" xlink:title="deckBuilderEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1982.25,-195.25 1880.75,-195.25 1880.75,-176.75 1982.25,-176.75 1982.25,-195.25"/>
<text text-anchor="start" x="1888.75" y="-182.7" font-family="Helvetica,sans-Serif" font-size="9.00">deckBuilderEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge122" class="edge">
<title>src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1868.15,-193.53C1869.29,-193.4 1870.42,-193.26 1871.56,-193.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1871.55,-195.24 1877.25,-192.43 1871.04,-191.07 1871.55,-195.24"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="node94" class="node">
<title>src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<g id="a_node94"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/DeckBuilder/DeckBuilderCard.ts" xlink:title="DeckBuilderCard.ts">
<polygon fill="#fa9f36" stroke="black" points="2090.75,-209.25 1995.25,-209.25 1995.25,-190.75 2090.75,-190.75 2090.75,-209.25"/>
<text text-anchor="start" x="2003.25" y="-196.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilderCard.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge121" class="edge">
<title>src/client/domain/DeckBuilder/deckBuilderReducer.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1868.04,-200.88C1872.34,-200.93 1876.62,-200.97 1880.75,-201 1925.86,-201.34 1937.14,-201.38 1982.25,-201 1983.55,-200.99 1984.86,-200.98 1986.18,-200.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1986.03,-203.07 1992.01,-200.9 1985.98,-198.87 1986.03,-203.07"/>
</g>
<!-- src/client/domain/GameSettings/gameSettingsEvents.ts -->
<g id="node60" class="node">
<title>src/client/domain/GameSettings/gameSettingsEvents.ts</title>
<g id="a_node60"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/GameSettings/gameSettingsEvents.ts" xlink:title="gameSettingsEvents.ts">
<polygon fill="#fa9f36" stroke="black" points="1741.88,-270.25 1632.88,-270.25 1632.88,-251.75 1741.88,-251.75 1741.88,-270.25"/>
<text text-anchor="start" x="1640.88" y="-257.7" font-family="Helvetica,sans-Serif" font-size="9.00">gameSettingsEvents.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts -->
<g id="edge125" class="edge">
<title>src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1615.91,-268.74C1618.52,-268.45 1621.15,-268.16 1623.77,-267.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1623.91,-269.97 1629.65,-267.23 1623.45,-265.8 1623.91,-269.97"/>
</g>
<!-- src/client/domain/GameSettings/GameSettings.ts -->
<g id="node95" class="node">
<title>src/client/domain/GameSettings/GameSettings.ts</title>
<g id="a_node95"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/GameSettings/GameSettings.ts" xlink:title="GameSettings.ts">
<polygon fill="#fa9f36" stroke="black" points="1855,-277.25 1772.25,-277.25 1772.25,-258.75 1855,-258.75 1855,-277.25"/>
<text text-anchor="start" x="1780.25" y="-264.7" font-family="Helvetica,sans-Serif" font-size="9.00">GameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts -->
<g id="edge124" class="edge">
<title>src/client/domain/GameSettings/gameSettingsReducer.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1615.83,-277.39C1654.21,-278.49 1705.95,-278.97 1751.5,-276 1755.33,-275.75 1759.28,-275.41 1763.25,-275.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1763.18,-277.14 1768.92,-274.4 1762.73,-272.97 1763.18,-277.14"/>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts -->
<g id="node41" class="node">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts</title>
<g id="a_node41"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/addCardToDeck/addCardToDeck.ts" xlink:title="addCardToDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1250.25,-735.25 1158.5,-735.25 1158.5,-716.75 1250.25,-716.75 1250.25,-735.25"/>
<text text-anchor="start" x="1166.5" y="-722.7" font-family="Helvetica,sans-Serif" font-size="9.00">addCardToDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge33" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.54,-735.71C1267.44,-741.76 1284.96,-751.48 1295,-767 1304.29,-781.36 1290.87,-1985.95 1303,-1998 1330.04,-2024.86 1436.77,-2003.38 1474.5,-1998 1490.53,-1995.71 1507.79,-1991.04 1522.35,-1986.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1522.66,-1988.53 1527.71,-1984.67 1521.35,-1984.54 1522.66,-1988.53"/>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById.ts -->
<g id="edge32" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.48,-735.75C1267.37,-741.81 1284.9,-751.52 1295,-767 1311.61,-792.45 1288.2,-1287.45 1303,-1314 1311.98,-1330.11 1328.72,-1341.71 1344.27,-1349.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1343.19,-1351.38 1349.52,-1352.05 1344.99,-1347.59 1343.19,-1351.38"/>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts -->
<g id="node43" class="node">
<title>src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts</title>
<g id="a_node43"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts" xlink:title="addCardToDeckRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1443.88,-735.25 1317.62,-735.25 1317.62,-716.75 1443.88,-716.75 1443.88,-735.25"/>
<text text-anchor="start" x="1325.62" y="-722.7" font-family="Helvetica,sans-Serif" font-size="9.00">addCardToDeckRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts -->
<g id="edge34" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeckRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.73,-726C1268.32,-726 1288.89,-726 1308.37,-726"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1308.2,-728.1 1314.2,-726 1308.2,-723.9 1308.2,-728.1"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="node44" class="node">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<g id="a_node44"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/DeckBuilder/DeckBuilder.ts" xlink:title="DeckBuilder.ts">
<polygon fill="#fa9f36" stroke="black" points="1724.62,-196.25 1650.12,-196.25 1650.12,-177.75 1724.62,-177.75 1724.62,-196.25"/>
<text text-anchor="start" x="1658.12" y="-183.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuilder.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge35" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.71,-725.86C1267.22,-723.32 1284.4,-717.27 1295,-704 1310.88,-684.11 1286.6,-265.46 1303,-246 1385.95,-147.62 1558.67,-163.24 1640.93,-177.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1640.53,-179.54 1646.81,-178.54 1641.27,-175.41 1640.53,-179.54"/>
</g>
<!-- src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge36" class="edge">
<title>src/client/application/commands/addCardToDeck/addCardToDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.71,-725.86C1267.22,-723.32 1284.4,-717.27 1295,-704 1310.88,-684.11 1288.44,-266.87 1303,-246 1423.54,-73.17 1548.79,-118.82 1759.5,-118 1807.61,-117.81 1823.68,-98.7 1867.75,-118 1891.4,-128.36 1910.08,-152.51 1920.72,-169.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1918.76,-169.88 1923.69,-173.89 1922.34,-167.68 1918.76,-169.88"/>
</g>
<!-- src/client/application/queries/getCatalogCardById.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge87" class="edge">
<title>src/client/application/queries/getCatalogCardById.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1435.15,-1360.21C1450.01,-1362.29 1464.74,-1367.57 1474.5,-1379 1495.47,-1403.55 1461.72,-1935.29 1482.5,-1960 1491.02,-1970.13 1504.35,-1974.46 1517.39,-1975.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.15,-1978.06 1523.29,-1976.4 1517.46,-1973.88 1517.15,-1978.06"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge118" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1724.98,-190.83C1733.04,-191.67 1741.78,-192.59 1750.53,-193.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1750.05,-195.56 1756.23,-194.1 1750.49,-191.39 1750.05,-195.56"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge117" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1724.92,-185.78C1736.02,-185.46 1748.26,-185.16 1759.5,-185 1807.61,-184.32 1819.64,-184.62 1867.75,-185 1869.04,-185.01 1870.35,-185.02 1871.67,-185.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1871.44,-187.13 1877.47,-185.1 1871.49,-182.93 1871.44,-187.13"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge116" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilder.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1705.8,-196.72C1720.66,-206.01 1741.19,-221.49 1751.5,-241 1766.75,-269.86 1742.66,-286.04 1759.5,-314 1764.92,-323.01 1773.57,-330.26 1782.32,-335.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1781.11,-337.56 1787.35,-338.78 1783.23,-333.94 1781.11,-337.56"/>
</g>
<!-- src/client/domain/DeckBuilder/deckBuilderEvents.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge120" class="edge">
<title>src/client/domain/DeckBuilder/deckBuilderEvents.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1982.44,-192.39C1983.75,-192.55 1985.06,-192.72 1986.36,-192.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1985.76,-194.93 1991.98,-193.6 1986.29,-190.76 1985.76,-194.93"/>
</g>
<!-- src/client/application/commands/clearDeckDraft/clearDeckDraft.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge37" class="edge">
<title>src/client/application/commands/clearDeckDraft/clearDeckDraft.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1247.71,-1161.74C1265.45,-1167.71 1284.39,-1177.63 1295,-1194 1308.63,-1215.02 1285.2,-2078.36 1303,-2096 1316.53,-2109.41 1457.67,-2104.94 1474.5,-2096 1516.25,-2073.82 1540.67,-2019.99 1551.01,-1991.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1552.87,-1992.88 1552.88,-1986.53 1548.91,-1991.49 1552.87,-1992.88"/>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalog.ts -->
<g id="node47" class="node">
<title>src/client/application/commands/filterCatalog/filterCatalog.ts</title>
<g id="a_node47"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/filterCatalog/filterCatalog.ts" xlink:title="filterCatalog.ts">
<polygon fill="#fb6969" stroke="black" points="1418,-1223.25 1343.5,-1223.25 1343.5,-1204.75 1418,-1204.75 1418,-1223.25"/>
<text text-anchor="start" x="1351.5" y="-1210.7" font-family="Helvetica,sans-Serif" font-size="9.00">filterCatalog.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge38" class="edge">
<title>src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1418.35,-1220.26C1438.54,-1225.78 1462,-1236.04 1474.5,-1255 1485.28,-1271.35 1469.91,-1945 1482.5,-1960 1491.01,-1970.14 1504.34,-1974.47 1517.38,-1975.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.14,-1978.08 1523.28,-1976.41 1517.44,-1973.89 1517.14,-1978.08"/>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts -->
<g id="node48" class="node">
<title>src/client/domain/Catalog/CatalogFilters.ts</title>
<g id="a_node48"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/Catalog/CatalogFilters.ts" xlink:title="CatalogFilters.ts">
<polygon fill="#fa9f36" stroke="black" points="1598.88,-407.25 1516.88,-407.25 1516.88,-388.75 1598.88,-388.75 1598.88,-407.25"/>
<text text-anchor="start" x="1524.88" y="-394.7" font-family="Helvetica,sans-Serif" font-size="9.00">CatalogFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/domain/Catalog/CatalogFilters.ts -->
<g id="edge39" class="edge">
<title>src/client/application/commands/filterCatalog/filterCatalog.ts&#45;&gt;src/client/domain/Catalog/CatalogFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1387.93,-1204.28C1405.68,-1175.72 1456.66,-1089.07 1474.5,-1009 1478.08,-992.92 1472.23,-428.88 1482.5,-416 1488.84,-408.05 1498.05,-403.24 1507.83,-400.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1508.2,-402.48 1513.57,-399.09 1507.25,-398.39 1508.2,-402.48"/>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts -->
<g id="edge107" class="edge">
<title>src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFiltersReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1594.15,-407.66C1601.86,-410.11 1609.91,-412.93 1617.25,-416 1623.3,-418.53 1624.17,-420.53 1630.25,-423 1633.29,-424.24 1636.46,-425.41 1639.68,-426.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1639.03,-428.53 1645.38,-428.4 1640.33,-424.54 1639.03,-428.53"/>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge106" class="edge">
<title>src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1599.21,-393.9C1638.27,-390.65 1699.1,-387.49 1751.5,-393 1755.6,-393.43 1759.83,-394.02 1764.06,-394.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1763.45,-396.74 1769.72,-395.72 1764.19,-392.6 1763.45,-396.74"/>
</g>
<!-- src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge108" class="edge">
<title>src/client/domain/Catalog/CatalogFilters.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1598.34,-388.34C1658.5,-375.52 1775.84,-358.1 1867.75,-391 1886.32,-397.65 1903.82,-411.97 1915.6,-423.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1913.96,-424.58 1919.69,-427.32 1916.92,-421.6 1913.96,-424.58"/>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts -->
<g id="node49" class="node">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts</title>
<g id="a_node49"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/hideCardDetails/hideCardDetails.ts" xlink:title="hideCardDetails.ts">
<polygon fill="#fb6969" stroke="black" points="1250.25,-552.25 1158.5,-552.25 1158.5,-533.75 1250.25,-533.75 1250.25,-552.25"/>
<text text-anchor="start" x="1166.5" y="-539.7" font-family="Helvetica,sans-Serif" font-size="9.00">hideCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge40" class="edge">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.55,-552.7C1267.45,-558.76 1284.97,-568.48 1295,-584 1305.37,-600.05 1289.82,-1946.16 1303,-1960 1306.65,-1963.83 1447.33,-1969.78 1517.67,-1972.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.19,-1974.61 1523.27,-1972.74 1517.35,-1970.41 1517.19,-1974.61"/>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge41" class="edge">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1205.81,-533.47C1208.78,-472.49 1228.41,-137.58 1303,-77 1357.21,-32.97 1556.13,-43.22 1617.25,-77 1652.77,-96.63 1672.5,-142.99 1681.1,-168.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1679.06,-169.34 1682.86,-174.44 1683.07,-168.08 1679.06,-169.34"/>
</g>
<!-- src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge42" class="edge">
<title>src/client/application/commands/hideCardDetails/hideCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1205.77,-533.63C1208.57,-472.16 1227.53,-127.18 1303,-63 1346.01,-26.42 1500.41,-53 1556.88,-53 1556.88,-53 1556.88,-53 1688.38,-53 1768.22,-53 1800.85,-19.42 1867.75,-63 1904.58,-86.99 1920.85,-139.73 1927.11,-167.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1925.04,-168.08 1928.31,-173.53 1929.15,-167.23 1925.04,-168.08"/>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts -->
<g id="node50" class="node">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts</title>
<g id="a_node50"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts" xlink:title="initializeDeckBuilderFromLocation.ts">
<polygon fill="#fb6969" stroke="black" points="1287,-1223.25 1121.75,-1223.25 1121.75,-1204.75 1287,-1204.75 1287,-1223.25"/>
<text text-anchor="start" x="1129.75" y="-1210.7" font-family="Helvetica,sans-Serif" font-size="9.00">initializeDeckBuilderFromLocation.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge43" class="edge">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.52,-1223.72C1267.42,-1229.78 1284.94,-1239.5 1295,-1255 1307.93,-1274.92 1286.13,-2093.28 1303,-2110 1316.54,-2123.41 1457.9,-2119.35 1474.5,-2110 1519.83,-2084.46 1543.07,-2022.79 1552.17,-1992.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1554.13,-1992.84 1553.73,-1986.49 1550.09,-1991.7 1554.13,-1992.84"/>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts -->
<g id="edge44" class="edge">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1287.46,-1214C1303.7,-1214 1320.17,-1214 1334.62,-1214"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1334.31,-1216.1 1340.31,-1214 1334.31,-1211.9 1334.31,-1216.1"/>
</g>
<!-- src/client/application/commands/search/search.ts -->
<g id="node51" class="node">
<title>src/client/application/commands/search/search.ts</title>
<g id="a_node51"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/search/search.ts" xlink:title="search.ts">
<polygon fill="#fb6969" stroke="black" points="1407.75,-1284.25 1353.75,-1284.25 1353.75,-1265.75 1407.75,-1265.75 1407.75,-1284.25"/>
<text text-anchor="start" x="1362.38" y="-1271.7" font-family="Helvetica,sans-Serif" font-size="9.00">search.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/search/search.ts -->
<g id="edge45" class="edge">
<title>src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/search/search.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1227.62,-1223.63C1247.26,-1231.96 1276.75,-1244.02 1303,-1253 1316.56,-1257.64 1331.68,-1262.12 1344.89,-1265.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1344,-1267.74 1350.34,-1267.31 1345.11,-1263.69 1344,-1267.74"/>
</g>
<!-- src/client/application/commands/search/search.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge65" class="edge">
<title>src/client/application/commands/search/search.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1405.35,-1284.73C1427.71,-1295.38 1459.97,-1314.78 1474.5,-1343 1482.35,-1358.24 1471.47,-1946.88 1482.5,-1960 1491.02,-1970.13 1504.35,-1974.46 1517.39,-1975.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.15,-1978.07 1523.29,-1976.4 1517.45,-1973.88 1517.15,-1978.07"/>
</g>
<!-- src/client/application/commands/search/search.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts -->
<g id="edge66" class="edge">
<title>src/client/application/commands/search/search.ts&#45;&gt;src/client/domain/Catalog/catalogSearchEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1407.89,-1276.03C1428.85,-1275.37 1457.48,-1270.84 1474.5,-1253 1539.19,-1185.18 1567.1,-493.25 1630.25,-424 1630.53,-423.69 1630.82,-423.39 1631.11,-423.09"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1632.19,-424.92 1635.57,-419.53 1629.57,-421.63 1632.19,-424.92"/>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts -->
<g id="node52" class="node">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts</title>
<g id="a_node52"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/loadCatalogCards/loadCatalogCards.ts" xlink:title="loadCatalogCards.ts">
<polygon fill="#fb6969" stroke="black" points="1254.38,-796.25 1154.38,-796.25 1154.38,-777.75 1254.38,-777.75 1254.38,-796.25"/>
<text text-anchor="start" x="1162.38" y="-783.7" font-family="Helvetica,sans-Serif" font-size="9.00">loadCatalogCards.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge46" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.54,-796.71C1267.44,-802.76 1284.96,-812.48 1295,-828 1303.93,-841.81 1291.33,-2000.41 1303,-2012 1330.04,-2038.86 1437.28,-2020.17 1474.5,-2012 1495.46,-2007.4 1517.44,-1996.93 1533.42,-1988.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1534.37,-1990.02 1538.56,-1985.24 1532.3,-1986.36 1534.37,-1990.02"/>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts -->
<g id="node53" class="node">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts</title>
<g id="a_node53"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts" xlink:title="loadCatalogCardsRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1448,-796.25 1313.5,-796.25 1313.5,-777.75 1448,-777.75 1448,-796.25"/>
<text text-anchor="start" x="1321.5" y="-783.7" font-family="Helvetica,sans-Serif" font-size="9.00">loadCatalogCardsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts -->
<g id="edge47" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1254.41,-787C1269.94,-787 1287.48,-787 1304.41,-787"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1304.16,-789.1 1310.16,-787 1304.16,-784.9 1304.16,-789.1"/>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts -->
<g id="edge48" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCards.ts&#45;&gt;src/client/domain/Catalog/catalogEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1254.74,-785.99C1269.89,-783.08 1285.14,-777.01 1295,-765 1307.77,-749.44 1297.76,-603.44 1303,-584 1347.76,-417.87 1335.31,-330.1 1482.5,-241 1533.73,-209.99 1571.34,-202.54 1617.25,-241 1642.51,-262.16 1609.36,-288.52 1630.25,-314 1632.68,-316.96 1635.55,-319.52 1638.71,-321.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1637.63,-323.54 1643.86,-324.78 1639.76,-319.93 1637.63,-323.54"/>
</g>
<!-- src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge49" class="edge">
<title>src/client/application/commands/loadCatalogCards/loadCatalogCardsRequest.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1445.63,-777.3C1455.71,-774.26 1465.69,-770.26 1474.5,-765 1633.45,-670.15 1674.85,-622.48 1751.5,-454 1763.19,-428.31 1745.48,-415.5 1759.5,-391 1765.73,-380.12 1776.1,-371.15 1786.04,-364.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1786.98,-366.28 1790.92,-361.29 1784.73,-362.74 1786.98,-366.28"/>
</g>
<!-- src/client/domain/Catalog/catalogEvents.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts -->
<g id="edge109" class="edge">
<title>src/client/domain/Catalog/catalogEvents.ts&#45;&gt;src/client/domain/Catalog/CatalogCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1729.06,-340.58C1740.79,-341.91 1753.69,-343.36 1765.76,-344.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1765.17,-346.77 1771.37,-345.35 1765.64,-342.59 1765.17,-346.77"/>
</g>
<!-- src/client/domain/CardData/CardData..ts -->
<g id="node74" class="node">
<title>src/client/domain/CardData/CardData..ts</title>
<g id="a_node74"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/domain/CardData/CardData..ts" xlink:title="CardData..ts">
<polygon fill="#fa9f36" stroke="black" points="2181.75,-448.25 2114.75,-448.25 2114.75,-429.75 2181.75,-429.75 2181.75,-448.25"/>
<text text-anchor="start" x="2122.75" y="-435.7" font-family="Helvetica,sans-Serif" font-size="9.00">CardData..ts</text>
</a>
</g>
</g>
<!-- src/client/domain/Catalog/CatalogCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge105" class="edge">
<title>src/client/domain/Catalog/CatalogCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1850.85,-359.69C1913.22,-376.38 2040.45,-410.42 2106.07,-427.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2105.27,-429.94 2111.6,-429.46 2106.35,-425.88 2105.27,-429.94"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge50" class="edge">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1246.32,-1039.28C1264.46,-1045.19 1284.11,-1055.2 1295,-1072 1310.04,-1095.22 1283.36,-2048.51 1303,-2068 1316.53,-2081.42 1457.14,-2075.87 1474.5,-2068 1509.2,-2052.27 1535.05,-2013.9 1547.87,-1991.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1549.64,-1992.48 1550.68,-1986.21 1545.95,-1990.47 1549.64,-1992.48"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/queries/getCatalogCardById.ts -->
<g id="edge51" class="edge">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/application/queries/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1246.6,-1039.55C1264.57,-1045.51 1284,-1055.48 1295,-1072 1309.91,-1094.39 1289.68,-1290.62 1303,-1314 1312.13,-1330.03 1328.88,-1341.62 1344.4,-1349.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1343.31,-1351.3 1349.63,-1351.98 1345.11,-1347.51 1343.31,-1351.3"/>
</g>
<!-- src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge52" class="edge">
<title>src/client/application/commands/loadDeckDraft/loadDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1246.58,-1031.47C1264.31,-1029.35 1283.56,-1023.44 1295,-1009 1308.42,-992.06 1288.5,-247.03 1303,-231 1328.72,-202.56 1437.03,-220.12 1474.5,-212 1492.3,-208.14 1612.28,-163.94 1630.25,-161 1686.94,-151.73 1702.05,-160.22 1759.5,-160 1807.61,-159.81 1820.56,-150.61 1867.75,-160 1880.28,-162.49 1893.45,-167.54 1904.47,-172.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1903.37,-174.33 1909.69,-174.99 1905.16,-170.53 1903.37,-174.33"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge53" class="edge">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.53,-1101.72C1267.43,-1107.77 1284.95,-1117.49 1295,-1133 1309.34,-1155.12 1284.28,-2063.44 1303,-2082 1316.53,-2095.42 1457.42,-2090.45 1474.5,-2082 1512.69,-2063.1 1538.01,-2017.07 1549.6,-1991.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1551.43,-1992.68 1551.91,-1986.34 1547.58,-1991 1551.43,-1992.68"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/queries/getCatalogCardById.ts -->
<g id="edge54" class="edge">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/application/queries/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1249.78,-1101.73C1266.78,-1107.8 1284.58,-1117.53 1295,-1133 1317.5,-1166.39 1282.88,-1279.13 1303,-1314 1312.29,-1330.09 1329.22,-1341.73 1344.82,-1349.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1343.76,-1351.44 1350.08,-1352.11 1345.55,-1347.65 1343.76,-1351.44"/>
</g>
<!-- src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge55" class="edge">
<title>src/client/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1258.22,-1090.47C1272.26,-1087.34 1286.01,-1081.27 1295,-1070 1303.42,-1059.44 1299.67,-597.08 1303,-584 1345.45,-417.26 1353.37,-354.71 1482.5,-241 1530.44,-198.78 1566.91,-230.32 1617.25,-191 1625.31,-184.7 1621.2,-176.77 1630.25,-172 1677.91,-146.86 1697.65,-169.9 1751.5,-172 1791.7,-173.57 1836.86,-177.11 1871.77,-180.24"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1871.23,-182.3 1877.39,-180.76 1871.61,-178.12 1871.23,-182.3"/>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts -->
<g id="node58" class="node">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts</title>
<g id="a_node58"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/loadGameSettings/loadGameSettings.ts" xlink:title="loadGameSettings.ts">
<polygon fill="#fb6969" stroke="black" points="1254.75,-857.25 1154,-857.25 1154,-838.75 1254.75,-838.75 1254.75,-857.25"/>
<text text-anchor="start" x="1162" y="-844.7" font-family="Helvetica,sans-Serif" font-size="9.00">loadGameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge57" class="edge">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.54,-857.71C1267.44,-863.76 1284.96,-873.49 1295,-889 1303.58,-902.26 1291.79,-2014.87 1303,-2026 1330.04,-2052.85 1437.89,-2036.59 1474.5,-2026 1499.3,-2018.83 1523.81,-2001.72 1539.56,-1989.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1540.52,-1991.01 1543.81,-1985.57 1537.85,-1987.77 1540.52,-1991.01"/>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts -->
<g id="node59" class="node">
<title>src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts</title>
<g id="a_node59"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts" xlink:title="loadGameSettingsRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1448.38,-857.25 1313.12,-857.25 1313.12,-838.75 1448.38,-838.75 1448.38,-857.25"/>
<text text-anchor="start" x="1321.12" y="-844.7" font-family="Helvetica,sans-Serif" font-size="9.00">loadGameSettingsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts -->
<g id="edge56" class="edge">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettingsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1254.88,-848C1270.23,-848 1287.5,-848 1304.2,-848"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1303.84,-850.1 1309.84,-848 1303.84,-845.9 1303.84,-850.1"/>
</g>
<!-- src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts -->
<g id="edge58" class="edge">
<title>src/client/application/commands/loadGameSettings/loadGameSettings.ts&#45;&gt;src/client/domain/GameSettings/gameSettingsEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1254.82,-847.05C1269.97,-844.15 1285.21,-838.07 1295,-826 1303.47,-815.55 1299.42,-596.97 1303,-584 1346.83,-425.32 1340.67,-343.57 1482.5,-260 1532.43,-230.58 1601.88,-239.2 1645.46,-249.24"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1644.82,-251.24 1651.15,-250.61 1645.81,-247.16 1644.82,-251.24"/>
</g>
<!-- src/client/domain/GameSettings/gameSettingsEvents.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts -->
<g id="edge123" class="edge">
<title>src/client/domain/GameSettings/gameSettingsEvents.ts&#45;&gt;src/client/domain/GameSettings/GameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1742.15,-264.03C1749.16,-264.42 1756.31,-264.83 1763.25,-265.22"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1762.75,-267.29 1768.86,-265.53 1762.99,-263.1 1762.75,-267.29"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts -->
<g id="node61" class="node">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts</title>
<g id="a_node61"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts" xlink:title="removeCardFromDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1263.38,-918.25 1145.38,-918.25 1145.38,-899.75 1263.38,-899.75 1263.38,-918.25"/>
<text text-anchor="start" x="1153.38" y="-905.7" font-family="Helvetica,sans-Serif" font-size="9.00">removeCardFromDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge60" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.54,-918.71C1267.44,-924.77 1284.96,-934.49 1295,-950 1303.23,-962.71 1292.26,-2029.33 1303,-2040 1316.52,-2053.42 1456.52,-2046.31 1474.5,-2040 1502.64,-2030.12 1528.29,-2006.13 1543.25,-1989.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1544.51,-1991.69 1546.96,-1985.82 1541.39,-1988.89 1544.51,-1991.69"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById.ts -->
<g id="edge59" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/queries/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1249.95,-918.62C1266.98,-924.67 1284.75,-934.42 1295,-950 1317.23,-983.8 1283.17,-1278.74 1303,-1314 1312.04,-1330.08 1328.78,-1341.68 1344.32,-1349.54"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1343.24,-1351.35 1349.56,-1352.02 1345.04,-1347.55 1343.24,-1351.35"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge62" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1263.84,-906.08C1275.88,-902.65 1287.23,-896.79 1295,-887 1306.81,-872.12 1289.77,-216.64 1303,-203 1348.57,-156.04 1549.98,-171.73 1641.2,-181.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1640.73,-183.63 1646.93,-182.2 1641.19,-179.46 1640.73,-183.63"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge63" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1263.84,-906.08C1275.88,-902.66 1287.23,-896.79 1295,-887 1307.38,-871.4 1289.25,-184.41 1303,-170 1316.18,-156.19 1455.54,-162.19 1474.5,-160 1544.29,-151.94 1560.28,-139.29 1630.25,-133 1687.47,-127.85 1702.05,-132.22 1759.5,-132 1807.61,-131.81 1822.64,-115.26 1867.75,-132 1887.99,-139.51 1906.08,-156.79 1917.57,-169.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1915.81,-170.97 1921.3,-174.19 1919.02,-168.25 1915.81,-170.97"/>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts -->
<g id="node62" class="node">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts</title>
<g id="a_node62"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts" xlink:title="removeCardFromDeckRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1457,-918.25 1304.5,-918.25 1304.5,-899.75 1457,-899.75 1457,-918.25"/>
<text text-anchor="start" x="1312.5" y="-905.7" font-family="Helvetica,sans-Serif" font-size="9.00">removeCardFromDeckRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts -->
<g id="edge61" class="edge">
<title>src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeckRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1263.44,-909C1273.66,-909 1284.48,-909 1295.24,-909"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1295.23,-911.1 1301.23,-909 1295.23,-906.9 1295.23,-911.1"/>
</g>
<!-- src/client/application/commands/saveDeckDraft/saveDeckDraft.ts -->
<g id="node63" class="node">
<title>src/client/application/commands/saveDeckDraft/saveDeckDraft.ts</title>
<g id="a_node63"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/saveDeckDraft/saveDeckDraft.ts" xlink:title="saveDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1246.88,-1284.25 1161.88,-1284.25 1161.88,-1265.75 1246.88,-1265.75 1246.88,-1284.25"/>
<text text-anchor="start" x="1169.88" y="-1271.7" font-family="Helvetica,sans-Serif" font-size="9.00">saveDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/saveDeckDraft/saveDeckDraft.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge64" class="edge">
<title>src/client/application/commands/saveDeckDraft/saveDeckDraft.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1247.09,-1270.11C1264.48,-1270.52 1283.31,-1274.62 1295,-1288 1310.28,-1305.49 1286.5,-2107.66 1303,-2124 1330.08,-2150.82 1441.7,-2143.41 1474.5,-2124 1523.37,-2095.08 1545.19,-2025.73 1553.1,-1992.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1555.13,-1992.98 1554.39,-1986.67 1551.03,-1992.06 1555.13,-1992.98"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts -->
<g id="node65" class="node">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts</title>
<g id="a_node65"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/showCardDetails/showCardDetails.ts" xlink:title="showCardDetails.ts">
<polygon fill="#fb6969" stroke="black" points="1252.12,-979.25 1156.62,-979.25 1156.62,-960.75 1252.12,-960.75 1252.12,-979.25"/>
<text text-anchor="start" x="1164.62" y="-966.7" font-family="Helvetica,sans-Serif" font-size="9.00">showCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge67" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.53,-979.71C1267.43,-985.77 1284.95,-995.49 1295,-1011 1310.75,-1035.32 1282.43,-2033.59 1303,-2054 1316.53,-2067.42 1456.84,-2061.16 1474.5,-2054 1505.84,-2041.29 1531.81,-2010.3 1545.77,-1990.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1547.42,-1992.14 1549.12,-1986.01 1543.97,-1989.74 1547.42,-1992.14"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/queries/getCatalogCardById.ts -->
<g id="edge69" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/queries/getCatalogCardById.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1249.92,-979.64C1266.94,-985.7 1284.72,-995.44 1295,-1011 1313.57,-1039.1 1286.43,-1284.68 1303,-1314 1312.08,-1330.06 1328.82,-1341.65 1344.36,-1349.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1343.27,-1351.33 1349.59,-1352 1345.07,-1347.54 1343.27,-1351.33"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge70" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1252.55,-969.53C1268.47,-966.83 1284.77,-960.75 1295,-948 1307.66,-932.22 1297.96,-603.59 1303,-584 1345.91,-417.38 1348.04,-348.36 1482.5,-241 1506.84,-221.56 1588.9,-204.11 1641.19,-194.59"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1641.43,-196.68 1646.97,-193.55 1640.69,-192.54 1641.43,-196.68"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge71" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1252.16,-969.67C1268.23,-967.01 1284.74,-960.93 1295,-948 1307.1,-932.75 1291.33,-262.59 1303,-247 1390.55,-130.11 1477.63,-193.81 1617.25,-151 1623.03,-149.23 1624.28,-147.92 1630.25,-147 1687.02,-138.24 1702.05,-146.22 1759.5,-146 1807.61,-145.81 1821.56,-132.53 1867.75,-146 1884.59,-150.91 1901.23,-161.87 1913.13,-171.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1911.54,-172.49 1917.53,-174.62 1914.17,-169.22 1911.54,-172.49"/>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetailsRequest.ts -->
<g id="node66" class="node">
<title>src/client/application/commands/showCardDetails/showCardDetailsRequest.ts</title>
<g id="a_node66"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/showCardDetails/showCardDetailsRequest.ts" xlink:title="showCardDetailsRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1445.75,-979.25 1315.75,-979.25 1315.75,-960.75 1445.75,-960.75 1445.75,-979.25"/>
<text text-anchor="start" x="1323.75" y="-966.7" font-family="Helvetica,sans-Serif" font-size="9.00">showCardDetailsRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetailsRequest.ts -->
<g id="edge68" class="edge">
<title>src/client/application/commands/showCardDetails/showCardDetails.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetailsRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1252.56,-970C1269.21,-970 1288.33,-970 1306.61,-970"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1306.47,-972.1 1312.47,-970 1306.47,-967.9 1306.47,-972.1"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts -->
<g id="node67" class="node">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts</title>
<g id="a_node67"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts" xlink:title="switchDeckBuilderView.ts">
<polygon fill="#fb6969" stroke="black" points="1264.5,-613.25 1144.25,-613.25 1144.25,-594.75 1264.5,-594.75 1264.5,-613.25"/>
<text text-anchor="start" x="1152.25" y="-600.7" font-family="Helvetica,sans-Serif" font-size="9.00">switchDeckBuilderView.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge72" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.55,-613.7C1267.45,-619.76 1284.97,-629.48 1295,-645 1304.99,-660.45 1290.05,-1956.92 1303,-1970 1317.73,-1984.88 1450.4,-1979.88 1517.86,-1976.31"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.63,-1978.42 1523.51,-1976 1517.41,-1974.23 1517.63,-1978.42"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts -->
<g id="edge74" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1264.73,-600.78C1276.42,-597.32 1287.41,-591.52 1295,-582 1311.53,-561.28 1284.1,-123.58 1303,-105 1352.79,-56.04 1553.93,-75.56 1617.25,-105 1645.76,-118.26 1666.74,-149.71 1677.71,-169.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1675.72,-170.3 1680.38,-174.62 1679.43,-168.34 1675.72,-170.3"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts -->
<g id="edge75" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1264.74,-600.79C1276.43,-597.33 1287.41,-591.52 1295,-582 1312.66,-559.84 1283,-92.08 1303,-72 1322.91,-52.01 1528.66,-67 1556.88,-67 1556.88,-67 1556.88,-67 1688.38,-67 1769.78,-67 1798.81,-60.73 1867.75,-104 1892.9,-119.78 1911.77,-149.47 1921.96,-168.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1920.03,-169.28 1924.64,-173.65 1923.76,-167.36 1920.03,-169.28"/>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts -->
<g id="node68" class="node">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts</title>
<g id="a_node68"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts" xlink:title="switchDeckBuilderViewRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1458.5,-613.25 1303,-613.25 1303,-594.75 1458.5,-594.75 1458.5,-613.25"/>
<text text-anchor="start" x="1311" y="-600.7" font-family="Helvetica,sans-Serif" font-size="9.00">switchDeckBuilderViewRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts -->
<g id="edge73" class="edge">
<title>src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderViewRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1264.9,-604C1274.19,-604 1283.95,-604 1293.69,-604"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1293.59,-606.1 1299.59,-604 1293.59,-601.9 1293.59,-606.1"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts -->
<g id="node69" class="node">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts</title>
<g id="a_node69"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts" xlink:title="updateAvailableFilters.ts">
<polygon fill="#fb6969" stroke="black" points="1262.62,-674.25 1146.12,-674.25 1146.12,-655.75 1262.62,-655.75 1262.62,-674.25"/>
<text text-anchor="start" x="1154.12" y="-661.7" font-family="Helvetica,sans-Serif" font-size="9.00">updateAvailableFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge76" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1250.54,-674.71C1267.45,-680.76 1284.97,-690.48 1295,-706 1304.64,-720.91 1290.41,-1971.49 1303,-1984 1330.03,-2010.86 1436.46,-1986.3 1474.5,-1984 1488.66,-1983.14 1504.1,-1981.47 1517.76,-1979.73"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.77,-1981.85 1523.44,-1978.98 1517.22,-1977.68 1517.77,-1981.85"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts -->
<g id="node70" class="node">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts</title>
<g id="a_node70"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts" xlink:title="updateAvailableFiltersRequest.ts">
<polygon fill="#fb6969" stroke="black" points="1456.25,-674.25 1305.25,-674.25 1305.25,-655.75 1456.25,-655.75 1456.25,-674.25"/>
<text text-anchor="start" x="1313.25" y="-661.7" font-family="Helvetica,sans-Serif" font-size="9.00">updateAvailableFiltersRequest.ts</text>
</a>
</g>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts -->
<g id="edge77" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1262.96,-665C1273.52,-665 1284.75,-665 1295.9,-665"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1295.79,-667.1 1301.79,-665 1295.79,-662.9 1295.79,-667.1"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts -->
<g id="edge78" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts&#45;&gt;src/client/domain/Catalog/catalogFilterEvents.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1262.88,-662.31C1275.26,-658.92 1287.01,-653.02 1295,-643 1311.5,-622.33 1284.3,-185.7 1303,-167 1373.48,-96.53 1680.14,-96.42 1751.5,-166 1767.34,-181.45 1749.34,-345.34 1759.5,-365 1765.21,-376.05 1775.4,-385.02 1785.34,-391.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1784.04,-393.38 1790.24,-394.78 1786.27,-389.82 1784.04,-393.38"/>
</g>
<!-- src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge79" class="edge">
<title>src/client/application/commands/updateAvailableFilters/updateAvailableFiltersRequest.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1430.38,-655.28C1444.74,-651.92 1460.39,-647.78 1474.5,-643 1643.8,-585.62 1836.09,-488.39 1904.59,-452.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1905.3,-454.68 1909.64,-450.04 1903.35,-450.96 1905.3,-454.68"/>
</g>
<!-- src/client/domain/Catalog/catalogFilterEvents.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge110" class="edge">
<title>src/client/domain/Catalog/catalogFilterEvents.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1848.76,-415.72C1863.61,-419.95 1880.98,-424.9 1895.87,-429.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1895.07,-431.09 1901.41,-430.72 1896.22,-427.05 1895.07,-431.09"/>
</g>
<!-- src/client/application/queries/applyFilterToCard.ts -->
<g id="node73" class="node">
<title>src/client/application/queries/applyFilterToCard.ts</title>
<g id="a_node73"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/applyFilterToCard.ts" xlink:title="applyFilterToCard.ts">
<polygon fill="#fb6969" stroke="black" points="1606.75,-1372.25 1509,-1372.25 1509,-1353.75 1606.75,-1353.75 1606.75,-1372.25"/>
<text text-anchor="start" x="1517" y="-1359.7" font-family="Helvetica,sans-Serif" font-size="9.00">applyFilterToCard.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/applyFilterToCard.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge81" class="edge">
<title>src/client/application/queries/applyFilterToCard.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1562.67,-1353.55C1598.42,-1264.69 1875.35,-576.13 1923.4,-456.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1925.27,-457.64 1925.56,-451.29 1921.37,-456.08 1925.27,-457.64"/>
</g>
<!-- src/client/application/queries/applyFilterToCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge80" class="edge">
<title>src/client/application/queries/applyFilterToCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1558.54,-1353.4C1556.4,-1277.86 1552.31,-782 1812.62,-782 1812.62,-782 1812.62,-782 1932.5,-782 2087.17,-782 2134.63,-528.35 2144.89,-457.3"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2146.95,-457.76 2145.69,-451.53 2142.79,-457.19 2146.95,-457.76"/>
</g>
<!-- src/client/application/queries/findDeckCardById.ts -->
<g id="node75" class="node">
<title>src/client/application/queries/findDeckCardById.ts</title>
<g id="a_node75"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/findDeckCardById.ts" xlink:title="findDeckCardById.ts">
<polygon fill="#fb6969" stroke="black" points="1430.38,-1868.25 1331.12,-1868.25 1331.12,-1849.75 1430.38,-1849.75 1430.38,-1868.25"/>
<text text-anchor="start" x="1339.12" y="-1855.7" font-family="Helvetica,sans-Serif" font-size="9.00">findDeckCardById.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/findDeckCardById.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge82" class="edge">
<title>src/client/application/queries/findDeckCardById.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1430.73,-1856.23C1446.82,-1857.95 1463.4,-1863.01 1474.5,-1875 1487.39,-1888.92 1469.72,-1945.98 1482.5,-1960 1491.35,-1969.7 1504.62,-1973.96 1517.52,-1975.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.16,-1977.63 1523.31,-1976.02 1517.5,-1973.44 1517.16,-1977.63"/>
</g>
<!-- src/client/application/queries/getActiveFilters.ts -->
<g id="node76" class="node">
<title>src/client/application/queries/getActiveFilters.ts</title>
<g id="a_node76"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getActiveFilters.ts" xlink:title="getActiveFilters.ts">
<polygon fill="#fb6969" stroke="black" points="1424.38,-1527.25 1337.12,-1527.25 1337.12,-1508.75 1424.38,-1508.75 1424.38,-1527.25"/>
<text text-anchor="start" x="1345.12" y="-1514.7" font-family="Helvetica,sans-Serif" font-size="9.00">getActiveFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getActiveFilters.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge83" class="edge">
<title>src/client/application/queries/getActiveFilters.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1424.5,-1514.26C1442.65,-1515.15 1462.37,-1519.87 1474.5,-1534 1489.92,-1551.96 1467.22,-1941.92 1482.5,-1960 1491.05,-1970.11 1504.38,-1974.43 1517.42,-1975.95"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.18,-1978.04 1523.31,-1976.38 1517.48,-1973.85 1517.18,-1978.04"/>
</g>
<!-- src/client/application/queries/getAvailableFilters.ts -->
<g id="node77" class="node">
<title>src/client/application/queries/getAvailableFilters.ts</title>
<g id="a_node77"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getAvailableFilters.ts" xlink:title="getAvailableFilters.ts">
<polygon fill="#fb6969" stroke="black" points="1431.12,-1837.25 1330.38,-1837.25 1330.38,-1818.75 1431.12,-1818.75 1431.12,-1837.25"/>
<text text-anchor="start" x="1338.38" y="-1824.7" font-family="Helvetica,sans-Serif" font-size="9.00">getAvailableFilters.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getAvailableFilters.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge84" class="edge">
<title>src/client/application/queries/getAvailableFilters.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1431.38,-1825.12C1447.33,-1826.88 1463.66,-1831.99 1474.5,-1844 1491.82,-1863.18 1465.33,-1940.69 1482.5,-1960 1491.23,-1969.81 1504.47,-1974.1 1517.38,-1975.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.04,-1977.75 1523.19,-1976.13 1517.37,-1973.57 1517.04,-1977.75"/>
</g>
<!-- src/client/application/queries/getCardDetails.ts -->
<g id="node78" class="node">
<title>src/client/application/queries/getCardDetails.ts</title>
<g id="a_node78"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getCardDetails.ts" xlink:title="getCardDetails.ts">
<polygon fill="#fb6969" stroke="black" points="1424,-1558.25 1337.5,-1558.25 1337.5,-1539.75 1424,-1539.75 1424,-1558.25"/>
<text text-anchor="start" x="1345.5" y="-1545.7" font-family="Helvetica,sans-Serif" font-size="9.00">getCardDetails.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCardDetails.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge85" class="edge">
<title>src/client/application/queries/getCardDetails.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1424.49,-1545.27C1442.64,-1546.16 1462.36,-1550.87 1474.5,-1565 1488.81,-1581.64 1468.32,-1943.25 1482.5,-1960 1491.05,-1970.1 1504.39,-1974.42 1517.43,-1975.95"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.19,-1978.03 1523.32,-1976.37 1517.49,-1973.84 1517.19,-1978.03"/>
</g>
<!-- src/client/application/queries/getCardsInDeck.ts -->
<g id="node79" class="node">
<title>src/client/application/queries/getCardsInDeck.ts</title>
<g id="a_node79"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getCardsInDeck.ts" xlink:title="getCardsInDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1426.25,-1589.25 1335.25,-1589.25 1335.25,-1570.75 1426.25,-1570.75 1426.25,-1589.25"/>
<text text-anchor="start" x="1343.25" y="-1576.7" font-family="Helvetica,sans-Serif" font-size="9.00">getCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCardsInDeck.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge86" class="edge">
<title>src/client/application/queries/getCardsInDeck.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1426.4,-1576.39C1444,-1577.51 1462.77,-1582.38 1474.5,-1596 1487.7,-1611.33 1469.42,-1944.57 1482.5,-1960 1491.06,-1970.1 1504.4,-1974.41 1517.44,-1975.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.19,-1978.03 1523.33,-1976.36 1517.5,-1973.84 1517.19,-1978.03"/>
</g>
<!-- src/client/application/queries/getCatalogCards.ts -->
<g id="node80" class="node">
<title>src/client/application/queries/getCatalogCards.ts</title>
<g id="a_node80"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getCatalogCards.ts" xlink:title="getCatalogCards.ts">
<polygon fill="#fb6969" stroke="black" points="1428.12,-1496.25 1333.38,-1496.25 1333.38,-1477.75 1428.12,-1477.75 1428.12,-1496.25"/>
<text text-anchor="start" x="1341.38" y="-1483.7" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogCards.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCatalogCards.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge88" class="edge">
<title>src/client/application/queries/getCatalogCards.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1428.35,-1483.5C1445.39,-1484.85 1463.23,-1489.85 1474.5,-1503 1491.03,-1522.28 1466.12,-1940.6 1482.5,-1960 1491.04,-1970.12 1504.38,-1974.44 1517.41,-1975.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.17,-1978.05 1523.31,-1976.38 1517.48,-1973.86 1517.17,-1978.05"/>
</g>
<!-- src/client/application/queries/getCatalogError.ts -->
<g id="node81" class="node">
<title>src/client/application/queries/getCatalogError.ts</title>
<g id="a_node81"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getCatalogError.ts" xlink:title="getCatalogError.ts">
<polygon fill="#fb6969" stroke="black" points="1425.5,-1930.25 1336,-1930.25 1336,-1911.75 1425.5,-1911.75 1425.5,-1930.25"/>
<text text-anchor="start" x="1344" y="-1917.7" font-family="Helvetica,sans-Serif" font-size="9.00">getCatalogError.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getCatalogError.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge89" class="edge">
<title>src/client/application/queries/getCatalogError.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1425.98,-1917.62C1442.88,-1918.59 1461.26,-1922.72 1474.5,-1934 1483.7,-1941.84 1473.48,-1951.94 1482.5,-1960 1491.96,-1968.45 1504.87,-1972.52 1517.27,-1974.31"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1516.96,-1976.39 1523.15,-1974.92 1517.4,-1972.21 1516.96,-1976.39"/>
</g>
<!-- src/client/application/queries/getDeckBuilderView.ts -->
<g id="node82" class="node">
<title>src/client/application/queries/getDeckBuilderView.ts</title>
<g id="a_node82"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getDeckBuilderView.ts" xlink:title="getDeckBuilderView.ts">
<polygon fill="#fb6969" stroke="black" points="1434.5,-1620.25 1327,-1620.25 1327,-1601.75 1434.5,-1601.75 1434.5,-1620.25"/>
<text text-anchor="start" x="1335" y="-1607.7" font-family="Helvetica,sans-Serif" font-size="9.00">getDeckBuilderView.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getDeckBuilderView.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge90" class="edge">
<title>src/client/application/queries/getDeckBuilderView.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1434.6,-1608.21C1449.62,-1610.24 1464.57,-1615.5 1474.5,-1627 1486.59,-1641.01 1470.52,-1945.9 1482.5,-1960 1491.07,-1970.09 1504.42,-1974.4 1517.45,-1975.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.2,-1978.02 1523.34,-1976.36 1517.51,-1973.83 1517.2,-1978.02"/>
</g>
<!-- src/client/application/queries/getDeckName.ts -->
<g id="node83" class="node">
<title>src/client/application/queries/getDeckName.ts</title>
<g id="a_node83"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getDeckName.ts" xlink:title="getDeckName.ts">
<polygon fill="#fb6969" stroke="black" points="1422.5,-1682.25 1339,-1682.25 1339,-1663.75 1422.5,-1663.75 1422.5,-1682.25"/>
<text text-anchor="start" x="1347" y="-1669.7" font-family="Helvetica,sans-Serif" font-size="9.00">getDeckName.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getDeckName.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge91" class="edge">
<title>src/client/application/queries/getDeckName.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1422.53,-1669.24C1441.17,-1669.9 1461.86,-1674.44 1474.5,-1689 1494.24,-1711.75 1462.93,-1937.1 1482.5,-1960 1491.1,-1970.06 1504.45,-1974.37 1517.48,-1975.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.23,-1977.99 1523.37,-1976.33 1517.54,-1973.8 1517.23,-1977.99"/>
</g>
<!-- src/client/application/queries/getFilteredCards.ts -->
<g id="node84" class="node">
<title>src/client/application/queries/getFilteredCards.ts</title>
<g id="a_node84"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getFilteredCards.ts" xlink:title="getFilteredCards.ts">
<polygon fill="#fb6969" stroke="black" points="1427.38,-1465.25 1334.12,-1465.25 1334.12,-1446.75 1427.38,-1446.75 1427.38,-1465.25"/>
<text text-anchor="start" x="1342.12" y="-1452.7" font-family="Helvetica,sans-Serif" font-size="9.00">getFilteredCards.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getFilteredCards.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge93" class="edge">
<title>src/client/application/queries/getFilteredCards.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1427.4,-1452.42C1444.72,-1453.65 1463.02,-1458.6 1474.5,-1472 1492.14,-1492.59 1465.02,-1939.27 1482.5,-1960 1491.03,-1970.12 1504.37,-1974.44 1517.41,-1975.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.17,-1978.05 1523.3,-1976.39 1517.47,-1973.86 1517.17,-1978.05"/>
</g>
<!-- src/client/application/queries/getFilteredCards.ts&#45;&gt;src/client/application/queries/applyFilterToCard.ts -->
<g id="edge92" class="edge">
<title>src/client/application/queries/getFilteredCards.ts&#45;&gt;src/client/application/queries/applyFilterToCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1427.65,-1458.86C1444.58,-1457.43 1462.54,-1452.53 1474.5,-1440 1494.29,-1419.26 1462.73,-1396.76 1482.5,-1376 1487.39,-1370.86 1493.55,-1367.31 1500.2,-1364.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1500.74,-1366.95 1505.94,-1363.29 1499.59,-1362.91 1500.74,-1366.95"/>
</g>
<!-- src/client/application/queries/getGameSettings.ts -->
<g id="node85" class="node">
<title>src/client/application/queries/getGameSettings.ts</title>
<g id="a_node85"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getGameSettings.ts" xlink:title="getGameSettings.ts">
<polygon fill="#fb6969" stroke="black" points="1428.5,-1775.25 1333,-1775.25 1333,-1756.75 1428.5,-1756.75 1428.5,-1775.25"/>
<text text-anchor="start" x="1341" y="-1762.7" font-family="Helvetica,sans-Serif" font-size="9.00">getGameSettings.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getGameSettings.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge94" class="edge">
<title>src/client/application/queries/getGameSettings.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1428.65,-1762.7C1445.51,-1764.12 1463.16,-1769.13 1474.5,-1782 1487.59,-1796.85 1469.52,-1945.05 1482.5,-1960 1491.18,-1970 1504.54,-1974.29 1517.57,-1975.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.31,-1977.92 1523.44,-1976.26 1517.62,-1973.73 1517.31,-1977.92"/>
</g>
<!-- src/client/application/queries/getGameSettingsError.ts -->
<g id="node86" class="node">
<title>src/client/application/queries/getGameSettingsError.ts</title>
<g id="a_node86"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getGameSettingsError.ts" xlink:title="getGameSettingsError.ts">
<polygon fill="#fb6969" stroke="black" points="1438.25,-1806.25 1323.25,-1806.25 1323.25,-1787.75 1438.25,-1787.75 1438.25,-1806.25"/>
<text text-anchor="start" x="1331.25" y="-1793.7" font-family="Helvetica,sans-Serif" font-size="9.00">getGameSettingsError.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getGameSettingsError.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge95" class="edge">
<title>src/client/application/queries/getGameSettingsError.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1438.74,-1795.08C1452.25,-1797.5 1465.38,-1802.75 1474.5,-1813 1496.25,-1837.44 1460.94,-1935.4 1482.5,-1960 1491.15,-1969.88 1504.39,-1974.17 1517.3,-1975.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1516.97,-1977.83 1523.12,-1976.2 1517.3,-1973.64 1516.97,-1977.83"/>
</g>
<!-- src/client/application/queries/getMaxCardsInDeck.ts -->
<g id="node87" class="node">
<title>src/client/application/queries/getMaxCardsInDeck.ts</title>
<g id="a_node87"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getMaxCardsInDeck.ts" xlink:title="getMaxCardsInDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1434.88,-1651.25 1326.62,-1651.25 1326.62,-1632.75 1434.88,-1632.75 1434.88,-1651.25"/>
<text text-anchor="start" x="1334.62" y="-1638.7" font-family="Helvetica,sans-Serif" font-size="9.00">getMaxCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getMaxCardsInDeck.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge96" class="edge">
<title>src/client/application/queries/getMaxCardsInDeck.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1435.06,-1639.29C1449.92,-1641.37 1464.66,-1646.63 1474.5,-1658 1496.46,-1683.39 1460.73,-1934.45 1482.5,-1960 1491.08,-1970.08 1504.43,-1974.39 1517.46,-1975.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.22,-1978 1523.35,-1976.34 1517.52,-1973.81 1517.22,-1978"/>
</g>
<!-- src/client/application/queries/getSearchTerm.ts -->
<g id="node88" class="node">
<title>src/client/application/queries/getSearchTerm.ts</title>
<g id="a_node88"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getSearchTerm.ts" xlink:title="getSearchTerm.ts">
<polygon fill="#fb6969" stroke="black" points="1424.38,-1713.25 1337.12,-1713.25 1337.12,-1694.75 1424.38,-1694.75 1424.38,-1713.25"/>
<text text-anchor="start" x="1345.12" y="-1700.7" font-family="Helvetica,sans-Serif" font-size="9.00">getSearchTerm.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getSearchTerm.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge97" class="edge">
<title>src/client/application/queries/getSearchTerm.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1424.41,-1700.34C1442.53,-1701.25 1462.26,-1705.95 1474.5,-1720 1492.03,-1740.12 1465.13,-1939.75 1482.5,-1960 1491.12,-1970.05 1504.47,-1974.35 1517.5,-1975.88"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.25,-1977.97 1523.39,-1976.31 1517.56,-1973.78 1517.25,-1977.97"/>
</g>
<!-- src/client/application/queries/getTotalCardsInDeck.ts -->
<g id="node89" class="node">
<title>src/client/application/queries/getTotalCardsInDeck.ts</title>
<g id="a_node89"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/getTotalCardsInDeck.ts" xlink:title="getTotalCardsInDeck.ts">
<polygon fill="#fb6969" stroke="black" points="1436,-1744.25 1325.5,-1744.25 1325.5,-1725.75 1436,-1725.75 1436,-1744.25"/>
<text text-anchor="start" x="1333.5" y="-1731.7" font-family="Helvetica,sans-Serif" font-size="9.00">getTotalCardsInDeck.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/getTotalCardsInDeck.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge98" class="edge">
<title>src/client/application/queries/getTotalCardsInDeck.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1436.43,-1732.57C1450.79,-1734.79 1464.92,-1740.06 1474.5,-1751 1489.81,-1768.48 1467.33,-1942.4 1482.5,-1960 1491.14,-1970.03 1504.5,-1974.33 1517.53,-1975.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.27,-1977.95 1523.41,-1976.29 1517.58,-1973.76 1517.27,-1977.95"/>
</g>
<!-- src/client/application/queries/hasDeckDraft.ts&#45;&gt;src/client/application/services/DeckDraftService.ts -->
<g id="edge99" class="edge">
<title>src/client/application/queries/hasDeckDraft.ts&#45;&gt;src/client/application/services/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1421.31,-1393.19C1439.01,-1394.64 1459.27,-1398.99 1474.5,-1410 1583.86,-1489.08 1571.96,-1550.87 1617.25,-1678 1661.15,-1801.23 1679.66,-1959.35 1684.8,-2010.92"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="1682.69,-2010.98 1685.36,-2016.75 1686.87,-2010.58 1682.69,-2010.98"/>
</g>
<!-- src/client/application/queries/isCatalogLoading.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge100" class="edge">
<title>src/client/application/queries/isCatalogLoading.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1429.81,-1421.61C1446.41,-1423.12 1463.56,-1428.21 1474.5,-1441 1493.25,-1462.91 1463.92,-1937.95 1482.5,-1960 1491.03,-1970.12 1504.36,-1974.45 1517.4,-1975.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.16,-1978.06 1523.3,-1976.39 1517.46,-1973.87 1517.16,-1978.06"/>
</g>
<!-- src/client/application/queries/isGameSettingsLoading.ts -->
<g id="node92" class="node">
<title>src/client/application/queries/isGameSettingsLoading.ts</title>
<g id="a_node92"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/queries/isGameSettingsLoading.ts" xlink:title="isGameSettingsLoading.ts">
<polygon fill="#fb6969" stroke="black" points="1442.38,-1899.25 1319.12,-1899.25 1319.12,-1880.75 1442.38,-1880.75 1442.38,-1899.25"/>
<text text-anchor="start" x="1327.12" y="-1886.7" font-family="Helvetica,sans-Serif" font-size="9.00">isGameSettingsLoading.ts</text>
</a>
</g>
</g>
<!-- src/client/application/queries/isGameSettingsLoading.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge101" class="edge">
<title>src/client/application/queries/isGameSettingsLoading.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1442.44,-1889.54C1454.43,-1892.23 1465.94,-1897.23 1474.5,-1906 1491.45,-1923.36 1465.68,-1942.51 1482.5,-1960 1491.53,-1969.39 1504.72,-1973.6 1517.49,-1975.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1517.03,-1977.31 1523.19,-1975.74 1517.4,-1973.13 1517.03,-1977.31"/>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts -->
<g id="node93" class="node">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts</title>
<g id="a_node93"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/application/subscribers/subscribeToDeckDraft.ts" xlink:title="subscribeToDeckDraft.ts">
<polygon fill="#fb6969" stroke="black" points="1054.75,-1703.25 939.75,-1703.25 939.75,-1684.75 1054.75,-1684.75 1054.75,-1703.25"/>
<text text-anchor="start" x="947.75" y="-1690.7" font-family="Helvetica,sans-Serif" font-size="9.00">subscribeToDeckDraft.ts</text>
</a>
</g>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge102" class="edge">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1055.19,-1692.36C1069.37,-1694.33 1083.8,-1698.62 1095.38,-1707 1267.23,-1831.52 1123.25,-2024.18 1303,-2137 1335.28,-2157.26 1442.04,-2156.97 1474.5,-2137 1526.84,-2104.81 1547.14,-2027.96 1553.92,-1992.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1555.97,-1992.92 1554.96,-1986.65 1551.84,-1992.19 1555.97,-1992.92"/>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts -->
<g id="edge104" class="edge">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/domain/DeckBuilder/deckBuilderReducer.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M998.05,-1684.44C996.5,-1601.46 989.57,-994.79 1105.75,-523 1166.16,-277.71 1127.13,-25 1379.75,-25 1379.75,-25 1379.75,-25 1558.88,-25 1610.06,-25 1732.93,-102 1751.5,-118 1773.43,-136.9 1792.22,-164.98 1802.97,-182.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1800.96,-183.6 1805.8,-187.72 1804.58,-181.48 1800.96,-183.6"/>
</g>
<!-- src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/commands/saveDeckDraft/saveDeckDraft.ts -->
<g id="edge103" class="edge">
<title>src/client/application/subscribers/subscribeToDeckDraft.ts&#45;&gt;src/client/application/commands/saveDeckDraft/saveDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M999.36,-1684.37C1006.04,-1628.21 1042.69,-1343.55 1105.75,-1288 1118.53,-1276.74 1136.15,-1272.27 1152.91,-1271.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1152.64,-1273.14 1158.54,-1270.77 1152.45,-1268.94 1152.64,-1273.14"/>
</g>
<!-- src/client/domain/DeckBuilder/DeckBuilderCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge119" class="edge">
<title>src/client/domain/DeckBuilder/DeckBuilderCard.ts&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M2048.18,-209.67C2063.84,-245.93 2119.26,-374.21 2139.65,-421.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2137.63,-422.02 2141.94,-426.7 2141.48,-420.36 2137.63,-422.02"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx -->
<g id="node97" class="node">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx</title>
<g id="a_node97"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx" xlink:title="SignInButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="561.25,-3131.25 479.25,-3131.25 479.25,-3112.75 561.25,-3112.75 561.25,-3131.25"/>
<text text-anchor="start" x="487.25" y="-3118.7" font-family="Helvetica,sans-Serif" font-size="9.00">SignInButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx -->
<g id="node98" class="node">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx</title>
<g id="a_node98"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx" xlink:title="useSignInForm.tsx">
<polygon fill="#6cbaff" stroke="black" points="798,-3162.25 706.25,-3162.25 706.25,-3143.75 798,-3143.75 798,-3162.25"/>
<text text-anchor="start" x="714.25" y="-3149.7" font-family="Helvetica,sans-Serif" font-size="9.00">useSignInForm.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx -->
<g id="edge126" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M561.55,-3127.44C599.22,-3132.52 655.73,-3140.14 697.26,-3145.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="696.91,-3147.81 703.14,-3146.53 697.48,-3143.65 696.91,-3147.81"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx -->
<g id="node99" class="node">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx</title>
<g id="a_node99"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx" xlink:title="ShiningButton.tsx">
<polygon fill="#6cbaff" stroke="black" points="1040.88,-3075.25 953.62,-3075.25 953.62,-3056.75 1040.88,-3056.75 1040.88,-3075.25"/>
<text text-anchor="start" x="961.62" y="-3062.7" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningButton.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx -->
<g id="edge127" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M541.78,-3112.44C566.86,-3101.3 610.88,-3083.47 650.75,-3076 753.49,-3056.76 876.36,-3059.07 944.41,-3062.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="944.12,-3064.6 950.22,-3062.82 944.34,-3060.41 944.12,-3064.6"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge130" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.3,-3162.34C781.83,-3197.04 859.64,-3322.56 885.5,-3439 890.1,-3459.69 879.7,-4947.15 891.12,-4965 905.46,-4987.41 933,-4999.74 956.3,-5006.43"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="955.72,-5008.44 962.05,-5007.95 956.79,-5004.38 955.72,-5008.44"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge131" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M759.3,-3162.34C781.82,-3197.04 859.62,-3322.57 885.5,-3439 887.62,-3448.52 884.96,-4835.44 891.12,-4843 905.99,-4861.22 931.72,-4866.83 954.01,-4867.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.92,-4869.93 959.96,-4867.94 954,-4865.73 953.92,-4869.93"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css -->
<g id="node129" class="node">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css</title>
<g id="a_node129"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css" xlink:title="ShiningButton.css">
<polygon fill="#6cbaff" stroke="black" points="1249.12,-3075.25 1159.62,-3075.25 1159.62,-3056.75 1249.12,-3056.75 1249.12,-3075.25"/>
<text text-anchor="start" x="1167.62" y="-3062.7" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningButton.css</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css -->
<g id="edge185" class="edge">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningButton/ShiningButton.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1041.25,-3066C1073.06,-3066 1116.58,-3066 1150.52,-3066"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1150.34,-3068.1 1156.34,-3066 1150.34,-3063.9 1150.34,-3068.1"/>
</g>
<!-- src/client/infrastructure/lib/utils.ts -->
<g id="node130" class="node">
<title>src/client/infrastructure/lib/utils.ts</title>
<g id="a_node130"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/lib/utils.ts" xlink:title="utils.ts">
<polygon fill="#6cbaff" stroke="black" points="1231.38,-4290.25 1177.38,-4290.25 1177.38,-4271.75 1231.38,-4271.75 1231.38,-4290.25"/>
<text text-anchor="start" x="1191.62" y="-4277.7" font-family="Helvetica,sans-Serif" font-size="9.00">utils.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts -->
<g id="edge186" class="edge">
<title>src/client/infrastructure/components/ui/ShiningButton/ShiningButton.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1040.88,-3073.55C1060.63,-3079.33 1082.52,-3089.42 1095.38,-3107 1113.26,-3131.45 1190.47,-4115.24 1201.95,-4262.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1199.84,-4262.58 1202.4,-4268.4 1204.03,-4262.26 1199.84,-4262.58"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx -->
<g id="edge128" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/app/Auth/SignIn/SignInButton/SignInButton.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M713.54,-3122C674.5,-3122 613.26,-3122 570.4,-3122"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="570.58,-3119.9 564.58,-3122 570.58,-3124.1 570.58,-3119.9"/>
</g>
<!-- src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge129" class="edge">
<title>src/client/infrastructure/components/app/Auth/SignIn/SignInForm/SignInForm.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M790.85,-3122.78C832.99,-3123.64 901.33,-3125.05 947.5,-3126"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="947.35,-3128.09 953.39,-3126.12 947.44,-3123.9 947.35,-3128.09"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts -->
<g id="edge188" class="edge">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1038.36,-3133.88C1058.72,-3139.52 1081.93,-3149.72 1095.38,-3168 1163.11,-3260.12 1197.64,-4125.33 1202.72,-4262.67"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1200.6,-4262.34 1202.92,-4268.26 1204.8,-4262.18 1200.6,-4262.34"/>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css -->
<g id="node131" class="node">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css</title>
<g id="a_node131"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css" xlink:title="ShiningCard.css">
<polygon fill="#6cbaff" stroke="black" points="1246.12,-3136.25 1162.62,-3136.25 1162.62,-3117.75 1246.12,-3117.75 1246.12,-3136.25"/>
<text text-anchor="start" x="1170.62" y="-3123.7" font-family="Helvetica,sans-Serif" font-size="9.00">ShiningCard.css</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css -->
<g id="edge187" class="edge">
<title>src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1038.29,-3127C1071.28,-3127 1118.24,-3127 1153.65,-3127"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1153.36,-3129.1 1159.36,-3127 1153.36,-3124.9 1153.36,-3129.1"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge132" class="edge">
<title>src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M549.83,-3930.65C578.05,-3941.82 619.82,-3963.15 640,-3997 656.55,-4024.75 628.27,-4118.79 650.75,-4142 687.25,-4179.69 850.29,-4128.11 885.5,-4167 900.38,-4183.43 879.15,-4946.35 891.12,-4965 905.5,-4987.39 933.04,-4999.72 956.32,-5006.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="955.74,-5008.43 962.08,-5007.93 956.81,-5004.37 955.74,-5008.43"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge133" class="edge">
<title>src/client/infrastructure/components/app/Catalog/GameDetailsButton/GameDetailsButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M549.75,-3930.7C577.89,-3941.91 619.6,-3963.28 640,-3997 654.31,-4020.67 631.76,-4100.89 650.75,-4121 723.13,-4197.65 816.18,-4075.57 885.5,-4155 898.07,-4169.4 879.01,-4828.22 891.12,-4843 906.03,-4861.19 931.76,-4866.8 954.04,-4867.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.95,-4869.9 959.99,-4867.92 954.03,-4865.7 953.95,-4869.9"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge134" class="edge">
<title>src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M569.85,-3864.1C594.93,-3868.92 623.62,-3879.2 640,-3901 654.23,-3919.94 634.47,-4096.78 650.75,-4114 686.8,-4152.11 850.29,-4100.11 885.5,-4139 893.2,-4147.5 884.93,-4955.35 891.12,-4965 905.5,-4987.39 933.04,-4999.72 956.32,-5006.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="955.74,-5008.43 962.08,-5007.93 956.81,-5004.37 955.74,-5008.43"/>
</g>
<!-- src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge135" class="edge">
<title>src/client/infrastructure/components/app/Catalog/PlayGameButton/PlayGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M569.8,-3864.13C594.87,-3868.96 623.56,-3879.25 640,-3901 665.76,-3935.09 621.89,-4061.48 650.75,-4093 721.95,-4170.75 816.19,-4047.56 885.5,-4127 898.58,-4141.99 878.52,-4827.61 891.12,-4843 906.03,-4861.19 931.76,-4866.8 954.04,-4867.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.94,-4869.91 959.98,-4867.92 954.03,-4865.71 953.94,-4869.91"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge162" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M756.56,-3773.45C774.58,-3823.61 858.46,-4064.13 885.5,-4269 887.18,-4281.71 882.52,-5182.5 891.12,-5192 984.79,-5295.45 1063.82,-5225 1203.38,-5225 1203.38,-5225 1203.38,-5225 1932.5,-5225 2060.81,-5225 2141.33,-776.17 2146.94,-457.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2149.03,-457.51 2147.03,-451.48 2144.83,-457.44 2149.03,-457.51"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge163" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M804.77,-3769.99C833.01,-3770.15 866.11,-3764.65 885.5,-3742 895.87,-3729.89 882.06,-3181.12 891.12,-3168 903.99,-3149.37 926.67,-3139.2 947.67,-3133.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="947.94,-3135.75 953.28,-3132.32 946.96,-3131.67 947.94,-3135.75"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge179" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M819.84,-3707.5C844.49,-3712.79 870.14,-3723.34 885.5,-3744 896.79,-3759.18 878.54,-4410.88 891.12,-4425 904.01,-4439.46 924.09,-4444.22 943.26,-4444.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="943.24,-4446.92 949.24,-4444.81 943.24,-4442.72 943.24,-4446.92"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts -->
<g id="edge180" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameId/useGameId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M819.87,-3707.49C844.52,-3712.77 870.16,-3723.33 885.5,-3744 900.66,-3764.44 875.02,-4640.3 891.12,-4660 905.54,-4677.64 930.14,-4683.45 951.92,-4684.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="951.64,-4686.8 957.7,-4684.89 951.77,-4682.6 951.64,-4686.8"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx -->
<g id="node122" class="node">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx</title>
<g id="a_node122"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx" xlink:title="useGameSettingsByGameId.tsx">
<polygon fill="#6cbaff" stroke="black" points="1069.75,-4630.25 924.75,-4630.25 924.75,-4611.75 1069.75,-4611.75 1069.75,-4630.25"/>
<text text-anchor="start" x="932.75" y="-4617.7" font-family="Helvetica,sans-Serif" font-size="9.00">useGameSettingsByGameId.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx -->
<g id="edge181" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter.tsx&#45;&gt;src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M819.86,-3707.49C844.51,-3712.77 870.16,-3723.33 885.5,-3744 899.66,-3763.07 876.09,-4580.62 891.12,-4599 897.65,-4606.98 906.27,-4612.54 915.76,-4616.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="915.08,-4618.34 921.43,-4618.29 916.43,-4614.37 915.08,-4618.34"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/appStore.ts -->
<g id="edge190" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1053.74,-4925.9C1069.57,-4922.94 1085.26,-4916.61 1095.38,-4904 1114.19,-4880.56 1085.3,-2762.03 1105.75,-2740 1134.45,-2709.08 1255.36,-2739.44 1295,-2725 1387.07,-2691.47 1424.01,-2680.97 1474.5,-2597 1538.18,-2491.11 1553.66,-2084.87 1556.38,-1992.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1558.48,-1992.75 1556.55,-1986.7 1554.28,-1992.64 1558.48,-1992.75"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCards.ts -->
<g id="edge191" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/commands/loadCatalogCards/loadCatalogCards.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1053.5,-4925.96C1069.42,-4923.02 1085.22,-4916.68 1095.38,-4904 1113.07,-4881.91 1090.05,-851.55 1105.75,-828 1115.08,-814 1130.22,-804.77 1145.83,-798.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1146.2,-800.78 1151.15,-796.8 1144.79,-796.82 1146.2,-800.78"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getCatalogError.ts -->
<g id="edge192" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getCatalogError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1053.74,-4925.9C1069.57,-4922.94 1085.26,-4916.61 1095.38,-4904 1114.27,-4880.46 1085.51,-2753.39 1105.75,-2731 1134.11,-2699.63 1265.07,-2740.88 1295,-2711 1351.56,-2654.53 1375.17,-2052.85 1379.14,-1939.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1381.23,-1939.58 1379.34,-1933.51 1377.03,-1939.43 1381.23,-1939.58"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getFilteredCards.ts -->
<g id="edge193" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/getFilteredCards.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1053.74,-4925.9C1069.57,-4922.94 1085.26,-4916.61 1095.38,-4904 1114.43,-4880.26 1085.04,-2734.31 1105.75,-2712 1134.45,-2681.08 1266.27,-2727.89 1295,-2697 1306.59,-2684.54 1292.22,-1485.17 1303,-1472 1308.69,-1465.05 1316.55,-1460.62 1325.08,-1457.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1325.55,-1459.92 1330.84,-1456.4 1324.51,-1455.85 1325.55,-1459.92"/>
</g>
<!-- src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/isCatalogLoading.ts -->
<g id="edge194" class="edge">
<title>src/client/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId.tsx&#45;&gt;src/client/application/queries/isCatalogLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1053.74,-4925.9C1069.57,-4922.94 1085.26,-4916.61 1095.38,-4904 1114.55,-4880.11 1096.54,-2727.22 1105.75,-2698 1150.15,-2557.2 1250.36,-2565.73 1295,-2425 1303.26,-2398.95 1285.67,-1462.14 1303,-1441 1308.2,-1434.66 1315.19,-1430.42 1322.86,-1427.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1323.3,-1429.7 1328.48,-1426.01 1322.13,-1425.66 1323.3,-1429.7"/>
</g>
<!-- src/client/infrastructure/hooks/useDebounce/useDebounce.ts -->
<g id="node134" class="node">
<title>src/client/infrastructure/hooks/useDebounce/useDebounce.ts</title>
<g id="a_node134"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/hooks/useDebounce/useDebounce.ts" xlink:title="useDebounce.ts">
<polygon fill="#6cbaff" stroke="black" points="1245.75,-4508.25 1163,-4508.25 1163,-4489.75 1245.75,-4489.75 1245.75,-4508.25"/>
<text text-anchor="start" x="1171" y="-4495.7" font-family="Helvetica,sans-Serif" font-size="9.00">useDebounce.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/hooks/useDebounce/index.ts&#45;&gt;src/client/infrastructure/hooks/useDebounce/useDebounce.ts -->
<g id="edge195" class="edge">
<title>src/client/infrastructure/hooks/useDebounce/index.ts&#45;&gt;src/client/infrastructure/hooks/useDebounce/useDebounce.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1024.65,-4499C1057.62,-4499 1114.65,-4499 1155.42,-4499"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1161.58,-4501.1 1155.58,-4499 1161.58,-4496.9 1161.58,-4501.1"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge196" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.42,-4443.88C1061.54,-4443.8 1082.45,-4439.69 1095.38,-4425 1111.7,-4406.45 1091.52,-2666.2 1105.75,-2646 1157.29,-2572.8 1225.25,-2633.13 1295,-2577 1411.04,-2483.62 1415.32,-2430.68 1474.5,-2294 1521.83,-2184.68 1546.58,-2042.16 1554.24,-1992.23"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1556.28,-1992.75 1555.09,-1986.51 1552.13,-1992.13 1556.28,-1992.75"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeck.ts -->
<g id="edge197" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/addCardToDeck/addCardToDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.44,-4443.9C1061.56,-4443.82 1082.47,-4439.7 1095.38,-4425 1112.13,-4405.91 1091.66,-788.14 1105.75,-767 1115.87,-751.82 1132.82,-742.25 1149.78,-736.21"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1150.14,-738.31 1155.2,-734.46 1148.84,-734.31 1150.14,-738.31"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts -->
<g id="edge198" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/filterCatalog/filterCatalog.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.43,-4443.88C1061.54,-4443.81 1082.45,-4439.69 1095.38,-4425 1104.64,-4414.47 1104.51,-2419.97 1105.75,-2406 1149.22,-1915.35 1219.84,-1800.8 1295,-1314 1297.02,-1300.92 1295.45,-1265.86 1303,-1255 1311.64,-1242.57 1325.28,-1233.59 1338.67,-1227.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1339.5,-1229.19 1344.16,-1224.87 1337.82,-1225.34 1339.5,-1229.19"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/hideCardDetails/hideCardDetails.ts -->
<g id="edge199" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/hideCardDetails/hideCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.44,-4443.9C1061.56,-4443.82 1082.47,-4439.71 1095.38,-4425 1112.97,-4404.95 1090.96,-606.19 1105.75,-584 1115.87,-568.82 1132.82,-559.24 1149.78,-553.21"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1150.13,-555.31 1155.2,-551.46 1148.84,-551.31 1150.13,-555.31"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/search/search.ts -->
<g id="edge201" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/search/search.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.43,-4443.88C1061.54,-4443.8 1082.45,-4439.69 1095.38,-4425 1113.61,-4404.27 1087.87,-2458.04 1105.75,-2437 1160.98,-2371.99 1239.69,-2469.95 1295,-2405 1314.13,-2382.54 1290.79,-1369.86 1303,-1343 1313.57,-1319.74 1336.41,-1301.19 1354.44,-1289.38"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1355.3,-1291.32 1359.26,-1286.35 1353.06,-1287.77 1355.3,-1291.32"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts -->
<g id="edge200" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/removeCardFromDeck/removeCardFromDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.44,-4443.89C1061.56,-4443.82 1082.47,-4439.7 1095.38,-4425 1111.29,-4406.86 1092.36,-970.08 1105.75,-950 1114.53,-936.84 1128.44,-927.89 1143.03,-921.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.49,-923.89 1148.36,-919.8 1142,-919.96 1143.49,-923.89"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetails.ts -->
<g id="edge202" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/showCardDetails/showCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.44,-4443.89C1061.56,-4443.82 1082.47,-4439.7 1095.38,-4425 1111.01,-4407.18 1092.6,-1030.73 1105.75,-1011 1115.48,-996.41 1131.51,-987 1147.79,-980.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1148.42,-982.94 1153.43,-979.03 1147.08,-978.97 1148.42,-982.94"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts -->
<g id="edge203" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/switchDeckBuilderView/switchDeckBuilderView.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.44,-4443.9C1061.56,-4443.82 1082.47,-4439.71 1095.38,-4425 1112.69,-4405.27 1091.19,-666.84 1105.75,-645 1114.53,-631.84 1128.43,-622.89 1143.03,-616.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.49,-618.89 1148.35,-614.8 1142,-614.96 1143.49,-618.89"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts -->
<g id="edge204" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/commands/updateAvailableFilters/updateAvailableFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.44,-4443.9C1061.56,-4443.82 1082.47,-4439.71 1095.38,-4425 1112.41,-4405.59 1091.42,-727.49 1105.75,-706 1114.53,-692.84 1128.43,-683.89 1143.03,-677.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.49,-679.89 1148.36,-675.8 1142,-675.96 1143.49,-679.89"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge212" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.46,-4445.44C1062.08,-4451.16 1083.39,-4461.25 1095.38,-4479 1115.36,-4508.61 1082.5,-5094.87 1105.75,-5122 1145.95,-5168.91 1317.97,-5169 1379.75,-5169 1379.75,-5169 1379.75,-5169 1688.38,-5169 2031.25,-5169 1823.8,-4945.82 1867.75,-4483 1908.44,-4054.46 1928.75,-736.33 1930.39,-457.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1932.49,-457.74 1930.43,-451.73 1928.29,-457.72 1932.49,-457.74"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getActiveFilters.ts -->
<g id="edge205" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getActiveFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.42,-4443.88C1061.54,-4443.8 1082.45,-4439.69 1095.38,-4425 1113.4,-4404.51 1087.65,-2480.43 1105.75,-2460 1161.97,-2396.54 1238.66,-2499.36 1295,-2436 1311.65,-2417.27 1287.11,-1553.37 1303,-1534 1309.41,-1526.19 1318.56,-1521.56 1328.32,-1518.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1328.6,-1521.02 1334.03,-1517.72 1327.73,-1516.91 1328.6,-1521.02"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardDetails.ts -->
<g id="edge206" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardDetails.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.42,-4443.88C1061.54,-4443.8 1082.45,-4439.69 1095.38,-4425 1113.27,-4404.66 1087.31,-2493.85 1105.75,-2474 1134.46,-2443.09 1266.23,-2489.86 1295,-2459 1311.93,-2440.83 1287.25,-1584.2 1303,-1565 1309.48,-1557.11 1318.75,-1552.46 1328.62,-1549.85"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1328.98,-1551.92 1334.43,-1548.65 1328.13,-1547.8 1328.98,-1551.92"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardsInDeck.ts -->
<g id="edge207" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getCardsInDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.42,-4443.88C1061.54,-4443.8 1082.45,-4439.69 1095.38,-4425 1112.83,-4405.16 1089.51,-2542.86 1105.75,-2522 1159.12,-2453.44 1241.5,-2541.46 1295,-2473 1310,-2453.8 1287.55,-1614.83 1303,-1596 1308.95,-1588.74 1317.27,-1584.24 1326.24,-1581.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1326.7,-1583.57 1332.05,-1580.14 1325.72,-1579.49 1326.7,-1583.57"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckBuilderView.ts -->
<g id="edge208" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckBuilderView.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.42,-4443.88C1061.54,-4443.8 1082.45,-4439.69 1095.38,-4425 1112.71,-4405.3 1087.89,-2555.22 1105.75,-2536 1134.46,-2505.09 1266.23,-2551.86 1295,-2521 1311.93,-2502.83 1287.25,-1646.2 1303,-1627 1307.15,-1621.94 1312.46,-1618.21 1318.34,-1615.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1318.92,-1617.52 1323.81,-1613.46 1317.46,-1613.59 1318.92,-1617.52"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckName.ts -->
<g id="edge209" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getDeckName.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.42,-4443.88C1061.54,-4443.8 1082.45,-4439.69 1095.38,-4425 1112.58,-4405.45 1088.03,-2569.08 1105.75,-2550 1134.46,-2519.09 1266.23,-2565.85 1295,-2535 1311.03,-2517.81 1288.09,-1707.16 1303,-1689 1309.75,-1680.78 1319.52,-1676.09 1329.85,-1673.54"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1330.13,-1675.62 1335.63,-1672.43 1329.34,-1671.49 1330.13,-1675.62"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getSearchTerm.ts -->
<g id="edge210" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getSearchTerm.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.42,-4443.88C1061.54,-4443.8 1082.45,-4439.69 1095.38,-4425 1112.45,-4405.6 1088.16,-2582.94 1105.75,-2564 1134.46,-2533.09 1266.22,-2579.85 1295,-2549 1310.71,-2532.16 1288.39,-1737.8 1303,-1720 1309.41,-1712.19 1318.56,-1707.56 1328.32,-1704.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1328.6,-1707.02 1334.04,-1703.73 1327.74,-1702.91 1328.6,-1707.02"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getTotalCardsInDeck.ts -->
<g id="edge211" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/application/queries/getTotalCardsInDeck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.42,-4443.88C1061.54,-4443.8 1082.45,-4439.69 1095.38,-4425 1112.28,-4405.79 1088.58,-2601.97 1105.75,-2583 1134.13,-2551.64 1266.56,-2594.3 1295,-2563 1310.17,-2546.31 1288.68,-1768.43 1303,-1751 1306.83,-1746.34 1311.64,-1742.81 1316.97,-1740.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1317.51,-1742.21 1322.29,-1738.02 1315.94,-1738.31 1317.51,-1742.21"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts -->
<g id="edge213" class="edge">
<title>src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts&#45;&gt;src/client/domain/DeckBuilder/DeckBuilderCard.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1042.47,-4445.43C1062.08,-4451.16 1083.4,-4461.25 1095.38,-4479 1116.19,-4509.86 1081.07,-5121.13 1105.75,-5149 1172.84,-5224.75 1455.68,-5211 1556.88,-5211 1556.88,-5211 1556.88,-5211 1814.62,-5211 1838.65,-5211 1852.09,-5219.22 1867.75,-5201 2042.42,-4997.7 2042.25,-537.73 2042.02,-218.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2044.12,-218.49 2042.01,-212.49 2039.92,-218.49 2044.12,-218.49"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge219" class="edge">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1080.64,-4794.45C1086.23,-4791.21 1091.26,-4787.12 1095.38,-4782 1113.21,-4759.79 1089.9,-2754.67 1105.75,-2731 1156.48,-2655.27 1214.1,-2693.98 1295,-2652 1378.68,-2608.58 1423.09,-2618.01 1474.5,-2539 1535.35,-2445.49 1552.88,-2079.63 1556.22,-1992.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1558.32,-1992.75 1556.44,-1986.67 1554.12,-1992.59 1558.32,-1992.75"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts -->
<g id="edge220" class="edge">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/commands/initializeDeckBuilderFromLocation/initializeDeckBuilderFromLocation.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1080.65,-4794.46C1086.24,-4791.22 1091.27,-4787.13 1095.38,-4782 1110.69,-4762.89 1092.16,-1275.38 1105.75,-1255 1114.53,-1241.84 1128.44,-1232.89 1143.03,-1226.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1143.49,-1228.89 1148.36,-1224.8 1142,-1224.96 1143.49,-1228.89"/>
</g>
<!-- src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/queries/getAvailableFilters.ts -->
<g id="edge221" class="edge">
<title>src/client/infrastructure/hooks/useInitializeDeckBuilderFromLocation/useInitializeDeckBuilderFromLocation.ts&#45;&gt;src/client/application/queries/getAvailableFilters.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1080.64,-4794.46C1086.23,-4791.21 1091.26,-4787.12 1095.38,-4782 1113.7,-4759.18 1088.07,-2698.32 1105.75,-2675 1158.61,-2605.31 1241.96,-2690.55 1295,-2621 1308.09,-2603.84 1289.3,-1860.68 1303,-1844 1308.01,-1837.9 1314.7,-1833.74 1322.03,-1830.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1322.19,-1833.11 1327.31,-1829.34 1320.96,-1829.09 1322.19,-1833.11"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/domain/CardData/CardData..ts -->
<g id="edge160" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/domain/CardData/CardData..ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M796.73,-3642.85C826.94,-3645.99 865.25,-3655.63 885.5,-3683 897.97,-3699.85 877,-5176.51 891.12,-5192 1038.12,-5353.21 1161.58,-5239 1379.75,-5239 1379.75,-5239 1379.75,-5239 1932.5,-5239 2006.52,-5239 2047.73,-5282.63 2098.75,-5229 2142.94,-5182.55 2147,-777.53 2147.24,-457.34"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="2149.34,-457.75 2147.24,-451.74 2145.14,-457.74 2149.34,-457.75"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge161" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuilderPanel/DeckCardRow.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M796.95,-3647.79C826.66,-3649.01 864.26,-3644.75 885.5,-3620 893.68,-3610.47 883.98,-3178.32 891.12,-3168 904.02,-3149.39 926.69,-3139.22 947.69,-3133.67"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="947.96,-3135.77 953.3,-3132.34 946.98,-3131.68 947.96,-3135.77"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts -->
<g id="edge169" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckCardDetailsDialog/DeckCardDetailsDialog.tsx&#45;&gt;src/client/infrastructure/hooks/useDeckBuilder/useDeckBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M815.73,-3523.65C841.61,-3528.62 869.3,-3539.17 885.5,-3561 899.8,-3580.27 875.17,-4407.07 891.12,-4425 904,-4439.47 924.07,-4444.23 943.24,-4444.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="943.23,-4446.93 949.23,-4444.82 943.22,-4442.73 943.23,-4446.93"/>
</g>
<!-- src/client/infrastructure/store.ts&#45;&gt;src/client/application/appStore.ts -->
<g id="edge241" class="edge">
<title>src/client/infrastructure/store.ts&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1024.45,-5071.57C1046.91,-5073.82 1078.26,-5072.66 1095.38,-5053 1113.68,-5031.98 1095.26,-3071.83 1105.75,-3046 1194.67,-2827.07 1384.35,-2881.42 1474.5,-2663 1484.82,-2637.99 1477.53,-2202.6 1482.5,-2176 1495.78,-2104.88 1531.51,-2025.81 1548.14,-1991.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1549.84,-1992.78 1550.6,-1986.47 1546.07,-1990.93 1549.84,-1992.78"/>
</g>
<!-- src/client/infrastructure/store.ts&#45;&gt;src/client/application/services/DeckDraftService.ts -->
<g id="edge242" class="edge">
<title>src/client/infrastructure/store.ts&#45;&gt;src/client/application/services/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1012.41,-5076.66C1059.07,-5107.19 1209.91,-5195.42 1295,-5122 1700.76,-4771.91 1579.9,-3266.61 1617.25,-2732 1618.58,-2712.93 1618.59,-2059.15 1630.25,-2044 1630.85,-2043.22 1631.49,-2042.47 1632.16,-2041.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1633.27,-2043.57 1636.6,-2038.16 1630.62,-2040.3 1633.27,-2043.57"/>
</g>
<!-- src/client/infrastructure/store.ts&#45;&gt;src/client/application/subscribers/subscribeToDeckDraft.ts -->
<g id="edge243" class="edge">
<title>src/client/infrastructure/store.ts&#45;&gt;src/client/application/subscribers/subscribeToDeckDraft.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M969.97,-5072.71C940.06,-5077.29 895.34,-5078.53 888.31,-5047 885.21,-5033.06 887.71,-3005.27 888.31,-2991 910.27,-2467.24 981.89,-1827.64 995.13,-1712.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="997.19,-1712.74 995.79,-1706.54 993.02,-1712.26 997.19,-1712.74"/>
</g>
<!-- src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts -->
<g id="node142" class="node">
<title>src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts</title>
<g id="a_node142"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts" xlink:title="browserDeckDraftService.ts">
<polygon fill="#6cbaff" stroke="black" points="1269,-5031.25 1139.75,-5031.25 1139.75,-5012.75 1269,-5012.75 1269,-5031.25"/>
<text text-anchor="start" x="1147.75" y="-5018.7" font-family="Helvetica,sans-Serif" font-size="9.00">browserDeckDraftService.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/store.ts&#45;&gt;src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts -->
<g id="edge244" class="edge">
<title>src/client/infrastructure/store.ts&#45;&gt;src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1024.39,-5064.21C1044.04,-5061.88 1071.56,-5058.11 1095.38,-5053 1118.91,-5047.95 1144.84,-5040.57 1165.4,-5034.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1165.85,-5036.33 1170.96,-5032.55 1164.61,-5032.32 1165.85,-5036.33"/>
</g>
<!-- src/client/infrastructure/services/location/browserLocationService.ts -->
<g id="node143" class="node">
<title>src/client/infrastructure/services/location/browserLocationService.ts</title>
<g id="a_node143"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/services/location/browserLocationService.ts" xlink:title="browserLocationService.ts">
<polygon fill="#6cbaff" stroke="black" points="1266.38,-5092.25 1142.38,-5092.25 1142.38,-5073.75 1266.38,-5073.75 1266.38,-5092.25"/>
<text text-anchor="start" x="1150.38" y="-5079.7" font-family="Helvetica,sans-Serif" font-size="9.00">browserLocationService.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/store.ts&#45;&gt;src/client/infrastructure/services/location/browserLocationService.ts -->
<g id="edge245" class="edge">
<title>src/client/infrastructure/store.ts&#45;&gt;src/client/infrastructure/services/location/browserLocationService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1024.65,-5069.06C1051.99,-5071.19 1095.89,-5074.62 1133.34,-5077.54"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1133.05,-5079.62 1139.19,-5077.99 1133.37,-5075.43 1133.05,-5079.62"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge167" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingFilters/FilterItem.tsx&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M785.94,-3458.95C817.2,-3460.88 862.75,-3469.25 885.5,-3500 892.42,-3509.36 882.97,-5167.69 891.12,-5176 939.73,-5225.54 1133.97,-5183 1203.38,-5183 1203.38,-5183 1203.38,-5183 1688.38,-5183 1716.78,-5183 1729.8,-5191.33 1751.5,-5173 1875.1,-5068.58 1840.43,-4987.48 1867.75,-4828 1907.33,-4596.96 1928.8,-754.65 1930.4,-457.22"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1932.5,-457.51 1930.43,-451.49 1928.3,-457.48 1932.5,-457.51"/>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx -->
<g id="node121" class="node">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx</title>
<g id="a_node121"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx" xlink:title="DeckBuildingSkeletonCard.tsx">
<polygon fill="#6cbaff" stroke="black" points="590.5,-3407.25 450,-3407.25 450,-3388.75 590.5,-3388.75 590.5,-3407.25"/>
<text text-anchor="start" x="458" y="-3394.7" font-family="Helvetica,sans-Serif" font-size="9.00">DeckBuildingSkeletonCard.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx -->
<g id="edge168" class="edge">
<title>src/client/infrastructure/components/app/DeckBuilding/DeckBuildingSkeletonCard/DeckBuildingSkeletonCard.tsx&#45;&gt;src/client/infrastructure/components/ui/ShiningCard/ShiningCard.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M590.71,-3400.24C609.31,-3397.22 627.65,-3390.27 640,-3376 659.94,-3352.96 628.79,-3123.12 650.75,-3102 688.35,-3065.84 834.89,-3089.35 885.5,-3102 888.25,-3102.69 888.46,-3104.03 891.12,-3105 909.13,-3111.53 929.6,-3116.36 947.67,-3119.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="947.07,-3121.83 953.35,-3120.85 947.82,-3117.7 947.07,-3121.83"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/appStore.ts -->
<g id="edge214" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/appStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1069.96,-4616.46C1079.8,-4612.78 1088.77,-4607.22 1095.38,-4599 1112.11,-4578.17 1088.66,-2695.54 1105.75,-2675 1160.52,-2609.19 1220.59,-2681.35 1295,-2639 1399.67,-2579.43 1422.09,-2544.43 1474.5,-2436 1551.22,-2277.27 1557.1,-2056.78 1557.08,-1992.36"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1559.18,-1992.72 1557.05,-1986.73 1554.98,-1992.74 1559.18,-1992.72"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettings.ts -->
<g id="edge215" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/commands/loadGameSettings/loadGameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1069.98,-4616.48C1079.82,-4612.79 1088.78,-4607.23 1095.38,-4599 1111.48,-4578.89 1091.46,-910.44 1105.75,-889 1115.04,-875.06 1130.09,-865.85 1145.61,-859.77"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1145.95,-861.88 1150.9,-857.88 1144.54,-857.92 1145.95,-861.88"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettings.ts -->
<g id="edge216" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettings.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1069.96,-4616.47C1079.8,-4612.78 1088.77,-4607.22 1095.38,-4599 1112.67,-4577.46 1087.92,-2631.1 1105.75,-2610 1160.86,-2544.79 1239.73,-2642.08 1295,-2577 1309.3,-2560.17 1288.98,-1799.07 1303,-1782 1308.45,-1775.36 1315.88,-1771.03 1323.97,-1768.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1324.44,-1770.31 1329.67,-1766.71 1323.33,-1766.26 1324.44,-1770.31"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettingsError.ts -->
<g id="edge217" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/getGameSettingsError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1069.96,-4616.46C1079.8,-4612.78 1088.77,-4607.22 1095.38,-4599 1112.55,-4577.61 1087.08,-2644.1 1105.75,-2624 1134.46,-2593.09 1266.22,-2639.84 1295,-2609 1310.09,-2592.83 1288.96,-1830.09 1303,-1813 1306.32,-1808.96 1310.37,-1805.77 1314.87,-1803.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1315.43,-1805.32 1320.03,-1800.93 1313.7,-1801.49 1315.43,-1805.32"/>
</g>
<!-- src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/isGameSettingsLoading.ts -->
<g id="edge218" class="edge">
<title>src/client/infrastructure/hooks/useGameSettingsByGameId/useGameSettingsByGameId.tsx&#45;&gt;src/client/application/queries/isGameSettingsLoading.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1069.96,-4616.46C1079.8,-4612.78 1088.77,-4607.22 1095.38,-4599 1112.36,-4577.85 1087.71,-2666.26 1105.75,-2646 1162.1,-2582.73 1238.46,-2686.11 1295,-2623 1308.29,-2608.16 1290.35,-1921.38 1303,-1906 1305.32,-1903.18 1307.99,-1900.78 1310.92,-1898.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1311.84,-1900.63 1316.03,-1895.85 1309.77,-1896.97 1311.84,-1900.63"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/Match/Match.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge182" class="edge">
<title>src/client/infrastructure/components/app/Gaming/Match/Match.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M544.01,-4148.63C569.75,-4159.4 612.99,-4177.16 650.75,-4191 753.98,-4228.83 820.81,-4180.1 885.5,-4269 896.88,-4284.63 880.67,-4948.74 891.12,-4965 905.51,-4987.38 933.05,-4999.71 956.33,-5006.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="955.75,-5008.42 962.08,-5007.93 956.82,-5004.36 955.75,-5008.42"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts -->
<g id="edge183" class="edge">
<title>src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/builders/urlBuilder.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M570.33,-4083.54C594.6,-4088.8 622.39,-4099.05 640,-4119 655.33,-4136.37 633.46,-4154.58 650.75,-4170 689.85,-4204.87 850.08,-4153.39 885.5,-4192 900.02,-4207.82 879.52,-4946.93 891.12,-4965 905.5,-4987.38 933.04,-4999.72 956.33,-5006.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="955.74,-5008.42 962.08,-5007.93 956.82,-5004.36 955.74,-5008.42"/>
</g>
<!-- src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts -->
<g id="edge184" class="edge">
<title>src/client/infrastructure/components/app/Gaming/StartGameButton/StartGameButton.tsx&#45;&gt;src/client/infrastructure/hooks/useLocale/useLocale.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M570.13,-4084.77C593.9,-4090.36 621.31,-4100.52 640,-4119 650.07,-4128.96 639.78,-4140.04 650.75,-4149 732.37,-4215.72 816.17,-4103.59 885.5,-4183 897.56,-4196.81 879.5,-4828.82 891.12,-4843 906.03,-4861.19 931.77,-4866.8 954.04,-4867.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.95,-4869.9 959.99,-4867.92 954.03,-4865.7 953.95,-4869.9"/>
</g>
<!-- src/client/infrastructure/components/debug/JsonObjectViewer/JsonObjectViewer.tsx -->
<g id="node126" class="node">
<title>src/client/infrastructure/components/debug/JsonObjectViewer/JsonObjectViewer.tsx</title>
<g id="a_node126"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/debug/JsonObjectViewer/JsonObjectViewer.tsx" xlink:title="JsonObjectViewer.tsx">
<polygon fill="#6cbaff" stroke="black" points="571.75,-2897.25 468.75,-2897.25 468.75,-2878.75 571.75,-2878.75 571.75,-2897.25"/>
<text text-anchor="start" x="476.75" y="-2884.7" font-family="Helvetica,sans-Serif" font-size="9.00">JsonObjectViewer.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx -->
<g id="node128" class="node">
<title>src/client/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx</title>
<g id="a_node128"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/debug/MatchMakingConsoleEvents/MatchMakingConsoleEvents.tsx" xlink:title="MatchMakingConsoleEvents.tsx">
<polygon fill="#6cbaff" stroke="black" points="593.5,-3019.25 447,-3019.25 447,-3000.75 593.5,-3000.75 593.5,-3019.25"/>
<text text-anchor="start" x="455" y="-3006.7" font-family="Helvetica,sans-Serif" font-size="9.00">MatchMakingConsoleEvents.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/SkeletonHelper/SkeletonHelper.tsx -->
<g id="node132" class="node">
<title>src/client/infrastructure/components/ui/SkeletonHelper/SkeletonHelper.tsx</title>
<g id="a_node132"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/components/ui/SkeletonHelper/SkeletonHelper.tsx" xlink:title="SkeletonHelper.tsx">
<polygon fill="#6cbaff" stroke="black" points="1043.5,-3197.25 951,-3197.25 951,-3178.75 1043.5,-3178.75 1043.5,-3197.25"/>
<text text-anchor="start" x="959" y="-3184.7" font-family="Helvetica,sans-Serif" font-size="9.00">SkeletonHelper.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts -->
<g id="edge189" class="edge">
<title>src/client/infrastructure/components/ui/Sparkles/Sparkles.tsx&#45;&gt;src/client/infrastructure/lib/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1000.15,-3258.56C1019.1,-3353.91 1174.02,-4133.33 1199.76,-4262.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1197.62,-4262.84 1200.85,-4268.32 1201.74,-4262.02 1197.62,-4262.84"/>
</g>
<!-- src/client/infrastructure/hooks/useDeckById/useDeckById.tsx -->
<g id="node135" class="node">
<title>src/client/infrastructure/hooks/useDeckById/useDeckById.tsx</title>
<g id="a_node135"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/hooks/useDeckById/useDeckById.tsx" xlink:title="useDeckById.tsx">
<polygon fill="#6cbaff" stroke="black" points="1039,-4569.25 955.5,-4569.25 955.5,-4550.75 1039,-4550.75 1039,-4569.25"/>
<text text-anchor="start" x="963.5" y="-4556.7" font-family="Helvetica,sans-Serif" font-size="9.00">useDeckById.tsx</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/lib/groupByDataProperty.ts -->
<g id="node137" class="node">
<title>src/client/infrastructure/lib/groupByDataProperty.ts</title>
<g id="a_node137"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/client/infrastructure/lib/groupByDataProperty.ts" xlink:title="groupByDataProperty.ts">
<polygon fill="#6cbaff" stroke="black" points="1260.75,-4352.25 1148,-4352.25 1148,-4333.75 1260.75,-4333.75 1260.75,-4352.25"/>
<text text-anchor="start" x="1156" y="-4339.7" font-family="Helvetica,sans-Serif" font-size="9.00">groupByDataProperty.ts</text>
</a>
</g>
</g>
<!-- src/client/infrastructure/lib/groupByDataProperty.ts&#45;&gt;src/client/domain/Catalog/Filter.ts -->
<g id="edge224" class="edge">
<title>src/client/infrastructure/lib/groupByDataProperty.ts&#45;&gt;src/client/domain/Catalog/Filter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1212.62,-4352.47C1228.88,-4374.04 1268.9,-4428.83 1295,-4479 1441.02,-4759.7 1240.47,-5141 1556.88,-5141 1556.88,-5141 1556.88,-5141 1688.38,-5141 1940.31,-5141 1931.55,-778.94 1930.56,-457.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1932.66,-457.65 1930.54,-451.66 1928.46,-457.67 1932.66,-457.65"/>
</g>
<!-- src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/application/services/DeckDraftService.ts -->
<g id="edge239" class="edge">
<title>src/client/infrastructure/services/deckDraft/browserDeckDraftService.ts&#45;&gt;src/client/application/services/DeckDraftService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1207.31,-5012.48C1217.35,-4962.97 1264.14,-4729.95 1295,-4538 1463,-3493.24 1494.9,-3230.08 1617.25,-2179 1620.73,-2149.06 1610.96,-2067.16 1630.25,-2044 1630.79,-2043.35 1631.35,-2042.73 1631.94,-2042.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1633.16,-2043.84 1636.57,-2038.48 1630.56,-2040.54 1633.16,-2043.84"/>
</g>
<!-- src/client/infrastructure/services/location/browserLocationService.ts&#45;&gt;src/client/application/services/LocationService.ts -->
<g id="edge240" class="edge">
<title>src/client/infrastructure/services/location/browserLocationService.ts&#45;&gt;src/client/application/services/LocationService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1266.55,-5078.12C1277.28,-5074.65 1287.42,-5069.26 1295,-5061 2001.46,-4291.01 1501.01,-3770.49 1617.25,-2732 1646.32,-2472.3 1677.07,-2155.68 1684.63,-2077.23"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1686.69,-2077.68 1685.18,-2071.51 1682.51,-2077.28 1686.69,-2077.68"/>
</g>
<!-- src/server/DependencyInjection.ts -->
<g id="node144" class="node">
<title>src/server/DependencyInjection.ts</title>
<g id="a_node144"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/DependencyInjection.ts" xlink:title="DependencyInjection.ts">
<path fill="#ccffcc" stroke="black" d="M570.08,-7070.25C570.08,-7070.25 470.42,-7070.25 470.42,-7070.25 467.33,-7070.25 464.25,-7067.17 464.25,-7064.08 464.25,-7064.08 464.25,-7057.92 464.25,-7057.92 464.25,-7054.83 467.33,-7051.75 470.42,-7051.75 470.42,-7051.75 570.08,-7051.75 570.08,-7051.75 573.17,-7051.75 576.25,-7054.83 576.25,-7057.92 576.25,-7057.92 576.25,-7064.08 576.25,-7064.08 576.25,-7067.17 573.17,-7070.25 570.08,-7070.25"/>
<text text-anchor="start" x="472.25" y="-7057.7" font-family="Helvetica,sans-Serif" font-size="9.00">DependencyInjection.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts -->
<g id="node145" class="node">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts</title>
<g id="a_node145"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts" xlink:title="SaveDeckCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="807,-5383.25 697.25,-5383.25 697.25,-5364.75 807,-5364.75 807,-5383.25"/>
<text text-anchor="start" x="705.25" y="-5370.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">SaveDeckCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts -->
<g id="node146" class="node">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts</title>
<g id="a_node146"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts" xlink:title="SaveDeckCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="591.25,-5383.25 449.25,-5383.25 449.25,-5364.75 591.25,-5364.75 591.25,-5383.25"/>
<text text-anchor="start" x="457.25" y="-5370.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">SaveDeckCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts -->
<g id="edge246" class="edge">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/commands/Deck/SaveDeck/SaveDeckCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M591.47,-5374C622.35,-5374 658.34,-5374 688.32,-5374"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="687.99,-5376.1 693.99,-5374 687.99,-5371.9 687.99,-5376.1"/>
</g>
<!-- src/server/application/ports/DeckRepository.ts -->
<g id="node147" class="node">
<title>src/server/application/ports/DeckRepository.ts</title>
<g id="a_node147"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/ports/DeckRepository.ts" xlink:title="DeckRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="796.88,-6243.25 707.38,-6243.25 707.38,-6224.75 796.88,-6224.75 796.88,-6243.25"/>
<text text-anchor="start" x="715.38" y="-6230.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">DeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/ports/DeckRepository.ts -->
<g id="edge247" class="edge">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M551.06,-5383.71C579.59,-5394.78 621.02,-5415.88 640,-5450 660.75,-5487.29 622.83,-6185.73 650.75,-6218 662.46,-6231.53 680.64,-6236.85 698.33,-6238.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="697.82,-6240.42 703.92,-6238.63 698.04,-6236.23 697.82,-6240.42"/>
</g>
<!-- src/server/domain/Deck/Deck.ts -->
<g id="node148" class="node">
<title>src/server/domain/Deck/Deck.ts</title>
<g id="a_node148"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/domain/Deck/Deck.ts" xlink:title="Deck.ts">
<polygon fill="#dc6b0d" stroke="black" points="1024.25,-6318.25 970.25,-6318.25 970.25,-6299.75 1024.25,-6299.75 1024.25,-6318.25"/>
<text text-anchor="start" x="982.25" y="-6305.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">Deck.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge248" class="edge">
<title>src/server/application/commands/Deck/SaveDeck/SaveDeckCommandHandler.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M541.4,-5364.44C602.99,-5336.77 790.34,-5264.41 885.5,-5359 894.72,-5368.16 882.53,-6285.25 891.12,-6295 907.99,-6314.15 937.41,-6316.67 960.85,-6314.95"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.04,-6317.04 966.81,-6314.36 960.63,-6312.86 961.04,-6317.04"/>
</g>
<!-- src/server/application/ports/DeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge277" class="edge">
<title>src/server/application/ports/DeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M770.39,-6243.56C795.95,-6257.26 845.74,-6282.23 891.12,-6295 913.97,-6301.43 940.47,-6304.93 961.15,-6306.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="960.74,-6308.9 966.9,-6307.31 961.09,-6304.72 960.74,-6308.9"/>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts -->
<g id="node149" class="node">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts</title>
<g id="a_node149"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts" xlink:title="LeaveMatchCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="811.12,-5479.25 693.12,-5479.25 693.12,-5460.75 811.12,-5460.75 811.12,-5479.25"/>
<text text-anchor="start" x="701.12" y="-5466.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">LeaveMatchCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts -->
<g id="node150" class="node">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts</title>
<g id="a_node150"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts" xlink:title="LeaveMatchCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="595.38,-5479.25 445.12,-5479.25 445.12,-5460.75 595.38,-5460.75 595.38,-5479.25"/>
<text text-anchor="start" x="453.12" y="-5466.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">LeaveMatchCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts -->
<g id="edge249" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/commands/Match/LeaveMatch/LeaveMatchCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M595.57,-5470C623.97,-5470 656.18,-5470 683.89,-5470"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="683.69,-5472.1 689.69,-5470 683.69,-5467.9 683.69,-5472.1"/>
</g>
<!-- src/server/application/ports/Context.ts -->
<g id="node151" class="node">
<title>src/server/application/ports/Context.ts</title>
<g id="a_node151"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/ports/Context.ts" xlink:title="Context.ts">
<polygon fill="#dd1c1c" stroke="black" points="780.38,-5995.25 723.88,-5995.25 723.88,-5976.75 780.38,-5976.75 780.38,-5995.25"/>
<text text-anchor="start" x="731.88" y="-5982.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">Context.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge250" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M550.68,-5479.67C579.13,-5490.76 620.69,-5511.93 640,-5546 659.95,-5581.2 632.2,-5874.04 650.75,-5910 665.63,-5938.85 697.02,-5959.8 720.76,-5972.31"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="719.79,-5974.18 726.1,-5975.01 721.69,-5970.43 719.79,-5974.18"/>
</g>
<!-- src/server/application/ports/MatchRepository.ts -->
<g id="node152" class="node">
<title>src/server/application/ports/MatchRepository.ts</title>
<g id="a_node152"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/ports/MatchRepository.ts" xlink:title="MatchRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="798.75,-6057.25 705.5,-6057.25 705.5,-6038.75 798.75,-6038.75 798.75,-6057.25"/>
<text text-anchor="start" x="713.5" y="-6044.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge251" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M551,-5479.75C579.49,-5490.84 620.88,-5511.96 640,-5546 653.23,-5569.55 632.99,-6011.65 650.75,-6032 662.06,-6044.96 679.32,-6050.37 696.35,-6052.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="696.06,-6054.19 702.18,-6052.49 696.33,-6050 696.06,-6054.19"/>
</g>
<!-- src/server/domain/Match/errors/MatchAlreadyFinishedError.ts -->
<g id="node153" class="node">
<title>src/server/domain/Match/errors/MatchAlreadyFinishedError.ts</title>
<g id="a_node153"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/domain/Match/errors/MatchAlreadyFinishedError.ts" xlink:title="MatchAlreadyFinishedError.ts">
<polygon fill="#dc6b0d" stroke="black" points="1065.25,-6143.25 929.25,-6143.25 929.25,-6124.75 1065.25,-6124.75 1065.25,-6143.25"/>
<text text-anchor="start" x="937.25" y="-6130.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchAlreadyFinishedError.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchAlreadyFinishedError.ts -->
<g id="edge252" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchAlreadyFinishedError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M564.41,-5460.26C590.45,-5452.08 621.97,-5437.63 640,-5413 660.15,-5385.47 624.99,-5359.37 650.75,-5337 690.14,-5302.79 848.48,-5300.25 885.5,-5337 900.9,-5352.29 876.93,-6101.6 891.12,-6118 898.78,-6126.84 909.08,-6132.29 920.26,-6135.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="919.67,-6137.5 925.99,-6136.81 920.62,-6133.41 919.67,-6137.5"/>
</g>
<!-- src/server/domain/Match/errors/MatchNotFoundError.ts -->
<g id="node154" class="node">
<title>src/server/domain/Match/errors/MatchNotFoundError.ts</title>
<g id="a_node154"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/domain/Match/errors/MatchNotFoundError.ts" xlink:title="MatchNotFoundError.ts">
<polygon fill="#dc6b0d" stroke="black" points="1052.12,-6112.25 942.38,-6112.25 942.38,-6093.75 1052.12,-6093.75 1052.12,-6112.25"/>
<text text-anchor="start" x="950.38" y="-6099.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchNotFoundError.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchNotFoundError.ts -->
<g id="edge253" class="edge">
<title>src/server/application/commands/Match/LeaveMatch/LeaveMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/errors/MatchNotFoundError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M565.33,-5460.31C591.38,-5452.19 622.58,-5437.79 640,-5413 653.49,-5393.81 633.34,-5323.72 650.75,-5308 728.19,-5238.08 806.8,-5239.5 885.5,-5308 946.46,-5361.06 987.89,-5970.81 995.12,-6084.73"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="993,-6084.52 995.47,-6090.38 997.19,-6084.26 993,-6084.52"/>
</g>
<!-- src/server/domain/Match/Match.ts -->
<g id="node166" class="node">
<title>src/server/domain/Match/Match.ts</title>
<g id="a_node166"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/domain/Match/Match.ts" xlink:title="Match.ts">
<polygon fill="#dc6b0d" stroke="black" points="1024.25,-6196.25 970.25,-6196.25 970.25,-6177.75 1024.25,-6177.75 1024.25,-6196.25"/>
<text text-anchor="start" x="980.38" y="-6183.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">Match.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/MatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge278" class="edge">
<title>src/server/application/ports/MatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M798.95,-6040.08C828,-6037.91 864.02,-6040.99 885.5,-6064 902.05,-6081.73 874.68,-6155.17 891.12,-6173 908.43,-6191.76 937.81,-6194.31 961.12,-6192.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.24,-6194.8 967.02,-6192.15 960.85,-6190.62 961.24,-6194.8"/>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts -->
<g id="node155" class="node">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts</title>
<g id="a_node155"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts" xlink:title="AddPlayerToMatchMakingQueueCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="853.5,-5575.25 650.75,-5575.25 650.75,-5556.75 853.5,-5556.75 853.5,-5575.25"/>
<text text-anchor="start" x="658.75" y="-5562.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AddPlayerToMatchMakingQueueCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts -->
<g id="node156" class="node">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts</title>
<g id="a_node156"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts" xlink:title="AddPlayerToMatchMakingQueueCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="637.75,-5575.25 402.75,-5575.25 402.75,-5556.75 637.75,-5556.75 637.75,-5575.25"/>
<text text-anchor="start" x="410.75" y="-5562.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AddPlayerToMatchMakingQueueCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge255" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.18,-5575.66C610.59,-5581.68 628.46,-5591.39 640,-5607 660.03,-5634.08 635.22,-5880.1 650.75,-5910 665.71,-5938.8 697.09,-5959.76 720.8,-5972.29"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="719.83,-5974.15 726.13,-5974.99 721.73,-5970.4 719.83,-5974.15"/>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts -->
<g id="edge254" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M638.21,-5566C639.36,-5566 640.52,-5566 641.67,-5566"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="641.46,-5568.1 647.46,-5566 641.46,-5563.9 641.46,-5568.1"/>
</g>
<!-- src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="node157" class="node">
<title>src/server/application/ports/MatchmakingQueueRepository.ts</title>
<g id="a_node157"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/ports/MatchmakingQueueRepository.ts" xlink:title="MatchmakingQueueRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="827.62,-6088.25 676.62,-6088.25 676.62,-6069.75 827.62,-6069.75 827.62,-6088.25"/>
<text text-anchor="start" x="684.62" y="-6075.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge256" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.61,-5575.67C610.91,-5581.7 628.61,-5591.41 640,-5607 654.95,-5627.46 634.08,-6043.92 650.75,-6063 655.67,-6068.63 661.72,-6072.84 668.38,-6075.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="667.28,-6077.77 673.64,-6077.99 668.81,-6073.85 667.28,-6077.77"/>
</g>
<!-- src/server/application/ports/TimeService.ts -->
<g id="node158" class="node">
<title>src/server/application/ports/TimeService.ts</title>
<g id="a_node158"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/ports/TimeService.ts" xlink:title="TimeService.ts">
<polygon fill="#dd1c1c" stroke="black" points="790.12,-6026.25 714.12,-6026.25 714.12,-6007.75 790.12,-6007.75 790.12,-6026.25"/>
<text text-anchor="start" x="722.12" y="-6013.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">TimeService.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/TimeService.ts -->
<g id="edge257" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/TimeService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.58,-5575.7C610.87,-5581.73 628.58,-5591.44 640,-5607 665.9,-5642.31 621.87,-5968.08 650.75,-6001 664.07,-6016.19 685.55,-6020.99 705.22,-6021.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="705.06,-6023.76 711.07,-6021.69 705.08,-6019.56 705.06,-6023.76"/>
</g>
<!-- src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="node159" class="node">
<title>src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<g id="a_node159"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts" xlink:title="MatchmakingQueueItem.ts">
<polygon fill="#dc6b0d" stroke="black" points="1266.75,-6379.25 1142,-6379.25 1142,-6360.75 1266.75,-6360.75 1266.75,-6379.25"/>
<text text-anchor="start" x="1150" y="-6366.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchmakingQueueItem.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge258" class="edge">
<title>src/server/application/commands/MatchMaking/AddPlayerToMatchMakingQueue/AddPlayerToMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M566.57,-5556.27C592.56,-5548.19 623.26,-5533.85 640,-5509 661.4,-5477.24 622.84,-5363.22 650.75,-5337 726.79,-5265.56 801.57,-5275.02 885.5,-5337 1062.21,-5467.51 1181.59,-6223.47 1200.69,-6351.56"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1198.58,-6351.65 1201.53,-6357.28 1202.73,-6351.03 1198.58,-6351.65"/>
</g>
<!-- src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge280" class="edge">
<title>src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M827.89,-6070.35C915.45,-6061.83 1053.83,-6053.88 1095.38,-6083 1186.71,-6147.01 1200.88,-6298.46 1203.02,-6351.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1200.92,-6351.36 1203.21,-6357.29 1205.12,-6351.23 1200.92,-6351.36"/>
</g>
<!-- src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="node176" class="node">
<title>src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<g id="a_node176"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/domain/MatchmakingQueue/MatchmakingQueue.ts" xlink:title="MatchmakingQueue.ts">
<polygon fill="#dc6b0d" stroke="black" points="1051,-6379.25 943.5,-6379.25 943.5,-6360.75 1051,-6360.75 1051,-6379.25"/>
<text text-anchor="start" x="951.5" y="-6366.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MatchmakingQueue.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge279" class="edge">
<title>src/server/application/ports/MatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M827.85,-6070.77C849.39,-6072.51 870.96,-6078.96 885.5,-6095 894.94,-6105.42 882.14,-6337.19 891.12,-6348 901.83,-6360.88 917.97,-6367.43 934.41,-6370.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="933.77,-6372.54 940.01,-6371.36 934.4,-6368.38 933.77,-6372.54"/>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts -->
<g id="node160" class="node">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts</title>
<g id="a_node160"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts" xlink:title="CancelMatchRegistrationCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="837.75,-5636.25 666.5,-5636.25 666.5,-5617.75 837.75,-5617.75 837.75,-5636.25"/>
<text text-anchor="start" x="674.5" y="-5623.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CancelMatchRegistrationCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts -->
<g id="node161" class="node">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts</title>
<g id="a_node161"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts" xlink:title="CancelMatchRegistrationCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="622,-5636.25 418.5,-5636.25 418.5,-5617.75 622,-5617.75 622,-5636.25"/>
<text text-anchor="start" x="426.5" y="-5623.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CancelMatchRegistrationCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge260" class="edge">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.09,-5636.72C610.5,-5642.75 628.39,-5652.45 640,-5668 656.1,-5689.57 638.24,-5886.17 650.75,-5910 665.84,-5938.74 697.2,-5959.71 720.87,-5972.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="719.89,-5974.11 726.19,-5974.96 721.8,-5970.37 719.89,-5974.11"/>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge261" class="edge">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.58,-5636.7C610.87,-5642.73 628.58,-5652.43 640,-5668 665.96,-5703.4 621.8,-6029.99 650.75,-6063 655.68,-6068.63 661.74,-6072.83 668.4,-6075.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="667.31,-6077.75 673.66,-6077.97 668.83,-6073.83 667.31,-6077.75"/>
</g>
<!-- src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts -->
<g id="edge259" class="edge">
<title>src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CancelMatchRegistration/CancelMatchRegistrationCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M622.41,-5627C634.03,-5627 645.83,-5627 657.34,-5627"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="657.05,-5629.1 663.05,-5627 657.05,-5624.9 657.05,-5629.1"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts -->
<g id="node162" class="node">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts</title>
<g id="a_node162"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts" xlink:title="CleanUpMatchMakingQueueCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="845.62,-5697.25 658.62,-5697.25 658.62,-5678.75 845.62,-5678.75 845.62,-5697.25"/>
<text text-anchor="start" x="666.62" y="-5684.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CleanUpMatchMakingQueueCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts -->
<g id="node163" class="node">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts</title>
<g id="a_node163"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts" xlink:title="CleanUpMatchMakingQueueCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="629.88,-5697.25 410.62,-5697.25 410.62,-5678.75 629.88,-5678.75 629.88,-5697.25"/>
<text text-anchor="start" x="418.62" y="-5684.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CleanUpMatchMakingQueueCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge263" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M591.64,-5697.73C610.14,-5703.76 628.2,-5713.45 640,-5729 664.35,-5761.11 631.75,-5874.47 650.75,-5910 666.05,-5938.62 697.38,-5959.61 720.98,-5972.19"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="719.98,-5974.04 726.28,-5974.91 721.9,-5970.3 719.98,-5974.04"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge265" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.18,-5697.66C610.59,-5703.68 628.46,-5713.39 640,-5729 660.03,-5756.08 628.43,-6006.76 650.75,-6032 662.1,-6044.83 679.27,-6050.23 696.23,-6051.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="695.89,-6054.08 702.02,-6052.39 696.18,-6049.89 695.89,-6054.08"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge264" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.53,-5697.73C610.82,-5703.77 628.55,-5713.46 640,-5729 662.03,-5758.89 626.2,-6035.15 650.75,-6063 655.7,-6068.61 661.76,-6072.81 668.43,-6075.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="667.34,-6077.72 673.7,-6077.94 668.86,-6073.81 667.34,-6077.72"/>
</g>
<!-- src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts -->
<g id="edge262" class="edge">
<title>src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M630.13,-5688C636.59,-5688 643.08,-5688 649.51,-5688"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="649.21,-5690.1 655.21,-5688 649.21,-5685.9 649.21,-5690.1"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts -->
<g id="node164" class="node">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts</title>
<g id="a_node164"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts" xlink:title="MakeMatchCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="809.62,-5758.25 694.62,-5758.25 694.62,-5739.75 809.62,-5739.75 809.62,-5758.25"/>
<text text-anchor="start" x="702.62" y="-5745.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MakeMatchCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts -->
<g id="node165" class="node">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts</title>
<g id="a_node165"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts" xlink:title="MakeMatchCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="593.88,-5758.25 446.62,-5758.25 446.62,-5739.75 593.88,-5739.75 593.88,-5758.25"/>
<text text-anchor="start" x="454.62" y="-5745.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">MakeMatchCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge267" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M590.41,-5758.66C609.23,-5764.67 627.77,-5774.37 640,-5790 656.5,-5811.09 637.77,-5886.58 650.75,-5910 666.58,-5938.56 698.11,-5959.67 721.63,-5972.3"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="720.6,-5974.12 726.89,-5975.02 722.53,-5970.39 720.6,-5974.12"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge269" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.09,-5758.72C610.5,-5764.75 628.39,-5774.45 640,-5790 656.1,-5811.57 632.83,-6011.92 650.75,-6032 662.16,-6044.78 679.34,-6050.17 696.29,-6051.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="695.95,-6054.02 702.08,-6052.34 696.24,-6049.83 695.95,-6054.02"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge268" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.14,-5758.68C610.55,-5764.71 628.43,-5774.42 640,-5790 658.1,-5814.37 630.6,-6040.3 650.75,-6063 655.68,-6068.55 661.69,-6072.71 668.31,-6075.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="667.14,-6077.58 673.5,-6077.81 668.67,-6073.67 667.14,-6077.58"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts -->
<g id="edge266" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M594.3,-5749C623.61,-5749 657.13,-5749 685.63,-5749"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="685.28,-5751.1 691.28,-5749 685.28,-5746.9 685.28,-5751.1"/>
</g>
<!-- src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge270" class="edge">
<title>src/server/application/commands/MatchMaking/MakeMatch/MakeMatchCommandHandler.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M594.21,-5750.73C611.62,-5747.43 628.48,-5740.46 640,-5727 666.97,-5695.51 621.01,-5382.89 650.75,-5354 669.46,-5335.82 866.99,-5335.62 885.5,-5354 901.64,-5370.03 876.08,-6155.94 891.12,-6173 908,-6192.14 937.42,-6194.66 960.85,-6192.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.04,-6195.04 966.81,-6192.36 960.63,-6190.86 961.04,-6195.04"/>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts -->
<g id="node167" class="node">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts</title>
<g id="a_node167"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts" xlink:title="UpdatePlayersStatusCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="829.12,-5819.25 675.12,-5819.25 675.12,-5800.75 829.12,-5800.75 829.12,-5819.25"/>
<text text-anchor="start" x="683.12" y="-5806.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdatePlayersStatusCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts -->
<g id="node168" class="node">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts</title>
<g id="a_node168"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts" xlink:title="UpdatePlayersStatusCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="613.38,-5819.25 427.12,-5819.25 427.12,-5800.75 613.38,-5800.75 613.38,-5819.25"/>
<text text-anchor="start" x="435.12" y="-5806.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdatePlayersStatusCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts -->
<g id="edge271" class="edge">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M613.76,-5810C630.97,-5810 648.88,-5810 665.86,-5810"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="665.62,-5812.1 671.62,-5810 665.62,-5807.9 665.62,-5812.1"/>
</g>
<!-- src/server/application/ports/AppUserRepository.ts -->
<g id="node169" class="node">
<title>src/server/application/ports/AppUserRepository.ts</title>
<g id="a_node169"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/ports/AppUserRepository.ts" xlink:title="AppUserRepository.ts">
<polygon fill="#dd1c1c" stroke="black" points="804.38,-6119.25 699.88,-6119.25 699.88,-6100.75 804.38,-6100.75 804.38,-6119.25"/>
<text text-anchor="start" x="707.88" y="-6106.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge272" class="edge">
<title>src/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M592.1,-5819.72C610.5,-5825.75 628.39,-5835.45 640,-5851 656.17,-5872.66 632.76,-6073.83 650.75,-6094 660.86,-6105.34 675.53,-6110.86 690.54,-6113.19"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="690.18,-6115.27 696.38,-6113.87 690.66,-6111.09 690.18,-6115.27"/>
</g>
<!-- src/server/domain/AppUser/AppUser.ts -->
<g id="node172" class="node">
<title>src/server/domain/AppUser/AppUser.ts</title>
<g id="a_node172"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/domain/AppUser/AppUser.ts" xlink:title="AppUser.ts">
<polygon fill="#dc6b0d" stroke="black" points="1027.38,-6461.25 967.12,-6461.25 967.12,-6442.75 1027.38,-6442.75 1027.38,-6461.25"/>
<text text-anchor="start" x="975.12" y="-6448.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AppUser.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/AppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge275" class="edge">
<title>src/server/application/ports/AppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M804.74,-6101.5C832.79,-6100.05 865.7,-6104.11 885.5,-6126 896.05,-6137.66 882.23,-6396.03 891.12,-6409 906.16,-6430.92 934.5,-6441.68 957.97,-6446.95"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="957.43,-6448.98 963.72,-6448.11 958.26,-6444.86 957.43,-6448.98"/>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts -->
<g id="node170" class="node">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts</title>
<g id="a_node170"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts" xlink:title="UpdateSinglePlayerStatusCommand.ts">
<polygon fill="#dd1c1c" stroke="black" points="840,-5880.25 664.25,-5880.25 664.25,-5861.75 840,-5861.75 840,-5880.25"/>
<text text-anchor="start" x="672.25" y="-5867.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdateSinglePlayerStatusCommand.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts -->
<g id="node171" class="node">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts</title>
<g id="a_node171"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts" xlink:title="UpdateSinglePlayerStatusCommandHandler.ts">
<polygon fill="#dd1c1c" stroke="black" points="624.25,-5880.25 416.25,-5880.25 416.25,-5861.75 624.25,-5861.75 624.25,-5880.25"/>
<text text-anchor="start" x="424.25" y="-5867.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UpdateSinglePlayerStatusCommandHandler.ts</text>
</a>
</g>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge274" class="edge">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M541.45,-5880.53C569.11,-5894.8 617.89,-5924.53 640,-5966 653.43,-5991.19 631.33,-6073.08 650.75,-6094 661.04,-6105.08 675.68,-6110.54 690.62,-6112.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="690.2,-6114.96 696.41,-6113.58 690.7,-6110.79 690.2,-6114.96"/>
</g>
<!-- src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts -->
<g id="edge273" class="edge">
<title>src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler.ts&#45;&gt;src/server/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommand.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M624.42,-5871C634.67,-5871 645.04,-5871 655.21,-5871"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="655.06,-5873.1 661.06,-5871 655.06,-5868.9 655.06,-5873.1"/>
</g>
<!-- src/server/domain/AppUser/valueObjects/AppUserId.ts -->
<g id="node180" class="node">
<title>src/server/domain/AppUser/valueObjects/AppUserId.ts</title>
<g id="a_node180"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/domain/AppUser/valueObjects/AppUserId.ts" xlink:title="AppUserId.ts">
<polygon fill="#dc6b0d" stroke="black" points="1238.62,-6448.25 1170.12,-6448.25 1170.12,-6429.75 1238.62,-6429.75 1238.62,-6448.25"/>
<text text-anchor="start" x="1178.12" y="-6435.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AppUserId.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/AppUser/AppUser.ts&#45;&gt;src/server/domain/AppUser/valueObjects/AppUserId.ts -->
<g id="edge281" class="edge">
<title>src/server/domain/AppUser/AppUser.ts&#45;&gt;src/server/domain/AppUser/valueObjects/AppUserId.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1027.64,-6450.14C1062.58,-6447.92 1121.17,-6444.21 1161.01,-6441.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1161.08,-6443.78 1166.93,-6441.31 1160.81,-6439.59 1161.08,-6443.78"/>
</g>
<!-- src/server/application/ports/AuthenticationGateway.ts -->
<g id="node173" class="node">
<title>src/server/application/ports/AuthenticationGateway.ts</title>
<g id="a_node173"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/ports/AuthenticationGateway.ts" xlink:title="AuthenticationGateway.ts">
<polygon fill="#dd1c1c" stroke="black" points="811.5,-6212.25 692.75,-6212.25 692.75,-6193.75 811.5,-6193.75 811.5,-6212.25"/>
<text text-anchor="start" x="700.75" y="-6199.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">AuthenticationGateway.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/AuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge276" class="edge">
<title>src/server/application/ports/AuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M811.67,-6194.34C838.02,-6193.94 867.24,-6199 885.5,-6219 899.74,-6234.6 879.09,-6391.64 891.12,-6409 906.26,-6430.84 934.6,-6441.61 958.04,-6446.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="957.49,-6448.93 963.78,-6448.07 958.32,-6444.81 957.49,-6448.93"/>
</g>
<!-- src/server/application/ports/CryptoPort.ts -->
<g id="node174" class="node">
<title>src/server/application/ports/CryptoPort.ts</title>
<g id="a_node174"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/ports/CryptoPort.ts" xlink:title="CryptoPort.ts">
<polygon fill="#dd1c1c" stroke="black" points="786.38,-6150.25 717.88,-6150.25 717.88,-6131.75 786.38,-6131.75 786.38,-6150.25"/>
<text text-anchor="start" x="725.88" y="-6137.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">CryptoPort.ts</text>
</a>
</g>
</g>
<!-- src/server/application/ports/IdentityProvider.ts -->
<g id="node175" class="node">
<title>src/server/application/ports/IdentityProvider.ts</title>
<g id="a_node175"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/ports/IdentityProvider.ts" xlink:title="IdentityProvider.ts">
<polygon fill="#dd1c1c" stroke="black" points="796.12,-6181.25 708.12,-6181.25 708.12,-6162.75 796.12,-6162.75 796.12,-6181.25"/>
<text text-anchor="start" x="716.12" y="-6168.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">IdentityProvider.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/MatchmakingQueue/MatchmakingQueue.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge282" class="edge">
<title>src/server/domain/MatchmakingQueue/MatchmakingQueue.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1051.34,-6370C1076.19,-6370 1106.14,-6370 1132.85,-6370"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1132.75,-6372.1 1138.75,-6370 1132.75,-6367.9 1132.75,-6372.1"/>
</g>
<!-- src/server/application/queries/loadDeckBuilderSettingsByGameId.ts -->
<g id="node177" class="node">
<title>src/server/application/queries/loadDeckBuilderSettingsByGameId.ts</title>
<g id="a_node177"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/queries/loadDeckBuilderSettingsByGameId.ts" xlink:title="loadDeckBuilderSettingsByGameId.ts">
<polygon fill="#dd1c1c" stroke="black" points="604.75,-6057.25 435.75,-6057.25 435.75,-6038.75 604.75,-6038.75 604.75,-6057.25"/>
<text text-anchor="start" x="443.75" y="-6044.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadDeckBuilderSettingsByGameId.ts</text>
</a>
</g>
</g>
<!-- src/server/application/queries/loadGameById.ts -->
<g id="node178" class="node">
<title>src/server/application/queries/loadGameById.ts</title>
<g id="a_node178"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/queries/loadGameById.ts" xlink:title="loadGameById.ts">
<polygon fill="#dd1c1c" stroke="black" points="563.12,-6088.25 477.38,-6088.25 477.38,-6069.75 563.12,-6069.75 563.12,-6088.25"/>
<text text-anchor="start" x="485.38" y="-6075.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadGameById.ts</text>
</a>
</g>
</g>
<!-- src/server/application/queries/loadGameSettingsByGameId.ts -->
<g id="node179" class="node">
<title>src/server/application/queries/loadGameSettingsByGameId.ts</title>
<g id="a_node179"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/application/queries/loadGameSettingsByGameId.ts" xlink:title="loadGameSettingsByGameId.ts">
<polygon fill="#dd1c1c" stroke="black" points="592,-6119.25 448.5,-6119.25 448.5,-6100.75 592,-6100.75 592,-6119.25"/>
<text text-anchor="start" x="456.5" y="-6106.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">loadGameSettingsByGameId.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/Deck/errors/DeckNotOwnedError.ts -->
<g id="node181" class="node">
<title>src/server/domain/Deck/errors/DeckNotOwnedError.ts</title>
<g id="a_node181"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/domain/Deck/errors/DeckNotOwnedError.ts" xlink:title="DeckNotOwnedError.ts">
<polygon fill="#dc6b0d" stroke="black" points="1051.75,-6265.25 942.75,-6265.25 942.75,-6246.75 1051.75,-6246.75 1051.75,-6265.25"/>
<text text-anchor="start" x="950.75" y="-6252.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">DeckNotOwnedError.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/User/errors/UserNotAuthenticatedError.ts -->
<g id="node182" class="node">
<title>src/server/domain/User/errors/UserNotAuthenticatedError.ts</title>
<g id="a_node182"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/domain/User/errors/UserNotAuthenticatedError.ts" xlink:title="UserNotAuthenticatedError.ts">
<polygon fill="#dc6b0d" stroke="black" points="1064.5,-6544.25 930,-6544.25 930,-6525.75 1064.5,-6525.75 1064.5,-6544.25"/>
<text text-anchor="start" x="938" y="-6531.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UserNotAuthenticatedError.ts</text>
</a>
</g>
</g>
<!-- src/server/domain/User/errors/UserNotRegisteredError.ts -->
<g id="node183" class="node">
<title>src/server/domain/User/errors/UserNotRegisteredError.ts</title>
<g id="a_node183"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/domain/User/errors/UserNotRegisteredError.ts" xlink:title="UserNotRegisteredError.ts">
<polygon fill="#dc6b0d" stroke="black" points="1058.88,-6575.25 935.62,-6575.25 935.62,-6556.75 1058.88,-6556.75 1058.88,-6575.25"/>
<text text-anchor="start" x="943.62" y="-6562.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UserNotRegisteredError.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts -->
<g id="node184" class="node">
<title>src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts</title>
<g id="a_node184"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts" xlink:title="UuidIdentityProvider.ts">
<polygon fill="#248cea" stroke="black" points="574,-6339.25 466.5,-6339.25 466.5,-6320.75 574,-6320.75 574,-6339.25"/>
<text text-anchor="start" x="474.5" y="-6326.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">UuidIdentityProvider.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/CryptoPort.ts -->
<g id="edge283" class="edge">
<title>src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/CryptoPort.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M525.19,-6320.49C538.12,-6290.64 582.34,-6198.3 650.75,-6157 668.09,-6146.53 690.07,-6142.23 709.04,-6140.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="708.91,-6142.79 714.77,-6140.32 708.65,-6138.6 708.91,-6142.79"/>
</g>
<!-- src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/IdentityProvider.ts -->
<g id="edge284" class="edge">
<title>src/server/infrastructure/IdentityProvider/UuidIdentityProvider.ts&#45;&gt;src/server/application/ports/IdentityProvider.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M526.76,-6320.37C542.36,-6294.1 589.67,-6220.95 650.75,-6188 665.43,-6180.08 682.94,-6175.83 699.21,-6173.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="699.36,-6175.71 705.08,-6172.93 698.88,-6171.54 699.36,-6175.71"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts -->
<g id="node185" class="node">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts</title>
<g id="a_node185"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts" xlink:title="ConvexAuthenticationGateway.ts">
<polygon fill="#248cea" stroke="black" points="595.38,-6530.25 445.12,-6530.25 445.12,-6511.75 595.38,-6511.75 595.38,-6530.25"/>
<text text-anchor="start" x="453.12" y="-6517.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexAuthenticationGateway.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge286" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M588.3,-6511.26C689.38,-6496.58 877.53,-6469.25 958.06,-6457.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="958.25,-6459.64 963.89,-6456.7 957.65,-6455.49 958.25,-6459.64"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/application/ports/AuthenticationGateway.ts -->
<g id="edge285" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/application/ports/AuthenticationGateway.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M595.67,-6522.35C612.53,-6518.94 628.75,-6512 640,-6499 660.36,-6475.45 630.09,-6242.29 650.75,-6219 659.26,-6209.4 671.04,-6203.96 683.54,-6201.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="683.77,-6203.19 689.3,-6200.05 683.02,-6199.06 683.77,-6203.19"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotAuthenticatedError.ts -->
<g id="edge287" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotAuthenticatedError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M595.73,-6523.2C684.33,-6525.81 831.43,-6530.14 920.56,-6532.77"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="920.45,-6534.87 926.51,-6532.94 920.57,-6530.67 920.45,-6534.87"/>
</g>
<!-- src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotRegisteredError.ts -->
<g id="edge288" class="edge">
<title>src/server/infrastructure/gateways/AuthenticationGateway/ConvexAuthenticationGateway.ts&#45;&gt;src/server/domain/User/errors/UserNotRegisteredError.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M595.73,-6528.06C686.35,-6536.64 838.16,-6551.02 926.56,-6559.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="926.25,-6561.48 932.43,-6559.95 926.65,-6557.3 926.25,-6561.48"/>
</g>
<!-- src/server/infrastructure/gateways/Context/ConvexContext.ts -->
<g id="node186" class="node">
<title>src/server/infrastructure/gateways/Context/ConvexContext.ts</title>
<g id="a_node186"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/gateways/Context/ConvexContext.ts" xlink:title="ConvexContext.ts">
<polygon fill="#248cea" stroke="black" points="564.25,-6408.25 476.25,-6408.25 476.25,-6389.75 564.25,-6389.75 564.25,-6408.25"/>
<text text-anchor="start" x="484.25" y="-6395.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexContext.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/gateways/Context/ConvexContext.ts&#45;&gt;src/server/application/ports/Context.ts -->
<g id="edge289" class="edge">
<title>src/server/infrastructure/gateways/Context/ConvexContext.ts&#45;&gt;src/server/application/ports/Context.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M564.59,-6400.17C590.7,-6398.35 622.23,-6391.14 640,-6369 665.53,-6337.18 623.82,-6032.64 650.75,-6002 666.31,-5984.29 692.97,-5980.71 714.9,-5981.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="714.53,-5983.41 720.63,-5981.64 714.75,-5979.22 714.53,-5983.41"/>
</g>
<!-- src/server/infrastructure/gateways/Time/RealTimeService.ts -->
<g id="node187" class="node">
<title>src/server/infrastructure/gateways/Time/RealTimeService.ts</title>
<g id="a_node187"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/gateways/Time/RealTimeService.ts" xlink:title="RealTimeService.ts">
<polygon fill="#248cea" stroke="black" points="568,-6469.25 472.5,-6469.25 472.5,-6450.75 568,-6450.75 568,-6469.25"/>
<text text-anchor="start" x="480.5" y="-6456.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">RealTimeService.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/gateways/Time/RealTimeService.ts&#45;&gt;src/server/application/ports/TimeService.ts -->
<g id="edge290" class="edge">
<title>src/server/infrastructure/gateways/Time/RealTimeService.ts&#45;&gt;src/server/application/ports/TimeService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M568.44,-6464.29C593.57,-6463.77 622.77,-6458.16 640,-6438 669.24,-6403.78 621.08,-6066.85 650.75,-6033 664.06,-6017.81 685.54,-6013 705.22,-6012.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="705.08,-6014.44 711.07,-6012.31 705.06,-6010.24 705.08,-6014.44"/>
</g>
<!-- src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts -->
<g id="node188" class="node">
<title>src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts</title>
<g id="a_node188"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts" xlink:title="ConvexAppUserRepository.ts">
<polygon fill="#248cea" stroke="black" points="588.25,-6902.25 452.25,-6902.25 452.25,-6883.75 588.25,-6883.75 588.25,-6902.25"/>
<text text-anchor="start" x="460.25" y="-6889.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexAppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge291" class="edge">
<title>src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M588.45,-6895.81C607.9,-6893.03 627.29,-6886.06 640,-6871 666.7,-6839.37 623.66,-6157.29 650.75,-6126 660.74,-6114.46 675.45,-6108.89 690.54,-6106.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="690.7,-6108.67 696.42,-6105.91 690.22,-6104.5 690.7,-6108.67"/>
</g>
<!-- src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge292" class="edge">
<title>src/server/infrastructure/repositories/AppUser/ConvexAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M588.59,-6897.86C673.5,-6899.81 816.71,-6888.34 885.5,-6798 895.03,-6785.49 883.24,-6528.6 891.12,-6515 905.73,-6489.81 934.77,-6473.54 958.63,-6463.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="959.15,-6465.91 964,-6461.8 957.64,-6461.99 959.15,-6465.91"/>
</g>
<!-- src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts -->
<g id="node189" class="node">
<title>src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts</title>
<g id="a_node189"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts" xlink:title="ConvexDeckRepository.ts">
<polygon fill="#248cea" stroke="black" points="580.75,-6841.25 459.75,-6841.25 459.75,-6822.75 580.75,-6822.75 580.75,-6841.25"/>
<text text-anchor="start" x="467.75" y="-6828.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexDeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts -->
<g id="edge293" class="edge">
<title>src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.07,-6834.16C602.24,-6831.81 624.6,-6825.29 640,-6810 660.59,-6789.56 732.61,-6348.89 748.15,-6252.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="750.23,-6252.86 749.11,-6246.6 746.08,-6252.19 750.23,-6252.86"/>
</g>
<!-- src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge294" class="edge">
<title>src/server/infrastructure/repositories/Deck/ConvexDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M581.01,-6828.16C600.66,-6825.05 622.06,-6819.57 640,-6810 646.03,-6806.78 645.65,-6803.55 650.75,-6799 749.28,-6711.14 826.16,-6734.93 885.5,-6617 892.17,-6603.75 882.63,-6362.16 891.12,-6350 906.95,-6327.36 937.23,-6317.22 961.24,-6312.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.54,-6314.76 967.11,-6311.7 960.84,-6310.62 961.54,-6314.76"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts -->
<g id="node190" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts</title>
<g id="a_node190"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts" xlink:title="InMemoryAppUserRepository.ts">
<polygon fill="#248cea" stroke="black" points="592.75,-6780.25 447.75,-6780.25 447.75,-6761.75 592.75,-6761.75 592.75,-6780.25"/>
<text text-anchor="start" x="455.75" y="-6767.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryAppUserRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts -->
<g id="edge295" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/application/ports/AppUserRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M593.07,-6776.89C610.81,-6774.56 628.11,-6768.37 640,-6755 663.23,-6728.89 627.84,-6152.39 650.75,-6126 660.76,-6114.47 675.47,-6108.91 690.56,-6106.59"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="690.72,-6108.69 696.44,-6105.93 690.24,-6104.52 690.72,-6108.69"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts -->
<g id="edge296" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryAppUserRepository.ts&#45;&gt;src/server/domain/AppUser/AppUser.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M593,-6767.59C679.74,-6759.12 821.98,-6730.24 885.5,-6633 899.86,-6611.02 877.64,-6537.53 891.12,-6515 906.08,-6490.01 935.09,-6473.74 958.85,-6464"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="959.36,-6466.05 964.19,-6461.92 957.84,-6462.14 959.36,-6466.05"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts -->
<g id="node191" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts</title>
<g id="a_node191"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts" xlink:title="InMemoryDeckRepository.ts">
<polygon fill="#248cea" stroke="black" points="585.25,-6718.25 455.25,-6718.25 455.25,-6699.75 585.25,-6699.75 585.25,-6718.25"/>
<text text-anchor="start" x="463.25" y="-6705.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryDeckRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts -->
<g id="edge297" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/application/ports/DeckRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M585.6,-6714.67C605.47,-6712.99 625.83,-6707.22 640,-6693 652.97,-6679.98 647.18,-6629.03 650.75,-6611 678.46,-6470.91 728.61,-6305.85 745.44,-6251.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="747.4,-6252.75 747.2,-6246.39 743.4,-6251.49 747.4,-6252.75"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts -->
<g id="edge298" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryDeckRepository.ts&#45;&gt;src/server/domain/Deck/Deck.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M585.47,-6714.54C605.33,-6712.85 625.72,-6707.11 640,-6693 663.33,-6669.95 626.63,-6642.21 650.75,-6620 689.2,-6584.59 849.32,-6642.73 885.5,-6605 895.31,-6594.77 883,-6361.61 891.12,-6350 906.96,-6327.37 937.24,-6317.23 961.25,-6312.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.54,-6314.77 967.11,-6311.71 960.85,-6310.62 961.54,-6314.77"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts -->
<g id="node192" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts</title>
<g id="a_node192"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts" xlink:title="InMemoryMatchRepository.ts">
<polygon fill="#248cea" stroke="black" points="587.12,-6687.25 453.38,-6687.25 453.38,-6668.75 587.12,-6668.75 587.12,-6687.25"/>
<text text-anchor="start" x="461.38" y="-6674.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryMatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge299" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M587.49,-6680.92C607.24,-6678.22 627.06,-6671.28 640,-6656 661.26,-6630.9 629.17,-6088.83 650.75,-6064 662.04,-6051.01 679.29,-6045.6 696.32,-6043.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="696.31,-6045.98 702.15,-6043.48 696.03,-6041.79 696.31,-6045.98"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge300" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M587.36,-6679.13C606.52,-6676.21 626.02,-6669.59 640,-6656 656.3,-6640.16 638.87,-6625.38 650.75,-6606 721.03,-6491.32 825.83,-6535.55 885.5,-6415 894.33,-6397.17 880.17,-6252.61 891.12,-6236 906.82,-6212.2 937.32,-6199.73 961.44,-6193.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="961.73,-6195.44 967.06,-6191.98 960.73,-6191.36 961.73,-6195.44"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts -->
<g id="node193" class="node">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts</title>
<g id="a_node193"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts" xlink:title="InMemoryMatchmakingQueueRepository.ts">
<polygon fill="#248cea" stroke="black" points="616,-6749.25 424.5,-6749.25 424.5,-6730.75 616,-6730.75 616,-6749.25"/>
<text text-anchor="start" x="432.5" y="-6736.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">InMemoryMatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge301" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M616.47,-6740.03C625.39,-6736.47 633.5,-6731.31 640,-6724 663.23,-6697.89 627.84,-6121.39 650.75,-6095 655.66,-6089.35 661.69,-6085.13 668.35,-6082.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="668.77,-6084.12 673.59,-6079.98 667.24,-6080.2 668.77,-6084.12"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge303" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M616.26,-6749.32C747.18,-6756.26 979.92,-6746.19 1095.38,-6605 1121.58,-6572.95 1083.04,-6453.61 1105.75,-6419 1117.12,-6401.68 1136.57,-6390.21 1155.13,-6382.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1155.84,-6384.74 1160.72,-6380.68 1154.37,-6380.81 1155.84,-6384.74"/>
</g>
<!-- src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge302" class="edge">
<title>src/server/infrastructure/repositories/InMemory/InMemoryMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M616.27,-6739.08C625.11,-6735.66 633.26,-6730.79 640,-6724 666.82,-6696.97 623.28,-6665.37 650.75,-6639 688.52,-6602.74 849.57,-6657.08 885.5,-6619 893.43,-6610.59 884.47,-6420.46 891.12,-6411 901.49,-6396.27 917.97,-6386.83 934.84,-6380.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="935.19,-6382.87 940.25,-6379.01 933.89,-6378.88 935.19,-6382.87"/>
</g>
<!-- src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts -->
<g id="node194" class="node">
<title>src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts</title>
<g id="a_node194"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts" xlink:title="ConvexMatchRepository.ts">
<polygon fill="#248cea" stroke="black" points="582.62,-6626.25 457.88,-6626.25 457.88,-6607.75 582.62,-6607.75 582.62,-6626.25"/>
<text text-anchor="start" x="465.88" y="-6613.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexMatchRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts -->
<g id="edge304" class="edge">
<title>src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/application/ports/MatchRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M567.53,-6607.33C593.52,-6599.3 623.9,-6584.99 640,-6560 654.93,-6536.83 632.63,-6084.77 650.75,-6064 662.06,-6051.03 679.31,-6045.62 696.35,-6043.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="696.33,-6046 702.18,-6043.51 696.05,-6041.81 696.33,-6046"/>
</g>
<!-- src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts -->
<g id="edge305" class="edge">
<title>src/server/infrastructure/repositories/Match/ConvexMatchRepository.ts&#45;&gt;src/server/domain/Match/Match.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M549.49,-6607.29C574.78,-6597.73 612.08,-6581.46 640,-6560 773.08,-6457.7 815.64,-6425.63 885.5,-6273 892.42,-6257.88 881.15,-6249.31 891.12,-6236 908.03,-6213.42 937.92,-6200.88 961.52,-6194.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="962.02,-6196.19 967.29,-6192.62 960.95,-6192.13 962.02,-6196.19"/>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts -->
<g id="node195" class="node">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts</title>
<g id="a_node195"><a xlink:href="https://github.com/Evyweb/EvyGames/blob/master/src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts" xlink:title="ConvexMatchmakingQueueRepository.ts">
<polygon fill="#248cea" stroke="black" points="611.5,-6963.25 429,-6963.25 429,-6944.75 611.5,-6944.75 611.5,-6963.25"/>
<text text-anchor="start" x="437" y="-6950.7" font-family="Helvetica,sans-Serif" font-size="9.00" fill="white">ConvexMatchmakingQueueRepository.ts</text>
</a>
</g>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts -->
<g id="edge306" class="edge">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/application/ports/MatchmakingQueueRepository.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M611.88,-6951.03C622.67,-6946.97 632.5,-6940.9 640,-6932 669.97,-6896.44 620.34,-6130.18 650.75,-6095 655.64,-6089.34 661.67,-6085.11 668.32,-6082.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="668.74,-6084.1 673.56,-6079.95 667.21,-6080.18 668.74,-6084.1"/>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts -->
<g id="edge308" class="edge">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueueItem.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M594.66,-6944.26C725.69,-6922.34 995.46,-6855.45 1095.38,-6668 1108.4,-6643.56 1090.72,-6442.26 1105.75,-6419 1116.91,-6401.73 1136.12,-6390.3 1154.56,-6382.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1155.24,-6384.85 1160.12,-6380.78 1153.76,-6380.92 1155.24,-6384.85"/>
</g>
<!-- src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts -->
<g id="edge307" class="edge">
<title>src/server/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository.ts&#45;&gt;src/server/domain/MatchmakingQueue/MatchmakingQueue.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M605.83,-6944.26C617.65,-6941.22 629.37,-6937.23 640,-6932 771.48,-6867.36 824.5,-6845.21 885.5,-6712 892.46,-6696.79 881.56,-6424.72 891.12,-6411 901.43,-6396.22 917.89,-6386.77 934.76,-6380.73"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="935.12,-6382.82 940.17,-6378.96 933.82,-6378.83 935.12,-6382.82"/>
</g>
</g>
</svg>
    <script>
      var gMode = new Mode();

var title2ElementMap = (function makeElementMap() {
  /** @type {NodeListOf<SVGGElement>} */
  var nodes = document.querySelectorAll(".node");
  /** @type {NodeListOf<SVGGElement>} */
  var edges = document.querySelectorAll(".edge");
  return new Title2ElementMap(edges, nodes);
})();

function getHoverHandler(pTitle2ElementMap) {
  /** @type {string} */
  var currentHighlightedTitle = "";

  /** @param {MouseEvent} pMouseEvent */
  return function hoverHighlightHandler(pMouseEvent) {
    var closestNodeOrEdge = pMouseEvent.target.closest(".edge, .node");
    var closestTitleText = getTitleText(closestNodeOrEdge);

    if (
      currentHighlightedTitle !== closestTitleText &&
      gMode.get() === gMode.HOVER
    ) {
      resetNodesAndEdges();
      addHighlight(closestNodeOrEdge);
      pTitle2ElementMap.get(closestTitleText).forEach(addHighlight);
      currentHighlightedTitle = closestTitleText;
    }
  };
}

function getSelectHandler(pTitle2ElementMap) {
  /** @type {string} */
  var currentHighlightedTitle = "";

  /** @param {MouseEvent} pMouseEvent */
  return function selectHighlightHandler(pMouseEvent) {
    pMouseEvent.preventDefault();

    var closestNodeOrEdge = pMouseEvent.target.closest(".edge, .node");
    var closestTitleText = getTitleText(closestNodeOrEdge);

    if (closestNodeOrEdge) {
      gMode.setToSelect();
    } else {
      gMode.setToHover();
    }
    if (currentHighlightedTitle !== closestTitleText) {
      resetNodesAndEdges();
      addHighlight(closestNodeOrEdge);
      pTitle2ElementMap.get(closestTitleText).forEach(addHighlight);
      currentHighlightedTitle = closestTitleText;
    }
  };
}
function Mode() {
  var HOVER = 1;
  var SELECT = 2;

  function setToHover() {
    this._mode = HOVER;
  }
  function setToSelect() {
    this._mode = SELECT;
  }

  /**
   * @returns {number}
   */
  function get() {
    return this._mode || HOVER;
  }

  return {
    HOVER: HOVER,
    SELECT: SELECT,
    setToHover: setToHover,
    setToSelect: setToSelect,
    get: get,
  };
}

/**
 *
 * @param {SVGGelement[]} pEdges
 * @param {SVGGElement[]} pNodes
 * @return {{get: (pTitleText:string) => SVGGElement[]}}
 */
function Title2ElementMap(pEdges, pNodes) {
  /* {{[key: string]: SVGGElement[]}} */
  var elementMap = buildMap(pEdges, pNodes);

  /**
   * @param {NodeListOf<SVGGElement>} pEdges
   * @param {NodeListOf<SVGGElement>} pNodes
   * @return {{[key: string]: SVGGElement[]}}
   */
  function buildMap(pEdges, pNodes) {
    var title2NodeMap = buildTitle2NodeMap(pNodes);

    return nodeListToArray(pEdges).reduce(addEdgeToMap(title2NodeMap), {});
  }
  /**
   * @param {NodeListOf<SVGGElement>} pNodes
   * @return {{[key: string]: SVGGElement}}
   */
  function buildTitle2NodeMap(pNodes) {
    return nodeListToArray(pNodes).reduce(addNodeToMap, {});
  }

  function addNodeToMap(pMap, pNode) {
    var titleText = getTitleText(pNode);

    if (titleText) {
      pMap[titleText] = pNode;
    }
    return pMap;
  }

  function addEdgeToMap(pNodeMap) {
    return function (pEdgeMap, pEdge) {
      /** @type {string} */
      var titleText = getTitleText(pEdge);

      if (titleText) {
        var edge = pryEdgeFromTitle(titleText);

        pEdgeMap[titleText] = [pNodeMap[edge.from], pNodeMap[edge.to]];
        (pEdgeMap[edge.from] || (pEdgeMap[edge.from] = [])).push(pEdge);
        (pEdgeMap[edge.to] || (pEdgeMap[edge.to] = [])).push(pEdge);
      }
      return pEdgeMap;
    };
  }

  /**
   *
   * @param {string} pString
   * @return {{from?: string; to?:string;}}
   */
  function pryEdgeFromTitle(pString) {
    var nodeNames = pString.split(/\s*->\s*/);

    return {
      from: nodeNames.shift(),
      to: nodeNames.shift(),
    };
  }
  /**
   *
   * @param {string} pTitleText
   * @return {SVGGElement[]}
   */
  function get(pTitleText) {
    return (pTitleText && elementMap[pTitleText]) || [];
  }
  return {
    get: get,
  };
}

/**
 * @param {SVGGElement} pGElement
 * @return {string?}
 */
function getTitleText(pGElement) {
  /** @type {SVGTitleElement} */
  var title = pGElement && pGElement.querySelector("title");
  /** @type {string} */
  var titleText = title && title.textContent;

  if (titleText) {
    titleText = titleText.trim();
  }
  return titleText;
}

/**
 * @param {NodeListOf<Element>} pNodeList
 * @return {Element[]}
 */
function nodeListToArray(pNodeList) {
  var lReturnValue = [];

  pNodeList.forEach(function (pElement) {
    lReturnValue.push(pElement);
  });

  return lReturnValue;
}

function resetNodesAndEdges() {
  nodeListToArray(document.querySelectorAll(".current")).forEach(
    removeHighlight,
  );
}

/**
 * @param {SVGGElement} pGElement
 */
function removeHighlight(pGElement) {
  if (pGElement && pGElement.classList) {
    pGElement.classList.remove("current");
  }
}

/**
 * @param {SVGGElement} pGroup
 */
function addHighlight(pGroup) {
  if (pGroup && pGroup.classList) {
    pGroup.classList.add("current");
  }
}

var gHints = {
  HIDDEN: 1,
  SHOWN: 2,
  state: 1, // === HIDDEN
  show: function () {
    document.getElementById("hints").removeAttribute("style");
    gHints.state = gHints.SHOWN;
  },
  hide: function () {
    document.getElementById("hints").style = "display:none";
    gHints.state = gHints.HIDDEN;
  },
  toggle: function () {
    if ((gHints.state || gHints.HIDDEN) === gHints.HIDDEN) {
      gHints.show();
    } else {
      gHints.hide();
    }
  },
};

/** @param {KeyboardEvent} pKeyboardEvent */
function keyboardEventHandler(pKeyboardEvent) {
  if (pKeyboardEvent.key === "Escape") {
    resetNodesAndEdges();
    gMode.setToHover();
    gHints.hide();
  }
  if (pKeyboardEvent.key === "F1") {
    pKeyboardEvent.preventDefault();
    gHints.toggle();
  }
}

document.addEventListener("contextmenu", getSelectHandler(title2ElementMap));
document.addEventListener("mouseover", getHoverHandler(title2ElementMap));
document.addEventListener("keydown", keyboardEventHandler);
document.getElementById("close-hints").addEventListener("click", gHints.hide);
document.getElementById("button_help").addEventListener("click", gHints.toggle);
document.querySelector("svg").insertAdjacentHTML(
  "afterbegin",
  `<linearGradient id="edgeGradient">
      <stop offset="0%" stop-color="fuchsia"/>
      <stop offset="100%" stop-color="purple"/>
   </linearGradient>
  `,
);

// Add a small increment to the last value of the path to make gradients on
// horizontal paths work. Without them all browsers I tested with (firefox,
// chrome) do not render the gradient, but instead make the line transparent
// (or the color of the background, I haven't looked into it that deeply,
// but for the hack it doesn't matter which).
function skewLineABit(lDrawingInstructions) {
  var lLastValue = lDrawingInstructions.match(/(\d+\.?\d*)$/)[0];
  // Smaller values than .001 _should_ work as well, but don't in all
  // cases. Even this value is so small that it is not visible to the
  // human eye (tested with the two I have at my disposal).
  var lIncrement = 0.001;
  var lNewLastValue = parseFloat(lLastValue) + lIncrement;

  return lDrawingInstructions.replace(lLastValue, lNewLastValue);
}

nodeListToArray(document.querySelectorAll("path"))
  .filter(function (pElement) {
    return pElement.parentElement.classList.contains("edge");
  })
  .forEach(function (pElement) {
    pElement.attributes.d.value = skewLineABit(pElement.attributes.d.value);
  });

    </script>
  </body>
</html>
