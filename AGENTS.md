# Repository Instructions

## Important
- Run `npm install` before starting to work and wait for it to complete (be patient as it can take a while). 
- If packages install is not working, stop the task immediately and inform the user.

## General Workflow
- Follow a **Test-Driven Development** approach.
- After each iteration or change, run `npm run lint` and `npm test`.
- Both commands must succeed before committing.
- Keep commits focused and describe the functional change.

## Testing Guidelines
- Tests use **Vitest** with jest-extended matchers.
- Use `npm test` to run them.
- Structure tests with the sections:
  ```ts
  // Arrange
  // Act
  // Assert
  ```
- Only perform **one** action in the `// Act` section.
- Use sociable tests that exercise use cases (command or query handlers).
- These act as acceptance tests; the domain is never tested directly.
- Avoid overly technical tests and solitary unit tests.
- Do **not** dispatch multiple Redux actions in one test; setup must be manual.
- `describe` blocks should use `describe("When ...")` to give context.
- Each `it` statement must start with `should` and stay close to the functional behaviour.
- Never leave `.only` or `.skip` in committed tests.

## Project Conventions
- The project follows **Clean Architecture** boundaries:
  - `src/client` and `src/server` are split by feature.
  - Domain modules must not depend on application, infrastructure or presentation code.
  - Application modules must not depend on infrastructure code.
- Path alias `@/*` points to the repository root.
- Internationalization currently supports locales `en` and `fr`; update both when adding strings.
- Use Radix UI components and Next.js 15 conventions when adding front-end code.

## Tools
- Linting: `npm run lint` (TypeScript checking + ESLint).
- Testing: `npm test` (Vitest in jsdom environment).
- Coverage: `npm run test:coverage` (optional).

## Commit Requirements
- Keep the working directory clean before committing.
- Include relevant tests for new features or fixes.
- Ensure `npm run lint` and `npm test` pass before every commit.
