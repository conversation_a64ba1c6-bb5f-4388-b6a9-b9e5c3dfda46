{"name": "cleaner-code", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:all": "concurrently \"npm run dev\" \"npm run convex:dev\"", "build": "next build", "start": "next start", "lint": "tsc --noEmit && biome check app/ src/ convex/", "lint:fix": "tsc --noEmit && biome check --fix --unsafe app/ src/ convex/", "format": "biome format --write app/ src/ convex/", "deps:all": "npm run deps:screenshot && npm run deps:graph && npm run deps:github && npm run deps:server && npm run deps:client", "deps:github": "node tools/github.js", "deps:graph": "npx depcruise . --include-only \"^src|^app\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/index.html", "deps:screenshot": "npx depcruise . --include-only \"^src|^app\" --output-type dot | dot -T svg > assets/deps.svg", "deps:server": "npx depcruise . --include-only \"^src/server|^app\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/server-dependency-graph.html", "deps:client": "npx depcruise . --include-only \"^src/client|^app\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/client-dependency-graph.html", "test": "vitest run", "test:coverage": "vitest run --coverage", "convex:dev": "convex dev", "convex:deploy": "convex deploy", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "mcp": "npx tsx src/server/infrastructure/mcp/server.ts"}, "dependencies": {"@auth/core": "^0.37.4", "@convex-dev/auth": "^0.0.81", "@convex-dev/sharded-counter": "^0.1.7", "@evyweb/ioctopus": "^1.2.0", "@evyweb/simple-ddd-toolkit": "^0.20.0", "@formkit/auto-animate": "^0.8.2", "@mdxeditor/editor": "^3.29.1", "@modelcontextprotocol/sdk": "^1.11.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/themes": "^3.2.1", "@tanstack/react-form": "^1.1.2", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@uiw/codemirror-theme-github": "^4.23.10", "@uiw/react-codemirror": "^4.23.10", "auto-text-size": "^0.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.23.0", "convex-helpers": "^0.1.83", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "fastmcp": "^1.27.4", "framer-motion": "^11.18.2", "isomorphic.js": "^0.2.5", "lib0": "^0.2.104", "lodash-es": "^4.17.21", "lucide-react": "^0.488.0", "minimatch": "^10.0.1", "monaco-editor": "^0.52.2", "motion": "^12.6.2", "next": "15.3.0", "next-intl": "^3.26.5", "next-themes": "^0.4.6", "react": "19.1.0", "react-day-picker": "^9.6.3", "react-dom": "19.1.0", "react-markdown": "^9.1.0", "react-monaco-editor": "^0.58.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "simple-git": "^3.27.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "yjs": "^13.6.24", "zod": "^3.24.3"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@edge-runtime/vm": "^5.0.0", "@shikijs/transformers": "^1.29.2", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/nextjs": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/test": "^8.6.12", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/lodash": "^4.17.16", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.13", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.0.9", "cheerio": "^1.0.0", "concurrently": "^9.1.2", "convex-test": "^0.0.36", "dependency-cruiser": "^16.9.0", "eslint-plugin-storybook": "^0.12.0", "jest-extended": "^4.0.2", "jsdom": "^26.0.0", "msw": "^2.7.3", "postcss": "^8.5.3", "shiki": "^1.29.2", "storybook": "^8.6.12", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.9", "vitest-mock-extended": "^2.0.2"}, "volta": {"node": "22.13.1", "npm": "11.0.0"}, "overrides": {"@types/react": "19.1.2", "@types/react-dom": "19.1.2"}}