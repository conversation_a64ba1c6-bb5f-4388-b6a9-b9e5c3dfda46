{"name": "@evyweb/git-repo-to-gpt", "version": "1.0.0", "description": "Creates summary of your git repository", "main": "dist/cli.js", "module": "dist/cli.mjs", "types": "dist/cli.d.ts", "files": ["dist/"], "bin": {"git-repo-to-gpt": "./dist/cli.js"}, "scripts": {"build": "tsup src/cli.ts --format cjs,esm --dts", "lint": "tsc --noEmit", "start": "npm run build && node dist/cli.js", "start2": "tsup src/cli2.ts --format cjs,esm --dts && node dist/cli2.js", "start3": "tsup src/cli3.ts --format cjs,esm --dts && node dist/cli3.js", "test": "vitest run", "test:coverage": "vitest run --coverage"}, "keywords": ["typescript", "tsup", "vitest", "changesets"], "author": "Evyweb", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Evyweb/git-repo-to-gpt.git"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "^22.10.2", "@vitest/coverage-v8": "^2.1.8", "ts-node": "^10.9.2", "tsup": "^8.3.5", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.1.8"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.28.1"}, "dependencies": {"@inquirer/prompts": "^7.2.0", "fs-extra": "^11.2.0", "pg-boss": "^10.1.5", "simple-git": "^3.27.0"}}