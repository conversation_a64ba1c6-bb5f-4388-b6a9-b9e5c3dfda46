import {ScrapGitRepoUseCase} from "@/application/usecases/ScrapGitRepoUseCase";
import {GitAdapter} from "@/infrastructure/GitAdapter";
import {FileSystemAdapter} from "@/infrastructure/FileSystemAdapter";
import {InMemoryGitRepoRepository} from "@/infrastructure/InMemoryGitRepoRepository";
import {ScrapingProcessPresenter} from "@/presentation/ScrapingProcessPresenter";
import path from "path";

export async function main() {

    const gitAdapter = new GitAdapter();
    const fileSystemAdapter = new FileSystemAdapter(24 * 1024 * 1024);
    const gitRepoRepository = new InMemoryGitRepoRepository();
    const presenter = new ScrapingProcessPresenter(console);

    const useCase = new ScrapGitRepoUseCase(
        gitAdapter,
        fileSystemAdapter,
        presenter,
        gitRepoRepository
    );

    await useCase.execute({
        repositoryUrl: 'F:\\Workspaces\\EvyGames',
        destination: path.resolve('F:\\Workspaces\\EvyGames\\.references'),
        folders: ['app', 'convex', 'src']
    });
}

main().catch(console.error);
