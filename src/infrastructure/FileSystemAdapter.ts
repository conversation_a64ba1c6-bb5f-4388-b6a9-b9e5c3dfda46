import {IFileSystemService} from "@/application/ports/IFileSystemService";
import fs from "fs-extra";

export class FileSystemAdapter implements IFileSystemService {
    private readonly maxSplitSize: number;

    private readonly MAX_SPLIT_SIZE_IN_MO = 24;

    constructor(maxSplitSize: number = this.MAX_SPLIT_SIZE_IN_MO * 1024 * 1024) {
        this.maxSplitSize = maxSplitSize;
    }

    async writeFile(filePath: string, content: string): Promise<void> {
        await fs.outputFile(filePath, content);
        await new Promise((resolve) => setTimeout(resolve, 10));
    }

    async readFile(filePath: string): Promise<string> {
        return fs.readFile(filePath, "utf8");
    }

    splitContent(content: string, maxSize?: number): string[] {
        const size = maxSize || this.maxSplitSize;
        const chunks: string[] = [];
        let currentChunk = "";
        let currentSize = 0;

        for (const char of content) {
            const charSize = Buffer.byteLength(char, "utf8");
            if (currentSize + charSize > size) {
                chunks.push(currentChunk);
                currentChunk = "";
                currentSize = 0;
            }
            currentChunk += char;
            currentSize += charSize;
        }

        if (currentChunk) {
            chunks.push(currentChunk);
        }

        return chunks;
    }

    async deleteDirectory(destination: string): Promise<void> {
        return fs.remove(destination);
    }

}
