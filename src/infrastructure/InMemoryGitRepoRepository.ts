import { IGitRepoRepository } from "@/application/ports/IGitRepoRepository";
import { GitRepo } from "@/domain/GitRepo";

export class InMemoryGitRepoRepository implements IGitRepoRepository {
    private repos: GitRepo[] = [];

    save(repo: GitRepo): void {
        this.repos.push(repo);
    }

    findByUrl(url: string): GitRepo | undefined {
        return this.repos.find(repo => repo.url === url);
    }
}
