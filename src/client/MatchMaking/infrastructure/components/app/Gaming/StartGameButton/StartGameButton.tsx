'use client';

import {useMutation, useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";
import {<PERSON>ton, Card, Flex, Grid, Heading} from "@radix-ui/themes";
import {Id} from "@/convex/_generated/dataModel";
import {useEffect, useState} from "react";
import {useRouter} from "next/navigation";
import {useLocale} from "@/src/client/Shared/infrastructure/hooks/useLocale/useLocale";
import {buildMatchUrl} from "@/src/client/Shared/infrastructure/builders/urlBuilder";
import {CheckIcon} from "lucide-react";

type Props = {
  gameId: string;
};

export const StartGameButton = ({gameId}: Props) => {
  const addPlayerToMatchMakingQueue = useMutation(api.mutations.addPlayerToMatchMakingQueue.endpoint);
  const cancelMatchRegistration = useMutation(api.mutations.cancelMatchRegistration.endpoint);
  const router = useRouter();
  const locale = useLocale();

  const matchCreated = useQuery(api.queries.onMatchCreated.endpoint, {gameId: gameId as Id<"games">});
  const decks = useQuery(api.queries.loadDecksByUserIdAndGameId.endpoint, {gameId: gameId as Id<"games">});

  const [loading, setLoading] = useState(false);
  const [deckId, setDeckId] = useState<string | null>(null);

  const handleStart = async () => {
    await addPlayerToMatchMakingQueue({gameId, deckId: deckId!});
    setLoading(true);
  };

  const handleCancel = async () => {
    await cancelMatchRegistration({gameId});
    setLoading(false);
  }

  useEffect(() => {
    if (matchCreated) {
      router.push(buildMatchUrl(locale, matchCreated.payload.matchId));
    }
  }, [matchCreated, router, locale, gameId]);

  return (
    <>
      {!matchCreated && (
        <>
          {loading && (
            <Flex direction="column" gap="8" align="center" justify="center" mt="8">
              <Heading size="8">Searching for an opponent...</Heading>
              <Button size="3" onClick={handleCancel}>Cancel</Button>
            </Flex>
          )}
          {!loading && (
            <Flex direction="column" gap="3">
              <Heading size="5">Select a deck</Heading>
              <Grid columns="6" gap="3">
                {decks?.map((deck) => (
                  <Card key={deck._id} size="2">
                    <Flex direction="column" gap="3" align="center" justify="center">
                      <Heading size="4">
                        {deck.name}
                      </Heading>
                      <Button size="2" onClick={() => setDeckId(deck._id)}>Select Deck {deck._id === deckId ? <CheckIcon /> : ""}</Button>
                    </Flex>
                  </Card>
                ))}
              </Grid>

              <Button disabled={!deckId} size="3" onClick={handleStart}>Start</Button>
            </Flex>
          )}
        </>
      )}
    </>
  );
};
