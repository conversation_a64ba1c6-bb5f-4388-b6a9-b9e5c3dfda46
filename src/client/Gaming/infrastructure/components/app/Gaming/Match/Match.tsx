'use client';

import {FC, useEffect} from "react";
import {useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";
import {Id} from "@/convex/_generated/dataModel";
import {useRouter} from "next/navigation";
import {buildGameUrl} from "@/src/client/Shared/infrastructure/builders/urlBuilder";

type Props = {
  locale: string;
  matchId: Id<"matches">;
  match: {
    players: string[];
    status: string;
    gameId: Id<"games">;
  };
};

const Match: FC<Props> = ({locale, matchId, match}) => {
  const matchEnded = useQuery(api.queries.match.onMatchEnded.endpoint, {matchId});
  const router = useRouter();

  useEffect(() => {
    if(matchEnded) {
      router.push(buildGameUrl(locale, match.gameId));
    }
  }, [matchEnded, router, locale, match.gameId]);

  return (
    <div>
      <p>Match ID: {matchId}</p>
      <p>Players: {match?.players.join(" vs ")}</p>
      <p>Status: {match?.status}</p>
    </div>
  );
};

export default Match;