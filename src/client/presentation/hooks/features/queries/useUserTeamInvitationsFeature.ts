'use client';

import { api } from '@/convex/_generated/api';
import { useQuery } from 'convex/react';

export function useUserTeamInvitationsFeature() {
  const invitations = useQuery(api.queries.lobby.getUserTeamInvitations.endpoint);

  return {
    isLoading: invitations === undefined,
    invitations: invitations || [],
    hasInvitations: invitations && invitations.length > 0,
    noInvitationsFound: invitations && invitations.length === 0,
  };
}
