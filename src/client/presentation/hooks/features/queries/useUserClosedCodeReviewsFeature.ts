import { api } from '@/convex/_generated/api';
import { useQuery } from 'convex/react';

export const useUserClosedCodeReviewsFeature = () => {
  const codeReviews = useQuery(api.queries.lobby.getUserCodeReviewsViewModel.endpoint, { status: 'close' });

  return {
    codeReviews: codeReviews || [],
    totalCodeReviews: codeReviews?.length || 0,
    noCodeReviewsFound: codeReviews && codeReviews.length === 0,
    isLoading: !codeReviews,
    codeReviewsExist: codeReviews && codeReviews.length > 0,
  };
};
