import { api } from '@/convex/_generated/api';
import { useQuery } from 'convex/react';

export const useUserReadyCodeReviewsFeature = () => {
  const codeReviews = useQuery(api.queries.lobby.getUserCodeReviewsViewModel.endpoint, { status: 'new' });

  return {
    codeReviews: codeReviews || [],
    totalCodeReviews: codeReviews?.length || 0,
    noCodeReviewsFound: codeReviews && codeReviews.length === 0,
    isLoading: !codeReviews,
    codeReviewsExist: codeReviews && codeReviews.length > 0,
  };
};
