import { api } from '@/convex/_generated/api';
import { usePaginatedQuery } from 'convex/react';

export const usePrivateCodeReviewsFeature = (totalCodeReviewsToDisplay: number) => {
  const { results, status, loadMore, isLoading } = usePaginatedQuery(
    api.queries.lobby.getPrivateCodeReviews.endpoint,
    {},
    { initialNumItems: totalCodeReviewsToDisplay }
  );

  return {
    action: {
      loadMorePrivateCodeReviews: loadMore,
    },
    viewModel: {
      codeReviews: results,
      noCodeReviewsFound: results.length === 0 && status === 'Exhausted',
      shouldDisplayLoadMoreButton: status !== 'Exhausted' && results.length > 0,
      isLoadingFirstPage: status === 'LoadingFirstPage',
      shouldDisableLoadMoreButton: status !== 'CanLoadMore',
      isLoading,
    },
  };
};
