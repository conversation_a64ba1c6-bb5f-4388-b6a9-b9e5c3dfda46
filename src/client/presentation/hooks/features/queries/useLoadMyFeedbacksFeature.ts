import { api } from '@/convex/_generated/api';
import { usePaginatedQuery } from 'convex/react';

export const useLoadMyFeedbacksFeature = (totalFeedbacks: number) => {
  const {
    results: feedbacks,
    status,
    loadMore,
    isLoading,
  } = usePaginatedQuery(api.queries.userFeedbacks.endpoint, {}, { initialNumItems: totalFeedbacks });

  return {
    actions: {
      loadMore,
    },
    viewModel: {
      feedbacks,
      status,
      isLoading,
      isLoadingFirstPage: status === 'LoadingFirstPage',
      containsFeedbacks: feedbacks && feedbacks.length > 0,
      noFeedbackFound: feedbacks && feedbacks.length === 0 && status === 'Exhausted',
      shouldDisplayLoadMoreButton: status !== 'LoadingFirstPage' && status !== 'Exhausted',
    },
  };
};
