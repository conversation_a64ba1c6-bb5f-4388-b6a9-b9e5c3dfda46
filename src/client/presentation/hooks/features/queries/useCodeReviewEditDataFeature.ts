import { api } from '@/convex/_generated/api';
import { useQuery } from 'convex/react';

export const useCodeReviewEditDataFeature = (codeReviewId: string) => {
  const codeReview = useQuery(api.queries.codeReviewEdit.endpoint, { codeReviewId });

  return {
    viewModel: {
      isLoading: !codeReview,
      isLoaded: !!codeReview,
      hasCodeSnippets: codeReview && codeReview.codeSnippets?.length > 0,
      codeReview: {
        title: codeReview?.title || '',
      },
      codeSnippets: codeReview?.codeSnippets || [],
    },
  };
};
