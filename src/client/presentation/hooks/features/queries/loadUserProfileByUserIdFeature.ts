import { api } from '@/convex/_generated/api';
import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { fetchQuery } from 'convex/nextjs';

export async function loadUserProfileByUserIdFeature(userId: string) {
  const userProfile = await fetchQuery(
    api.queries.getUserProfileByUserId.endpoint,
    { userId },
    { token: await convexAuthNextjsToken() }
  );

  if (!userProfile) {
    throw new Error('User not found');
  }

  return {
    userProfile,
  };
}
