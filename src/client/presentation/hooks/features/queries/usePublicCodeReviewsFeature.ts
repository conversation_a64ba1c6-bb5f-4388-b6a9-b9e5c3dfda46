import { api } from '@/convex/_generated/api';
import { usePaginatedQuery } from 'convex/react';

export const usePublicCodeReviewsFeature = (totalCodeReviewsToDisplay: number) => {
  const { results, status, loadMore, isLoading } = usePaginatedQuery(
    api.queries.lobby.getPublicCodeReviews.endpoint,
    {},
    { initialNumItems: totalCodeReviewsToDisplay }
  );

  return {
    publicCodeReviews: results,
    noPublicCodeReviewsFound: results.length === 0 && status === 'Exhausted',
    shouldDisplayLoadMoreButton: status !== 'Exhausted',
    isLoadingFirstPage: status === 'LoadingFirstPage',
    shouldDisableLoadMoreButton: status !== 'CanLoadMore',
    loadMorePublicCodeReviews: loadMore,
    isLoading,
  };
};
