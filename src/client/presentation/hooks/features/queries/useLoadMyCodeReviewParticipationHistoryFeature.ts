import { api } from '@/convex/_generated/api';
import { useQuery } from 'convex/react';

export const useLoadMyCodeReviewParticipationHistoryFeature = () => {
  const participationHistory = useQuery(api.queries.codeReviewParticipationHistory.endpoint);

  return {
    viewModel: {
      isLoading: !participationHistory,
      participationHistory: participationHistory || [],
      hasNoParticipation: participationHistory && participationHistory.length === 0,
    },
  };
};
