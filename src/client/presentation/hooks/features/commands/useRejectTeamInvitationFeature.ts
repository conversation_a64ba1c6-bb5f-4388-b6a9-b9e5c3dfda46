import { api } from '@/convex/_generated/api';
import { useMutation } from 'convex/react';
import { useToast } from '../../utils/useToast/useToast';

export const useRejectTeamInvitationFeature = () => {
  const rejectTeamInvitationMutation = useMutation(api.mutations.user.rejectTeamInvitation.endpoint);
  const { toast } = useToast();

  const rejectTeamInvitation = async (invitationId: string) => {
    try {
      await rejectTeamInvitationMutation({ invitationId });
      toast({
        title: 'Invitation rejected',
        description: 'The team invitation has been declined.',
        variant: 'success',
      });
      return true;
    } catch (error) {
      let errorMessage = 'Please try again later';

      if (error instanceof Error) {
        if (error.message.includes('teamInvitation.errors.notFound')) {
          errorMessage = 'Invitation not found';
        } else if (error.message.includes('teamInvitation.errors.notPending')) {
          errorMessage = 'This invitation is no longer pending';
        }
      }

      toast({
        title: 'Failed to reject invitation',
        description: errorMessage,
        variant: 'destructive',
      });
      return false;
    }
  };

  return { rejectTeamInvitation };
};
