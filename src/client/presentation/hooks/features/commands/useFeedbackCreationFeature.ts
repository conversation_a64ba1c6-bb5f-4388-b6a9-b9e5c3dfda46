import { api } from '@/convex/_generated/api';
import { useMutation } from 'convex/react';

export const useFeedbackCreationFeature = (snippetId: string) => {
  const postFeedback = useMutation(api.mutations.user.postFeedback.endpoint).withOptimisticUpdate(
    (localStore, feedbackToCreate) => {
      const existingViewModel = localStore.getQuery(api.queries.feedbackList.endpoint, { snippetId });
      const currentUserProfile = localStore.getQuery(api.queries.userProfile.endpoint);

      if (!existingViewModel || !currentUserProfile) return;

      const newFeedback = {
        id: `temp_${Date.now()}`,
        author: {
          name: currentUserProfile.user,
          avatar: currentUserProfile.avatar.image,
          isCurrentUser: false,
        },
        title: feedbackToCreate.title,
        content: feedbackToCreate.content,
        createdAt: Date.now().toString(),
        type: feedbackToCreate.type,
        subType: feedbackToCreate.subType,
      };

      localStore.setQuery(
        api.queries.feedbackList.endpoint,
        { snippetId },
        {
          ...existingViewModel,
          feedbacks: [...existingViewModel.feedbacks, newFeedback],
        }
      );
    }
  );

  return {
    postFeedback,
  };
};
