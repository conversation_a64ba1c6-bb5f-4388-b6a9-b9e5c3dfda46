import { api } from '@/convex/_generated/api';
import { useMutation } from 'convex/react';
import { useToast } from '../../utils/useToast/useToast';

export type CreateTeamRequest = {
  name: string;
};

export const useCreateTeamFeature = () => {
  const createTeamMutation = useMutation(api.mutations.user.createTeam.endpoint).withOptimisticUpdate(
    (localStore, args: CreateTeamRequest) => {
      const userProfile = localStore.getQuery(api.queries.userProfile.endpoint);
      const userTeams = localStore.getQuery(api.queries.lobby.getUserTeams.endpoint);

      if (!userProfile || !userTeams) return;

      const tempId = `temp_${Date.now()}`;

      localStore.setQuery(
        api.queries.lobby.getUserTeams.endpoint,
        {},
        {
          ...userTeams,
          ownedTeams: [
            ...userTeams.ownedTeams,
            {
              teamId: tempId,
              teamName: args.name,
              members: [
                {
                  userId: userProfile.user,
                  name: userProfile.user,
                  avatar: userProfile.avatar.image,
                  avatarFallback: userProfile.user.substring(0, 2).toUpperCase(),
                  isOwner: true,
                },
              ],
            },
          ],
        }
      );
    }
  );

  const { toast } = useToast();

  const createTeam = async (request: CreateTeamRequest): Promise<string> => {
    try {
      return await createTeamMutation(request);
    } catch (error) {
      toast({
        title: 'Failed to create team',
        description: 'Please try again later',
        variant: 'destructive',
      });
      throw error;
    }
  };

  return { createTeam };
};
