import { api } from '@/convex/_generated/api';
import { useMutation } from 'convex/react';
import { useToast } from '../../utils/useToast/useToast';

export type SendTeamInvitationRequest = {
  teamId: string;
  email: string;
};

export const useSendTeamInvitationFeature = () => {
  const sendTeamInvitationMutation = useMutation(api.mutations.user.sendTeamInvitation.endpoint);
  const { toast } = useToast();

  const sendTeamInvitation = async (request: SendTeamInvitationRequest) => {
    try {
      await sendTeamInvitationMutation(request);
      toast({
        title: 'Invitation sent',
        description: 'The invitation has been sent successfully.',
        variant: 'success',
      });
    } catch (error) {
      let errorMessage = 'Please try again later';

      if (error instanceof Error) {
        if (error.message.includes('teamInvitation.errors.alreadyExists')) {
          errorMessage = 'An invitation has already been sent to this email address';
        } else if (error.message.includes('team.errors.notOwner')) {
          errorMessage = 'You must be the team owner to send invitations';
        } else if (error.message.includes('team.errors.notFound')) {
          errorMessage = 'Team not found';
        }
      }

      toast({
        title: 'Failed to send invitation',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  return { sendTeamInvitation };
};
