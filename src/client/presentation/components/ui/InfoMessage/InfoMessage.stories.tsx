import type { Meta, StoryObj } from '@storybook/react';
import InfoMessage from './InfoMessage';

const meta = {
  title: 'Custom/InfoMessage',
  component: InfoMessage,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof InfoMessage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'This is an informational message for the user.',
  },
};

export const LongMessage: Story = {
  args: {
    children:
      'This is a longer informational message that contains more details. It might wrap to multiple lines depending on the container width and the amount of text provided.',
  },
};

export const WithLink: Story = {
  args: {
    children: (
      <>
        This is an informational message with a{' '}
        <a href="http://localhost:3000" className="text-blue-500 underline">
          link
        </a>{' '}
        inside.
      </>
    ),
  },
};
