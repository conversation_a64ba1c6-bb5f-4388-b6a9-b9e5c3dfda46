import type { Meta, StoryObj } from '@storybook/react';
import ShiningButton from './ShiningButton';

const meta = {
  title: 'Custom/ShiningButton',
  component: ShiningButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['1', '2', '3', '4'],
    },
    variant: {
      control: 'select',
      options: ['solid', 'outline', 'ghost', 'soft'],
    },
    color: {
      control: 'select',
      options: ['indigo', 'orange', 'blue', 'green', 'red'],
    },
  },
} satisfies Meta<typeof ShiningButton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Shining Button',
    size: '2',
    variant: 'solid',
    color: 'indigo',
  },
};

export const Small: Story = {
  args: {
    children: 'Small Button',
    size: '1',
    variant: 'solid',
    color: 'indigo',
  },
};

export const Large: Story = {
  args: {
    children: 'Large Button',
    size: '3',
    variant: 'solid',
    color: 'indigo',
  },
};

export const Outline: Story = {
  args: {
    children: 'Outline Button',
    size: '2',
    variant: 'outline',
    color: 'indigo',
  },
};

export const Ghost: Story = {
  args: {
    children: 'Ghost Button',
    size: '2',
    variant: 'ghost',
    color: 'indigo',
  },
};

export const Orange: Story = {
  args: {
    children: 'Orange Button',
    size: '2',
    variant: 'solid',
    color: 'orange',
  },
};
