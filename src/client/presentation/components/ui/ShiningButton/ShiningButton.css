.button-shine-effect {
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
  transition: all 200ms ease;
}

.button-shine-effect::before {
  content: "";
  position: absolute;
  top: 0;
  left: -200%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-30deg);
  pointer-events: none;
}

.button-shine-effect:hover {
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.button-shine-effect.animate::before {
  animation: buttonShine 0.5s forwards;
}

@keyframes buttonShine {
  to {
    left: 200%;
  }
}
