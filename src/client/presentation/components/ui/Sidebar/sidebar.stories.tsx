import type { <PERSON>a, StoryObj } from '@storybook/react';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarProvider,
} from './sidebar';

const meta = {
  title: 'UI/Sidebar',
  component: Sidebar,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div style={{ height: '500px', display: 'flex' }}>
        <SidebarProvider>
          <Story />
        </SidebarProvider>
      </div>
    ),
  ],
} satisfies Meta<typeof Sidebar>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <Sidebar side="left">
      <SidebarHeader>
        <h2 className="text-xl font-bold">My App</h2>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          <SidebarMenuItem>Home</SidebarMenuItem>
          <SidebarMenuItem>Documents</SidebarMenuItem>
          <SidebarMenuItem>Messages</SidebarMenuItem>
          <SidebarMenuItem>Profile</SidebarMenuItem>
          <SidebarMenuItem>Settings</SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <div className="p-2 text-sm text-muted-foreground">Version 1.0.0</div>
      </SidebarFooter>
    </Sidebar>
  ),
};

export const WithGroups: Story = {
  render: () => (
    <Sidebar side="left">
      <SidebarHeader>
        <h2 className="text-xl font-bold">My App</h2>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Main</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>Home</SidebarMenuItem>
              <SidebarMenuItem>Documents</SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarGroupLabel>Account</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>Profile</SidebarMenuItem>
              <SidebarMenuItem>Settings</SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <div className="p-2 text-sm text-muted-foreground">Version 1.0.0</div>
      </SidebarFooter>
    </Sidebar>
  ),
};
