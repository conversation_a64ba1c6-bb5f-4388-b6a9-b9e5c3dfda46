'use client';

import { motion, useAnimation } from 'framer-motion';
import { useCallback } from 'react';

const shakeKeyframes = {
  x: [0, -3, 3, -3, 3, 0],
  transition: { duration: 0.2 },
};

export function ShakeCard({ children }: { children: React.ReactNode }) {
  const controls = useAnimation();

  const handleClick = useCallback(() => {
    controls.start(shakeKeyframes);
  }, [controls]);

  return (
    <motion.div animate={controls} onClick={handleClick}>
      {children}
    </motion.div>
  );
}
