import { Card, Text } from '@radix-ui/themes';
import type { Meta, StoryObj } from '@storybook/react';
import { ShakeCard } from './ShakeCard';

const meta = {
  title: 'Custom/ShakeCard',
  component: ShakeCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof ShakeCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: (
      <Card className="p-4 bg-[#11111160] w-[250px]">
        <Text size="3" weight="bold">
          Shake Card
        </Text>
        <Text size="2">Click me to see the shake animation!</Text>
      </Card>
    ),
  },
};

export const WithImage: Story = {
  args: {
    children: (
      <Card className="p-4 bg-[#11111160] w-[250px]">
        <div className="mb-3 h-[100px] bg-gray-700 rounded-md flex items-center justify-center">
          <Text size="5">🖼️</Text>
        </div>
        <Text size="3" weight="bold">
          Image Card
        </Text>
        <Text size="2">Click me to shake the image card!</Text>
      </Card>
    ),
  },
};
