import { Card, Text } from '@radix-ui/themes';
import type { Meta, StoryObj } from '@storybook/react';
import CardGrid from './CardGrid';

const meta = {
  title: 'Custom/CardGrid',
  component: CardGrid,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CardGrid>;

export default meta;
type Story = StoryObj<typeof meta>;

// Helper function to create a sample card
const SampleCard = ({ index }: { index: number }) => (
  <Card className="p-4 bg-[#11111160]">
    <Text size="3" weight="bold">
      Card {index}
    </Text>
    <Text size="2">This is a sample card for the grid.</Text>
  </Card>
);

export const Default: Story = {
  render: () => (
    <div className="w-full">
      <CardGrid>
        <SampleCard key="card-1" index={1} />
        <SampleCard key="card-2" index={2} />
        <SampleCard key="card-3" index={3} />
        <SampleCard key="card-4" index={4} />
        <SampleCard key="card-5" index={5} />
        <SampleCard key="card-6" index={6} />
      </CardGrid>
    </div>
  ),
};

export const ManyCards: Story = {
  render: () => (
    <div className="w-full">
      <CardGrid>
        <SampleCard key="card-1" index={1} />
        <SampleCard key="card-2" index={2} />
        <SampleCard key="card-3" index={3} />
        <SampleCard key="card-4" index={4} />
        <SampleCard key="card-5" index={5} />
        <SampleCard key="card-6" index={6} />
        <SampleCard key="card-7" index={7} />
        <SampleCard key="card-8" index={8} />
        <SampleCard key="card-9" index={9} />
        <SampleCard key="card-10" index={10} />
        <SampleCard key="card-11" index={11} />
        <SampleCard key="card-12" index={12} />
      </CardGrid>
    </div>
  ),
};
