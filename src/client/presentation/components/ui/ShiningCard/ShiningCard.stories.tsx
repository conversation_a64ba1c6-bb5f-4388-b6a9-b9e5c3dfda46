import { Text } from '@radix-ui/themes';
import type { Meta, StoryObj } from '@storybook/react';
import ShiningCard from './ShiningCard';

const meta = {
  title: 'Custom/ShiningCard',
  component: ShiningCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['1', '2', '3'],
    },
  },
} satisfies Meta<typeof ShiningCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: (
      <div className="p-4">
        <Text size="5" weight="bold">
          Shining Card
        </Text>
        <Text size="2">This is a card with a shining effect.</Text>
      </div>
    ),
    size: '2',
  },
};

export const Small: Story = {
  args: {
    children: (
      <div className="p-3">
        <Text size="3" weight="bold">
          Small Card
        </Text>
        <Text size="1">A smaller card with content.</Text>
      </div>
    ),
    size: '1',
  },
};

export const Large: Story = {
  args: {
    children: (
      <div className="p-6">
        <Text size="6" weight="bold">
          Large Card
        </Text>
        <Text size="3">A larger card with more content and details to display.</Text>
        <Text size="2" className="mt-2">
          Additional information can be placed here.
        </Text>
      </div>
    ),
    size: '3',
  },
};
