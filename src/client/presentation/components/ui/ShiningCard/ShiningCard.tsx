'use client';

import { Card } from '@radix-ui/themes';
import { type ComponentPropsWithoutRef, forwardRef } from 'react';
import './ShiningCard.css';
import { cn } from '@/src/client/presentation/lib/utils';

type ShiningCardProps = ComponentPropsWithoutRef<typeof Card> & {
  /**
   * Disable the scaling effect on hover
   * @default false
   */
  disableScaling?: boolean;
  /**
   * Apply the hover effect permanently
   * @default false
   */
  selected?: boolean;
};

const ShiningCard = forwardRef<HTMLDivElement, ShiningCardProps>(
  ({ className, disableScaling = false, selected = false, ...props }, ref) => (
    <Card
      ref={ref}
      className={cn('glass-shine-effect', disableScaling && 'no-scaling', selected && 'selected', className)}
      {...props}
    />
  )
);

ShiningCard.displayName = 'ShiningCard';

export default ShiningCard;
