import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { But<PERSON> } from '../Button/button';
import { Input } from '../Input/input';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  Sheet<PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  SheetTrigger,
} from './sheet';

const meta = {
  title: 'UI/Sheet',
  component: Sheet,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Sheet>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Right: Story = {
  render: () => (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline">Open Sheet</Button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Edit Profile</SheetTitle>
          <SheetDescription>Make changes to your profile here. Click save when you&#39;re done.</SheetDescription>
        </SheetHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="name" className="text-right">
              Name
            </label>
            <Input id="name" value="John Doe" className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="username" className="text-right">
              Username
            </label>
            <Input id="username" value="@johndoe" className="col-span-3" />
          </div>
        </div>
        <SheetFooter>
          <SheetClose asChild>
            <Button type="submit">Save changes</Button>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  ),
};

export const Left: Story = {
  render: () => (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline">Open Left Sheet</Button>
      </SheetTrigger>
      <SheetContent side="left">
        <SheetHeader>
          <SheetTitle>Menu</SheetTitle>
          <SheetDescription>Navigation options and settings.</SheetDescription>
        </SheetHeader>
        <div className="flex flex-col gap-4 py-4">
          <Button variant="ghost" className="justify-start">
            Home
          </Button>
          <Button variant="ghost" className="justify-start">
            Dashboard
          </Button>
          <Button variant="ghost" className="justify-start">
            Settings
          </Button>
          <Button variant="ghost" className="justify-start">
            Profile
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  ),
};

export const Top: Story = {
  render: () => (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline">Open Top Sheet</Button>
      </SheetTrigger>
      <SheetContent side="top" className="h-1/3">
        <SheetHeader>
          <SheetTitle>Notifications</SheetTitle>
          <SheetDescription>Your recent notifications.</SheetDescription>
        </SheetHeader>
        <div className="py-4">
          <div className="mb-2 p-2 rounded bg-muted">
            <p className="font-medium">New message</p>
            <p className="text-sm text-muted-foreground">You have a new message from Jane.</p>
          </div>
          <div className="mb-2 p-2 rounded bg-muted">
            <p className="font-medium">Task completed</p>
            <p className="text-sm text-muted-foreground">Project X has been marked as complete.</p>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  ),
};

export const Bottom: Story = {
  render: () => (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline">Open Bottom Sheet</Button>
      </SheetTrigger>
      <SheetContent side="bottom" className="h-1/3">
        <SheetHeader>
          <SheetTitle>Music Player</SheetTitle>
          <SheetDescription>Currently playing</SheetDescription>
        </SheetHeader>
        <div className="flex items-center justify-center py-4">
          <div className="text-center">
            <p className="font-medium">Song Title</p>
            <p className="text-sm text-muted-foreground">Artist Name</p>
            <div className="flex justify-center gap-4 mt-4">
              <Button size="icon" variant="ghost">
                ⏮️
              </Button>
              <Button size="icon">▶️</Button>
              <Button size="icon" variant="ghost">
                ⏭️
              </Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  ),
};
