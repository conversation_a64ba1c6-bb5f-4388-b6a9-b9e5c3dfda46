import { Skeleton } from '@/src/client/presentation/components/ui/Skeleton/skeleton';
import type { Meta, StoryObj } from '@storybook/react';
import SkeletonHelper from './SkeletonHelper';

const meta = {
  title: 'Custom/SkeletonHelper',
  component: SkeletonHelper,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    amount: {
      control: { type: 'number', min: 1, max: 10 },
    },
  },
} satisfies Meta<typeof SkeletonHelper>;

export default meta;
type Story = StoryObj<typeof meta>;

// Create a simple skeleton component for demonstration
const CardSkeleton = () => (
  <div className="flex flex-col space-y-3 mb-4">
    <Skeleton className="h-[125px] w-[250px] rounded-xl" />
    <div className="space-y-2">
      <Skeleton className="h-4 w-[250px]" />
      <Skeleton className="h-4 w-[200px]" />
    </div>
  </div>
);

export const Default: Story = {
  args: {
    amount: 3,
    component: CardSkeleton,
  },
};

export const SingleItem: Story = {
  args: {
    amount: 1,
    component: CardSkeleton,
  },
};

export const ManyItems: Story = {
  args: {
    amount: 5,
    component: CardSkeleton,
  },
};
