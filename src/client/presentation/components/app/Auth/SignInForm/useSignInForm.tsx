import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { useAuthActions } from '@convex-dev/auth/react';
import { useCallback, useState } from 'react';

export const useSignInForm = () => {
  const [isLoading, setLoading] = useState(false);
  const locale = useLocale();
  const { signIn } = useAuthActions();

  const handleSignIn = useCallback(async () => {
    setLoading(true);
    await signIn('github', { redirectTo: `/${locale}/lobby/code-reviews/public` });
  }, [signIn, locale]);

  return {
    isLoading,
    handleSignIn,
  };
};
