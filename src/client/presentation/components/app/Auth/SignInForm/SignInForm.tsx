import SignInButton from '@/src/client/presentation/components/app/Auth/SignInButton/SignInButton';
import ShiningCard from '@/src/client/presentation/components/ui/ShiningCard/ShiningCard';
import { Flex, Heading, Text } from '@radix-ui/themes';
import Image from 'next/image';
import { type FC, memo } from 'react';

const SignInForm: FC = () => (
  <ShiningCard>
    <Flex direction="column" gap="4" p="6" minWidth={{ sm: '450px' }}>
      <Flex justify="center" align="center">
        <Image src="/logo.png" alt="logo" width="300" height="300" />
      </Flex>

      <Flex direction="column" align="center">
        <Heading size="6" mb="1">
          Login
        </Heading>
        <Text size="3" color="gray">
          Sign in or create an account
        </Text>
      </Flex>
      <SignInButton />
    </Flex>
  </ShiningCard>
);

export default memo(SignInForm);
