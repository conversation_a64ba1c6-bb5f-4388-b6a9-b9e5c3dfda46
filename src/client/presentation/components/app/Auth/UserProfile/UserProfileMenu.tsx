'use client';

import {Avatar, AvatarFallback, AvatarImage} from '@/src/client/presentation/components/ui/Avatar/avatar';

import {api} from '@/convex/_generated/api';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/src/client/presentation/components/ui/Sidebar/sidebar';
import {useLocale} from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import {useToast} from '@/src/client/presentation/hooks/utils/useToast/useToast';
import {useAuthActions} from '@convex-dev/auth/react';
import {DropdownMenu, Flex, Grid, Skeleton, Text} from '@radix-ui/themes';
import {useQuery} from 'convex/react';
import {Bell, ChevronsUpDown, LogOut, Sparkles, User} from 'lucide-react';
import Link from 'next/link';
import React, {useCallback} from 'react';
import {useUserProfile} from './useUserProfile';

const UserProfileMenu = () => {
  const {isMobile} = useSidebar();
  const {viewModel, isLoading} = useUserProfile();
  const {signOut} = useAuthActions();
  const {toast} = useToast();
  const locale = useLocale();

  // Get current user ID
  const currentUserData = useQuery(api.queries.getCurrentUserId.endpoint);
  const currentUserId = currentUserData?.userId;

  const handleSignOut = async () => {
    await signOut();
    location.reload();
  };

  const handleNotImplemented = useCallback(() => {
    toast({title: 'Feature not implemented', description: 'Try again later', variant: 'destructive'});
  }, [toast]);

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu.Root>
          <DropdownMenu.Trigger>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={viewModel?.avatar.image || ''} alt={viewModel?.user || ''}/>
                <AvatarFallback className="rounded-lg">{viewModel?.avatar.fallback || ''}</AvatarFallback>
              </Avatar>
              <Grid flexGrow="1" className="text-left text-sm leading-tight">
                <Text truncate={true} weight="bold" color="orange">
                  <Skeleton loading={isLoading}>{viewModel?.user || 'Craft User'}</Skeleton>
                </Text>

                <Text truncate={true} size="1">
                  <Skeleton loading={isLoading}>{viewModel?.plan || '(free plan)'}</Skeleton>
                </Text>
              </Grid>
              <ChevronsUpDown className="ml-auto size-4"/>
            </SidebarMenuButton>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg z-50"
            side={isMobile ? 'bottom' : 'right'}
            align="end"
            sideOffset={4}
          >
            <DropdownMenu.Label className="p-0 font-normal">
              <Flex align="center" gap="2" py="1.5" className="text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage src={viewModel?.avatar.image || ''} alt={viewModel?.user || ''}/>
                  <AvatarFallback className="rounded-lg">{viewModel?.avatar.fallback || ''}</AvatarFallback>
                </Avatar>
                <Grid flexGrow="1" className="text-left text-sm">
                  <Text truncate={true} weight="bold" color="orange">
                    <Skeleton loading={isLoading}>{viewModel?.user || 'Craft User'}</Skeleton>
                  </Text>

                  <Text truncate={true} size="1">
                    <Skeleton loading={isLoading}>{viewModel?.plan || '(free plan)'}</Skeleton>
                  </Text>
                </Grid>
              </Flex>
            </DropdownMenu.Label>
            <DropdownMenu.Separator/>
            <DropdownMenu.Group>
              <DropdownMenu.Item className="cursor-pointer !text-white" color="iris" onClick={handleNotImplemented}>
                <Sparkles size="20"/>
                Upgrade to Pro
              </DropdownMenu.Item>
            </DropdownMenu.Group>
            <DropdownMenu.Separator/>
            <DropdownMenu.Group>
              {currentUserId && (
                <DropdownMenu.Item className="cursor-pointer !text-white" color="iris" asChild>
                  <Link href={`/${locale}/users/${currentUserId}`}>
                    <User size="20"/>
                    <span>My Profile</span>
                  </Link>
                </DropdownMenu.Item>
              )}
              <DropdownMenu.Item className="cursor-pointer !text-white" color="iris" onClick={handleNotImplemented}>
                <Bell size="20"/>
                <span>Notifications</span>
              </DropdownMenu.Item>
            </DropdownMenu.Group>
            <DropdownMenu.Separator/>
            <DropdownMenu.Item className="cursor-pointer" color="red" onClick={handleSignOut}>
              <LogOut size="20"/>
              <span>Log out</span>
            </DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      </SidebarMenuItem>
    </SidebarMenu>
  );
};

export default UserProfileMenu;
