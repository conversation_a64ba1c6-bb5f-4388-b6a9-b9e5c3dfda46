'use client';

import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { Button } from '@radix-ui/themes';
import Link from 'next/link';
import React, { type FC } from 'react';

type Props = {
  codeReviewId: string;
};

const OpenCodeReviewButton: FC<Props> = ({ codeReviewId }) => {
  const locale = useLocale();

  return (
    <Button className="ml-auto" asChild>
      <Link href={`/${locale}/code-reviews/${codeReviewId}`}>Open code review</Link>
    </Button>
  );
};

export default OpenCodeReviewButton;
