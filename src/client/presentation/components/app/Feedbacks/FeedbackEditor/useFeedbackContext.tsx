'use client';

import React, { createContext, useContext, useState, type PropsWithChildren, useCallback } from 'react';

type FeedbackContextType = {
  feedbackTitle: string;
  feedbackText: string;
  setFeedbackTitle: (text: string) => void;
  setFeedbackText: (text: string) => void;
  resetFeedbackText: () => void;
  resetFeedbackTitle: () => void;
};

const FeedbackContext = createContext<FeedbackContextType | undefined>(undefined);

export const FeedbackProvider = ({ children }: PropsWithChildren) => {
  const [feedbackTitle, setFeedbackTitleState] = useState<string>('');
  const [feedbackText, setFeedbackTextState] = useState<string>('');

  const setFeedbackTitle = useCallback((text: string) => {
    setFeedbackTitleState(text);
  }, []);

  const resetFeedbackTitle = useCallback(() => {
    setFeedbackTitleState('');
  }, []);

  const setFeedbackText = useCallback((text: string) => {
    setFeedbackTextState(text);
  }, []);

  const resetFeedbackText = useCallback(() => {
    setFeedbackTextState('');
  }, []);

  return (
    <FeedbackContext.Provider
      value={{
        feedbackText,
        setFeedbackText,
        resetFeedbackText,
        feedbackTitle,
        setFeedbackTitle,
        resetFeedbackTitle,
      }}
    >
      {children}
    </FeedbackContext.Provider>
  );
};

export const useFeedbackContext = (): FeedbackContextType => {
  const context = useContext(FeedbackContext);
  if (!context) {
    throw new Error('useFeedbackContext must be used within a FeedbackProvider');
  }
  return context;
};
