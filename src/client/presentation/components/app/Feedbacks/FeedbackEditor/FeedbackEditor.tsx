'use client';

import { MDXEditorWrapper } from '@/src/client/presentation/components/app/Code/MdxEditor/MDXEditor';
import { useFeedbackContext } from '@/src/client/presentation/components/app/Feedbacks/FeedbackEditor/useFeedbackContext';
import { Box, Text, TextField } from '@radix-ui/themes';
import debounce from 'lodash-es/debounce';
import React from 'react';

const FeedbackEditor = () => {
  const { feedbackText, setFeedbackText, setFeedbackTitle } = useFeedbackContext();

  return (
    <Box>
      <Text as="label" size="2" htmlFor="feedback-text">
        Action to take:
      </Text>
      <TextField.Root
        placeholder="Ex: Extract code to a variable"
        my="2"
        onChange={debounce((event) => setFeedbackTitle(event.target.value), 200)}
      />

      <Text as="label" size="2">
        Details:
      </Text>
      <Box mt="2">
        <MDXEditorWrapper markdown={feedbackText} onChange={setFeedbackText} />
      </Box>
    </Box>
  );
};

export default FeedbackEditor;
