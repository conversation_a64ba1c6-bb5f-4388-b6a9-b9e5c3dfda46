import CodeBlock from '@/src/client/presentation/components/app/Code/CodeBlock/CodeBlock';
import { DetailedFeedbackAction } from '@/src/client/presentation/components/app/Feedbacks/FeedbackCodeContainer/DetailedFeedbackAction';
import { DetailedFeedbackFooter } from '@/src/client/presentation/components/app/Feedbacks/FeedbackCodeContainer/DetailedFeedbackFooter';
import OpenFeedbackButton from '@/src/client/presentation/components/app/Feedbacks/OpenFeedbackButton/OpenFeedbackButton';
import { ShikiCodeFormatter } from '@/src/server/infrastructure/formatters/CodeFormatter/ShikiCodeFormatter';
import { Card, Flex } from '@radix-ui/themes';
import React, { type FC, memo, useEffect, useState } from 'react';

type Props = {
  feedback: {
    id: string;
    title: string;
    code: string;
    content: string;
    language: string;
    startLine: number;
    endLine: number;
    subType: string;
    codeSnippetId: string;
    codeSnippetFilename: string;
    codeReviewTitle: string;
    codeReviewId: string;
  };
};

const MyFeedback: FC<Props> = ({ feedback }) => {
  const [formattedCode, setFormattedCode] = useState<string>('Loading code...');

  useEffect(() => {
    const codeFormatter = new ShikiCodeFormatter();
    codeFormatter
      .formatCodeSnippet(
        {
          code: feedback.code,
          language: feedback.language,
          startLine: feedback.startLine,
          endLine: feedback.endLine,
        },
        feedback.subType
      )
      .then((formattedCode) => {
        setFormattedCode(formattedCode);
      });
  }, [feedback]);

  return (
    <Card>
      <Flex direction="column" flexGrow="1">
        <DetailedFeedbackAction title={feedback.title} />

        <CodeBlock code={formattedCode} />

        {feedback.content && <DetailedFeedbackFooter content={feedback.content} />}

        <Flex justify="end" mt="3">
          <OpenFeedbackButton
            codeReviewId={feedback.codeReviewId}
            snippetId={feedback.codeSnippetId}
            feedbackId={feedback.id}
          />
        </Flex>
      </Flex>
    </Card>
  );
};

export default memo(MyFeedback);
