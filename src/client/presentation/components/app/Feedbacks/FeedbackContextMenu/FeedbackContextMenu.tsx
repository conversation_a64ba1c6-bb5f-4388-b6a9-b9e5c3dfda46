'use client';

import { useCodeContext } from '@/src/client/presentation/components/app/Code/CodeBlock/CodeContext';
import { useFeedbackCreationDialog } from '@/src/client/presentation/components/app/Feedbacks/FeedbackCreationDialog/FeedbackCreationDialogContext';
import { ContextMenu, Text } from '@radix-ui/themes';
import {
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  Eraser,
  FileDown,
  FileInput,
  FlaskConical,
  FlaskConicalOff,
  ListX,
  MessageCirclePlusIcon,
  Pi,
  Pickaxe,
  SquarePlus,
  Variable,
} from 'lucide-react';
import type React from 'react';
import { useCallback } from 'react';
import './FeedbackContextMenu.css';

const FeedbackContextMenu = ({ children }: { children: React.ReactNode }) => {
  const { selectedLines, resetSelection } = useCodeContext();

  const lineText = useCallback(() => {
    if (!selectedLines) return '';
    return selectedLines.start === selectedLines.end
      ? `line ${selectedLines.start}`
      : `lines (${selectedLines.start}-${selectedLines.end})`;
  }, [selectedLines]);

  const getButtonText = useCallback(() => {
    if (!selectedLines) return 'Add feedback on file';
    if (selectedLines.start === selectedLines.end) {
      return `Add feedback on line ${selectedLines.start}`;
    }
    return `Add feedback on lines (${selectedLines.start}-${selectedLines.end})`;
  }, [selectedLines]);

  const {
    openDialog,
    removeComment,
    addEmptyLineBefore,
    addEmptyLineAfter,
    addEmptyLinesBeforeAndAfter,
    removeCodeBlock,
    extractToVariable,
    extractToConstant,
    extractToFunction,
    extractToOwnFile,
    codeNotTested,
    fileNotTested,
  } = useFeedbackCreationDialog();

  return (
    <ContextMenu.Root modal={true}>
      <ContextMenu.Trigger>{children}</ContextMenu.Trigger>
      <ContextMenu.Content>
        <ContextMenu.Item onSelect={openDialog}>
          <MessageCirclePlusIcon size="20" />
          <Text>{getButtonText()}</Text>
        </ContextMenu.Item>

        <ContextMenu.Separator />

        <ContextMenu.Sub>
          <ContextMenu.SubTrigger disabled={!selectedLines}>
            <SquarePlus size="18" />
            Add ...
          </ContextMenu.SubTrigger>
          <ContextMenu.SubContent>
            <ContextMenu.Item
              disabled={!selectedLines}
              onSelect={async () => {
                await addEmptyLineBefore();
                resetSelection();
              }}
            >
              <ArrowUp size="18" />
              Empty line before {lineText()}
            </ContextMenu.Item>
            <ContextMenu.Item
              disabled={!selectedLines}
              onSelect={async () => {
                await addEmptyLineAfter();
                resetSelection();
              }}
            >
              <ArrowDown size="18" />
              Empty line after {lineText()}
            </ContextMenu.Item>
            <ContextMenu.Item
              disabled={!selectedLines}
              onSelect={async () => {
                await addEmptyLinesBeforeAndAfter();
                resetSelection();
              }}
            >
              <ArrowUpDown size="18" />
              Empty line both before and after {lineText()}
            </ContextMenu.Item>
          </ContextMenu.SubContent>
        </ContextMenu.Sub>

        <ContextMenu.Sub>
          <ContextMenu.SubTrigger disabled={!selectedLines}>
            <Eraser size="18" />
            Remove ...
          </ContextMenu.SubTrigger>
          <ContextMenu.SubContent>
            <ContextMenu.Item
              disabled={!selectedLines}
              onSelect={async () => {
                await removeComment();
                resetSelection();
              }}
            >
              <Eraser size="18" />
              Comment at {lineText()}
            </ContextMenu.Item>
            <ContextMenu.Item
              disabled={!selectedLines}
              onSelect={async () => {
                await removeCodeBlock();
                resetSelection();
              }}
            >
              <ListX size="18" />
              Code {lineText()}
            </ContextMenu.Item>
          </ContextMenu.SubContent>
        </ContextMenu.Sub>

        <ContextMenu.Separator />

        <ContextMenu.Sub>
          <ContextMenu.SubTrigger disabled={!selectedLines}>
            <Pickaxe size="18" />
            Extract code ...
          </ContextMenu.SubTrigger>
          <ContextMenu.SubContent>
            <ContextMenu.Item
              disabled={!selectedLines}
              onSelect={async () => {
                await extractToVariable();
                resetSelection();
              }}
            >
              <Variable size="18" />
              To variable
            </ContextMenu.Item>
            <ContextMenu.Item
              disabled={!selectedLines}
              onSelect={async () => {
                await extractToConstant();
                resetSelection();
              }}
            >
              <Pi size="18" />
              To constant
            </ContextMenu.Item>
            <ContextMenu.Item
              disabled={!selectedLines}
              onSelect={async () => {
                await extractToFunction();
                resetSelection();
              }}
            >
              <FileDown size="18" />
              To function
            </ContextMenu.Item>
            <ContextMenu.Item
              disabled={!selectedLines}
              onSelect={async () => {
                await extractToOwnFile();
                resetSelection();
              }}
            >
              <FileInput size="18" />
              To own file
            </ContextMenu.Item>
          </ContextMenu.SubContent>
        </ContextMenu.Sub>

        <ContextMenu.Separator />

        <ContextMenu.Sub>
          <ContextMenu.SubTrigger disabled={!selectedLines}>
            <FlaskConical size="18" />
            Testability
          </ContextMenu.SubTrigger>
          <ContextMenu.SubContent>
            <ContextMenu.Item
              disabled={!selectedLines}
              onSelect={async () => {
                await fileNotTested();
                resetSelection();
              }}
            >
              <FlaskConicalOff size="18" />
              File not tested
            </ContextMenu.Item>
            <ContextMenu.Item
              disabled={!selectedLines}
              onSelect={async () => {
                await codeNotTested();
                resetSelection();
              }}
            >
              <FlaskConicalOff size="18" />
              Missing tests for {lineText()}
            </ContextMenu.Item>
          </ContextMenu.SubContent>
        </ContextMenu.Sub>
      </ContextMenu.Content>
    </ContextMenu.Root>
  );
};

export default FeedbackContextMenu;
