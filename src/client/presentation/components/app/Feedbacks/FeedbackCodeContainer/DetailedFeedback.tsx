import CodeBlock from '@/src/client/presentation/components/app/Code/CodeBlock/CodeBlock';
import { DetailedFeedbackAction } from '@/src/client/presentation/components/app/Feedbacks/FeedbackCodeContainer/DetailedFeedbackAction';
import { DetailedFeedbackFooter } from '@/src/client/presentation/components/app/Feedbacks/FeedbackCodeContainer/DetailedFeedbackFooter';
import FeedbackCodeContainerContent from '@/src/client/presentation/components/app/Feedbacks/FeedbackCodeContainerContent/FeedbackCodeContainerContent';
import { Flex } from '@radix-ui/themes';
import React, { type FC } from 'react';

type Props = {
  title: string;
  code: string;
  content?: string;
};

const DetailedFeedback: FC<Props> = ({ title, code, content }) => {
  return (
    <Flex direction="column">
      <DetailedFeedbackAction title={title} />

      <FeedbackCodeContainerContent>
        <CodeBlock code={code} />
      </FeedbackCodeContainerContent>

      {content && <DetailedFeedbackFooter content={content} />}
    </Flex>
  );
};

export default DetailedFeedback;
