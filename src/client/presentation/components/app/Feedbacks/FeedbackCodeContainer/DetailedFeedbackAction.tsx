import { Avatar, Flex, Text } from '@radix-ui/themes';
import { Zap } from 'lucide-react';
import React, { type FC } from 'react';

export const DetailedFeedbackAction: FC<{ title: string }> = ({ title }) => (
  <Flex direction="column" className="bg-[#11111140] p-2 rounded-t-md">
    <Flex direction="row" gap="3" align="start" className="px-1">
      <Avatar fallback={<Zap color="plum" />} size="3" color="plum" />

      <Flex direction="column" flexGrow="1" className="text-left">
        <Text size="2" weight="medium" color="plum">
          Action
        </Text>

        <Text size="3">{title}</Text>
      </Flex>
    </Flex>
  </Flex>
);
