'use client';

import ShiningButton from '@/src/client/presentation/components/ui/ShiningButton/ShiningButton';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import Link from 'next/link';
import React, { type FC } from 'react';

type Props = {
  codeReviewId: string;
  feedbackId: string;
  snippetId: string;
};

const OpenFeedbackButton: FC<Props> = ({ codeReviewId, snippetId, feedbackId }) => {
  const locale = useLocale();

  return (
    <ShiningButton className="ml-auto" color="iris" asChild>
      <Link href={`/${locale}/code-reviews/${codeReviewId}?snippetId=${snippetId}&feedbackId=${feedbackId}`}>
        Open feedback
      </Link>
    </ShiningButton>
  );
};

export default OpenFeedbackButton;
