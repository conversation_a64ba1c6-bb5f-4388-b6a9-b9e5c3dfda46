'use client';

import { Select } from '@radix-ui/themes';
import React, { useCallback, useState } from 'react';

const FeedbackTypeSelector = () => {
  const [feedbackType, setFeedbackType] = useState<'constructive' | 'suggestion'>('constructive');

  const handleFeedbackTypeChange = useCallback((value: 'constructive' | 'suggestion') => {
    setFeedbackType(value);
  }, []);

  return (
    <Select.Root value={feedbackType} onValueChange={handleFeedbackTypeChange}>
      <Select.Trigger aria-label="Feedback type">
        {feedbackType === 'constructive' ? 'Constructive comment' : feedbackType}
      </Select.Trigger>
      <Select.Content>
        <Select.Item value="comment">Constructive comment</Select.Item>
        <Select.Item value="suggestion">Code suggestion</Select.Item>
      </Select.Content>
    </Select.Root>
  );
};

export default FeedbackTypeSelector;
