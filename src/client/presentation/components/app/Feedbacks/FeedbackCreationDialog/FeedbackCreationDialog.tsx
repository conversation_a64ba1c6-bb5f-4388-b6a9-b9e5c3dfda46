'use client';

import { useCodeContext } from '@/src/client/presentation/components/app/Code/CodeBlock/CodeContext';
import SelectedCodeBlock from '@/src/client/presentation/components/app/Code/SelectedCodeBlock/SelectedCodeBlock';
import FeedbackEditor from '@/src/client/presentation/components/app/Feedbacks/FeedbackEditor/FeedbackEditor';
import { Box, Button, Dialog, Flex, Inset, Text } from '@radix-ui/themes';
import React from 'react';
import './FeedbackCreationDialog.css';
import { useFeedbackCreationDialog } from '@/src/client/presentation/components/app/Feedbacks/FeedbackCreationDialog/FeedbackCreationDialogContext';

const FeedbackCreationDialog = () => {
  const { selectedCode } = useCodeContext();
  const { handleSubmit, isSubmitting, error, handleOpenChange, open } = useFeedbackCreationDialog();

  return (
    <Dialog.Root open={open} onOpenChange={handleOpenChange}>
      <Dialog.Content className="feedback-dialog">
        <Inset side="y" mb="4">
          <Box mt="4" mb="2">
            <Dialog.Title size="4" weight="bold">
              <Flex flexGrow="1" align="center">
                <Box as="span">Give a feedback</Box>
              </Flex>
            </Dialog.Title>

            <Dialog.Description>
              <Text size="2">
                {selectedCode
                  ? 'Please provide actionable and constructive feedback for this code:'
                  : 'Please provide actionable and constructive feedback for this file.'}
              </Text>
            </Dialog.Description>
          </Box>

          <Flex direction="column" gap="4">
            <SelectedCodeBlock />

            <Flex direction="column" gap="2">
              <FeedbackEditor />
            </Flex>
          </Flex>

          <Flex gap="3" mt="4" justify="end" align="center">
            <Flex gap="3">
              <Dialog.Close>
                <Button variant="soft" color="gray">
                  Cancel
                </Button>
              </Dialog.Close>
              <Button onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? 'Saving feedback...' : 'Save feedback'}
              </Button>
            </Flex>
          </Flex>

          {error && (
            <Text color="red" size="2" mt="2">
              {error}
            </Text>
          )}
        </Inset>
      </Dialog.Content>
    </Dialog.Root>
  );
};

export default FeedbackCreationDialog;
