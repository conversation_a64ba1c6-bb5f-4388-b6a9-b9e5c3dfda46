import FeedbackContextMenu from '@/src/client/presentation/components/app/Feedbacks/FeedbackContextMenu/FeedbackContextMenu';
import { Flex } from '@radix-ui/themes';
import React, { type FC, type PropsWithChildren } from 'react';

const FeedbackCodeContainerContent: FC<PropsWithChildren> = ({ children }) => (
  <Flex direction="column" className="min-h-0 rounded-md">
    <FeedbackContextMenu>
      <Flex className="h-full bg-[#111111] rounded-md">{children}</Flex>
    </FeedbackContextMenu>
  </Flex>
);

export default FeedbackCodeContainerContent;
