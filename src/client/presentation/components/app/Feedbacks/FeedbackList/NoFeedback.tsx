import { SidebarMenuButton } from '@/src/client/presentation/components/ui/Sidebar/sidebar';
import { Box, Text } from '@radix-ui/themes';
import { CornerDownRight } from 'lucide-react';
import React from 'react';

const NoFeedback = () => {
  return (
    <SidebarMenuButton disabled>
      <CornerDownRight />
      <Box as="span">
        <Text size="2">No feedback in this category yet.</Text>
      </Box>
    </SidebarMenuButton>
  );
};

export default NoFeedback;
