import type { api } from '@/convex/_generated/api';
import { type Preloaded, usePreloadedQuery } from 'convex/react';

export const useFeedbackListQuery = (preloadedQuery: Preloaded<typeof api.queries.feedbackList.endpoint>) => {
  const data = usePreloadedQuery(preloadedQuery);
  const feedbacks = data?.feedbacks || [];

  const boyscouts = feedbacks.filter((f) => f.type === 'boyscout');
  const constructive = feedbacks.filter((f) => f.type === 'constructive');
  const questions = feedbacks.filter((f) => f.type === 'question');
  const missing = feedbacks.filter((f) => f.type === 'missing');

  return {
    feedbacks: {
      boyscouts,
      constructive,
      questions,
      missing,
    },
    feedbackListLoading: !data || !feedbacks,
    noFeedbackFound: data && feedbacks && feedbacks.length === 0,
  };
};
