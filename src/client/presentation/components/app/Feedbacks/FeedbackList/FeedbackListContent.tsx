'use client';

import type { api } from '@/convex/_generated/api';
import FeedbackSidebarMenuItem from '@/src/client/presentation/components/app/Feedbacks/FeedbackItem/FeedbackSidebarMenuItem';
import NoFeedback from '@/src/client/presentation/components/app/Feedbacks/FeedbackList/NoFeedback';
import { useFeedbackListQuery } from '@/src/client/presentation/components/app/Feedbacks/FeedbackList/useFeedbackListQuery';
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
} from '@/src/client/presentation/components/ui/Sidebar/sidebar';
import { useFeedbackId } from '@/src/client/presentation/hooks/utils/useFeedbackId/useFeedbackId';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@radix-ui/react-collapsible';
import type { Preloaded } from 'convex/react';
import { ChevronDown } from 'lucide-react';
import React from 'react';

export default function FeedbackListContent({
  preloadedFeedbackListQuery,
}: {
  preloadedFeedbackListQuery: Preloaded<typeof api.queries.feedbackList.endpoint>;
}) {
  const { feedbacks } = useFeedbackListQuery(preloadedFeedbackListQuery);
  const [animationParent] = useAutoAnimate();
  const feedbackId = useFeedbackId();

  return (
    <>
      <Collapsible defaultOpen className="group/collapsible">
        <SidebarGroup>
          <SidebarGroupLabel asChild>
            <CollapsibleTrigger>
              Critical feedbacks ({feedbacks?.missing.length})
              <ChevronDown className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
            </CollapsibleTrigger>
          </SidebarGroupLabel>
          <CollapsibleContent>
            <SidebarGroupContent>
              <SidebarMenu>
                {feedbacks?.missing.map((feedback) => (
                  <FeedbackSidebarMenuItem
                    key={feedback.id}
                    feedback={feedback}
                    selected={feedbackId === feedback.id}
                  />
                ))}
                {feedbacks?.missing.length === 0 && (
                  <SidebarMenuItem>
                    <NoFeedback />
                  </SidebarMenuItem>
                )}
              </SidebarMenu>
            </SidebarGroupContent>
          </CollapsibleContent>
        </SidebarGroup>
      </Collapsible>

      <Collapsible defaultOpen className="group/collapsible">
        <SidebarGroup>
          <SidebarGroupLabel asChild>
            <CollapsibleTrigger>
              Boyscout rules ({feedbacks?.boyscouts.length})
              <ChevronDown className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
            </CollapsibleTrigger>
          </SidebarGroupLabel>
          <CollapsibleContent>
            <SidebarGroupContent>
              <SidebarMenu ref={animationParent}>
                {feedbacks?.boyscouts.map((feedback) => (
                  <FeedbackSidebarMenuItem
                    key={feedback.id}
                    feedback={feedback}
                    selected={feedbackId === feedback.id}
                  />
                ))}
                {feedbacks?.boyscouts.length === 0 && (
                  <SidebarMenuItem>
                    <NoFeedback />
                  </SidebarMenuItem>
                )}
              </SidebarMenu>
            </SidebarGroupContent>
          </CollapsibleContent>
        </SidebarGroup>
      </Collapsible>

      <Collapsible defaultOpen className="group/collapsible">
        <SidebarGroup>
          <SidebarGroupLabel asChild>
            <CollapsibleTrigger>
              Questions ({feedbacks?.questions.length})
              <ChevronDown className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
            </CollapsibleTrigger>
          </SidebarGroupLabel>
          <CollapsibleContent>
            <SidebarGroupContent>
              <SidebarMenu>
                {feedbacks?.questions.map((feedback) => (
                  <FeedbackSidebarMenuItem
                    key={feedback.id}
                    feedback={feedback}
                    selected={feedbackId === feedback.id}
                  />
                ))}
                {feedbacks?.questions.length === 0 && (
                  <SidebarMenuItem>
                    <NoFeedback />
                  </SidebarMenuItem>
                )}
              </SidebarMenu>
            </SidebarGroupContent>
          </CollapsibleContent>
        </SidebarGroup>
      </Collapsible>

      <Collapsible defaultOpen className="group/collapsible">
        <SidebarGroup>
          <SidebarGroupLabel asChild>
            <CollapsibleTrigger>
              Constructive feedbacks ({feedbacks?.constructive.length})
              <ChevronDown className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
            </CollapsibleTrigger>
          </SidebarGroupLabel>
          <CollapsibleContent>
            <SidebarGroupContent>
              <SidebarMenu>
                {feedbacks?.constructive.map((feedback) => (
                  <FeedbackSidebarMenuItem
                    key={feedback.id}
                    feedback={feedback}
                    selected={feedbackId === feedback.id}
                  />
                ))}
                {feedbacks?.constructive.length === 0 && (
                  <SidebarMenuItem>
                    <NoFeedback />
                  </SidebarMenuItem>
                )}
              </SidebarMenu>
            </SidebarGroupContent>
          </CollapsibleContent>
        </SidebarGroup>
      </Collapsible>
    </>
  );
}
