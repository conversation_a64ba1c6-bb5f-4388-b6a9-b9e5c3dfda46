'use client';

import { cn } from '@/src/client/presentation/lib/utils';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@radix-ui/react-collapsible';
import { Flex, Text } from '@radix-ui/themes';
import { ChevronDown, ChevronUp } from 'lucide-react';
import React, { type PropsWithChildren, useState } from 'react';

type FeedbackAmountProps = {
  amount: number;
  title: string;
  bgClass: string;
  textClass: string;
};

const FeedbackContainer = ({ children, amount, title, textClass }: PropsWithChildren<FeedbackAmountProps>) => {
  const [open, setOpen] = useState(true);
  const [feedbackType] = useAutoAnimate();
  const [feedbackTitle] = useAutoAnimate();
  const [feedbackListItems] = useAutoAnimate();

  return (
    <Collapsible open={open} onOpenChange={setOpen}>
      <CollapsibleTrigger className="w-full">
        <Flex
          direction="row"
          justify="between"
          className={cn('py-1 px-3', 'bg-[#11111140]', textClass, 'cursor-pointer')}
        >
          <Flex gap="2" align="center">
            <Text size="2" weight="bold">
              {title}
            </Text>
            <Text size="3" weight="bold">
              ({amount})
            </Text>
          </Flex>

          <Flex gap="2" align="center" ref={feedbackTitle}>
            {open ? <ChevronUp /> : <ChevronDown />}
          </Flex>
        </Flex>
      </CollapsibleTrigger>
      <CollapsibleContent className="py-1" ref={feedbackType}>
        <Flex direction="column" gap="2" ref={feedbackListItems}>
          {children}
        </Flex>
      </CollapsibleContent>
    </Collapsible>
  );
};

export default FeedbackContainer;
