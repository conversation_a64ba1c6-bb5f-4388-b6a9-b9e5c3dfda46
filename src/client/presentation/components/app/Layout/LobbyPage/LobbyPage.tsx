import MainBackground from '@/src/client/presentation/components/app/Layout/Backgrounds/MainBackground';
import SectionSeparator from '@/src/client/presentation/components/ui/SectionSeparator/SectionSeparator';
import { SidebarTrigger } from '@/src/client/presentation/components/ui/Sidebar/sidebar';
import { cn } from '@/src/client/presentation/lib/utils';
import { Box, Flex, Heading, Text } from '@radix-ui/themes';
import type { FC, ReactNode } from 'react';

interface LobbyPageProps {
  children: ReactNode;
}

const LobbyPage: FC<LobbyPageProps> = ({ children }) => {
  return (
    <Flex direction="column" gap="4" className="h-full px-3">
      {children}
      <MainBackground />
    </Flex>
  );
};

interface LobbyPageTitleProps {
  children: ReactNode;
}

const LobbyPageTitle: FC<LobbyPageTitleProps> = ({ children }) => <Heading>{children}</Heading>;

interface LobbyPageSubtitleProps {
  children: ReactNode;
}

const LobbyPageSubtitle: FC<LobbyPageSubtitleProps> = ({ children }) => {
  return (
    <Box display={{ initial: 'none', md: 'block' }}>
      <Text size="3" className="italic">
        {children}
      </Text>
    </Box>
  );
};

interface LobbyPageContentProps {
  children: ReactNode;
}

const LobbyPageContent: FC<LobbyPageContentProps> = ({ children }) => (
  <Flex direction="column" gap="6">
    {children}
  </Flex>
);

interface LobbyPageActionProps {
  children: ReactNode;
}

const LobbyPageAction: FC<LobbyPageActionProps> = ({ children }) => (
  <Flex mx="1" align="end">
    {children}
  </Flex>
);

type RadixColor = 'border-blue-500' | 'border-green-500' | 'border-indigo-500' | 'border-red-500';

interface LobbyPageHeaderProps {
  children: ReactNode;
  action?: ReactNode;
  color?: RadixColor;
}

const LobbyPageHeader: FC<LobbyPageHeaderProps> = ({ children, action, color = 'border-indigo-500' }) => {
  return (
    <>
      <Flex justify="between" align="center" className="mt-3">
        <Flex gap="3">
          <Flex align="center" justify="center" className="aspect-square" display={{ initial: 'flex', md: 'none' }}>
            <SidebarTrigger size="4" />
          </Flex>
          <Flex direction="column" className={cn('border-l-4  pl-3', color)}>
            {children}
          </Flex>
        </Flex>
        {action && (
          <Flex mx="1" align="center">
            {action}
          </Flex>
        )}
      </Flex>
      <SectionSeparator />
    </>
  );
};

const LobbyPageNamespace = Object.assign(LobbyPage, {
  Title: LobbyPageTitle,
  Subtitle: LobbyPageSubtitle,
  Content: LobbyPageContent,
  Action: LobbyPageAction,
  Header: LobbyPageHeader,
});

export default LobbyPageNamespace;
