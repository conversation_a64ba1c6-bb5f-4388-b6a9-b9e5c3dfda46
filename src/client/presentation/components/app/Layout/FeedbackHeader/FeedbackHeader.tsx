import { Flex } from '@radix-ui/themes';
import Image from 'next/image';
import type { FC, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  action?: ReactNode;
  image?: string;
}

const FeedbackHeader: FC<Props> = ({ children, action, image }) => (
  <Flex justify="between" align="center" className="w-full">
    <Flex gap="3">
      {image && (
        <Image src={image} alt="Code Review Avatar" width="40" height="40" className="opacity-80 hidden md:block" />
      )}

      <Flex direction="column" width="100%">
        {children}
      </Flex>
    </Flex>
    {action && (
      <Flex mx="1" align="center">
        {action}
      </Flex>
    )}
  </Flex>
);

export default FeedbackHeader;
