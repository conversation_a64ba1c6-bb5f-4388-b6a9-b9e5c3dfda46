import { GameControls } from '@/src/client/presentation/components/app/Layout/LeftSidebar/GameControls';
import { Sidebar, SidebarContent, SidebarHeader } from '@/src/client/presentation/components/ui/Sidebar/sidebar';
import { Box, Flex, Text } from '@radix-ui/themes';
import Image from 'next/image';
import React, { type FC } from 'react';

const GameMenuRightSidebar: FC = async () => {
  return (
    <Sidebar variant="floating" className="py-3 pl-0 pr-3" side="right" width="420px">
      <SidebarHeader>
        <Flex justify="between" p="3" align="center" className="bg-[#11111180] rounded-md">
          <Text color="orange">Map: Syntaxia</Text>
          <Text>Cell: A-12</Text>
        </Flex>
      </SidebarHeader>
      <SidebarContent>
        <Flex direction="column" p="3" mx="2" gap="3" className="bg-[#11111180] rounded-md">
          <Box width="100%">
            <Image src="/maps/map1.png" alt="map" width={400} height={400} />
          </Box>

          <GameControls />
        </Flex>
      </SidebarContent>
    </Sidebar>
  );
};

export default GameMenuRightSidebar;
