import FeedbackList from '@/src/client/presentation/components/app/Feedbacks/FeedbackList/FeedbackList';
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/src/client/presentation/components/ui/Sidebar/sidebar';
import { Heading } from '@radix-ui/themes';
import React, { type FC } from 'react';

type Props = {
  snippetId: string;
};

const CodeReviewRightSidebar: FC<Props> = async ({ snippetId }) => {
  return (
    <Sidebar variant="floating" className="py-3 pl-0 pr-3" side="right" width="420px">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton>
              <Heading size="4" color="iris">
                Feedbacks
              </Heading>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <FeedbackList snippetId={snippetId} />
      </SidebarContent>
    </Sidebar>
  );
};

export default CodeReviewRightSidebar;
