import MainBackground from '@/src/client/presentation/components/app/Layout/Backgrounds/MainBackground';
import SectionSeparator from '@/src/client/presentation/components/ui/SectionSeparator/SectionSeparator';
import { SidebarTrigger } from '@/src/client/presentation/components/ui/Sidebar/sidebar';
import { cn } from '@/src/client/presentation/lib/utils';
import { Flex, Heading, Text } from '@radix-ui/themes';
import Image from 'next/image';
import type { FC, ReactNode } from 'react';

interface CodeReviewPageProps {
  children: ReactNode;
}

const CodeReviewPage: FC<CodeReviewPageProps> = ({ children }) => {
  return (
    <Flex direction="column" gap="4" className="h-full w-full overflow-hidden px-3">
      {children}
      <MainBackground />
    </Flex>
  );
};

interface CodeReviewPageTitleProps {
  children: ReactNode;
}

const CodeReviewPageTitle: FC<CodeReviewPageTitleProps> = ({ children }) => <Heading>{children}</Heading>;

interface CodeReviewPageSubtitleProps {
  children: ReactNode;
}

const CodeReviewPageSubtitle: FC<CodeReviewPageSubtitleProps> = ({ children }) => <Text size="3">{children}</Text>;

interface CodeReviewPageContentProps {
  children: ReactNode;
}

const CodeReviewPageContent: FC<CodeReviewPageContentProps> = ({ children }) => (
  <Flex flexGrow="1" direction="column" gap="3" className="h-full min-h-0 overflow-hidden w-full">
    {children}
  </Flex>
);

interface CodeReviewPageActionProps {
  children: ReactNode;
}

const CodeReviewPageAction: FC<CodeReviewPageActionProps> = ({ children }) => (
  <Flex mx="1" align="end">
    {children}
  </Flex>
);

type RadixColor = 'border-blue-500' | 'border-green-500' | 'border-indigo-500' | 'border-red-500';

interface CodeReviewPageHeaderProps {
  children: ReactNode;
  action?: ReactNode;
  color?: RadixColor;
  image?: string;
}

const CodeReviewPageHeader: FC<CodeReviewPageHeaderProps> = (props) => {
  const { children, action, color = 'border-indigo-500', image } = props;

  return (
    <>
      <Flex justify="between" align="center" className="w-full mt-3">
        <Flex gap="3">
          <Flex align="center" justify="center" className="aspect-square" display={{ initial: 'flex', md: 'none' }}>
            <SidebarTrigger size="4" />
          </Flex>

          <Flex direction="column" className={cn('border-l-4 pl-3 md:border-l-0 md:pl-0', color)}>
            <Flex gap="3">
              {image && (
                <Image
                  src={image}
                  alt="Code Review Avatar"
                  width="40"
                  height="40"
                  className="opacity-80 hidden lg:block"
                />
              )}
              <Flex direction="column" width="100%" className={cn('md:border-l-4 md:pl-3', color)}>
                {children}
              </Flex>
            </Flex>
          </Flex>
        </Flex>
        {action && (
          <Flex mx="1" align="center">
            {action}
          </Flex>
        )}
      </Flex>
      <SectionSeparator />
    </>
  );
};

const CodeReviewPageNamespace = Object.assign(CodeReviewPage, {
  Title: CodeReviewPageTitle,
  Subtitle: CodeReviewPageSubtitle,
  Content: CodeReviewPageContent,
  Action: CodeReviewPageAction,
  Header: CodeReviewPageHeader,
});

export default CodeReviewPageNamespace;
