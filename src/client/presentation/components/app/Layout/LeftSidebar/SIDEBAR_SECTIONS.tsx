import Soon from '@/src/client/presentation/components/app/Layout/LeftSidebar/Soon';
import {
  Clapperboard,
  Clock2,
  CupSoda,
  History,
  Mail,
  MessageCircle,
  MessagesSquare,
  MonitorOff,
  Pizza,
  UsersRound,
} from 'lucide-react';
import React from 'react';

export const SIDEBAR_SECTIONS = {
  history: {
    label: 'Activity',
    items: [
      {
        title: 'My History',
        slug: 'lobby/activity/code-reviews',
        icon: <History />,
      },
      {
        title: 'My Feedbacks',
        slug: 'lobby/activity/feedbacks',
        icon: <MessageCircle />,
      },
      {
        title: 'My Comments',
        slug: 'lobby/activity/comments',
        icon: <MessagesSquare />,
      },
    ],
  },
  rooms: {
    label: 'Rooms',
    items: [
      {
        title: 'Public rooms',
        slug: 'lobby/code-reviews/public',
        icon: <Pizza />,
      },
      {
        title: 'Private rooms',
        slug: 'lobby/code-reviews/private',
        icon: <CupSoda />,
      },
    ],
  },
  myCodeReviews: {
    label: 'My Code Reviews',
    items: [
      {
        title: 'Ready to review',
        slug: 'lobby/code-reviews/ready',
        icon: <Clock2 />,
      },
      {
        title: 'Opened reviews',
        slug: 'lobby/code-reviews/opened',
        icon: <Clapperboard />,
      },
      {
        title: 'Closed reviews',
        slug: 'lobby/code-reviews/closed',
        icon: <MonitorOff />,
      },
    ],
  },
  teams: {
    label: 'Teams',
    items: [
      {
        title: 'My Teams',
        slug: 'lobby/teams',
        icon: <UsersRound />,
        badge: <Soon />,
      },
      {
        title: 'Invitations',
        slug: 'lobby/teams/invites',
        icon: <Mail />,
        badge: <Soon />,
      },
    ],
  },
};
