'use client';

import UserProfileMenu from '@/src/client/presentation/components/app/Auth/UserProfile/UserProfileMenu';
import SidebarLogo from '@/src/client/presentation/components/app/Layout/Logo/SidebarLogo';
import AppScrollArea from '@/src/client/presentation/components/ui/ScrollAreas/AppScrollArea';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/src/client/presentation/components/ui/Sidebar/sidebar';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import React, { type FC, type PropsWithChildren, type ReactNode } from 'react';

type Props = {
  sections: {
    [key: string]: {
      label: string;
      items: {
        title: string;
        slug: string;
        icon: ReactNode;
        badge?: ReactNode;
      }[];
    };
  };
};

const NavigationSidebar: FC<PropsWithChildren<Props>> = ({ children, sections }) => {
  const locale = useLocale();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  return (
    <Sidebar variant="floating" className="py-3 pl-3 pr-0">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarLogo />
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        {children}
        <AppScrollArea>
          {Object.entries(sections).map(([sectionKey, section]) => (
            <SidebarGroup key={sectionKey}>
              <SidebarGroupLabel>{section.label}</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {section.items.map((item) => {
                    const href = `/${locale}/${item.slug}`;
                    const fullPath = searchParams.size > 0 ? `${pathname}?${searchParams.toString()}` : pathname;

                    return (
                      <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton asChild isActive={fullPath === href}>
                          <Link href={href}>
                            {item.icon}
                            <span>{item.title}</span>
                          </Link>
                        </SidebarMenuButton>
                        <SidebarMenuBadge>{item.badge}</SidebarMenuBadge>
                      </SidebarMenuItem>
                    );
                  })}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          ))}
        </AppScrollArea>
      </SidebarContent>

      <SidebarFooter>
        <UserProfileMenu />
      </SidebarFooter>
    </Sidebar>
  );
};

export default NavigationSidebar;
