'use client';

import { Thick<PERSON>rrowDownIcon, ThickArrowLeftIcon, ThickArrowRightIcon, ThickArrowUpIcon } from '@radix-ui/react-icons';
import { Button, Flex, Grid } from '@radix-ui/themes';
import { Footprints } from 'lucide-react';
import type React from 'react';

export const GameControls: React.FC = () => {
  return (
    <Flex direction="column" gap="8">
      <Grid columns="3" gap="8" align="center" justify="center">
        <Button color="plum" size="4" className="py-6 hover:cursor-pointer">
          <ThickArrowUpIcon width="40" height="40" className="!rotate-[-45deg]" />
        </Button>
        <Button size="4" className="py-6 hover:cursor-pointer flex flex-col gap-3">
          <Flex direction="column" align="center" justify="center">
            <ThickArrowUpIcon width="40" height="40" />
          </Flex>
        </Button>
        <Button color="plum" size="4" className="py-6 hover:cursor-pointer">
          <ThickArrowUpIcon width="40" height="40" className="!rotate-[45deg]" />
        </Button>
      </Grid>

      <Grid columns="3" gap="8" align="center" justify="center">
        <Button size="4" className="py-6 hover:cursor-pointer">
          <ThickArrowLeftIcon width="40" height="40" />
        </Button>
        <Flex align="center" justify="center">
          <Footprints color="gray" width="40" height="40" />
        </Flex>
        <Button size="4" className="py-6 hover:cursor-pointer">
          <ThickArrowRightIcon width="40" height="40" />
        </Button>
      </Grid>

      <Grid columns="3" gap="8" align="center" justify="center">
        <Button color="plum" size="4" className="py-6 hover:cursor-pointer">
          <ThickArrowDownIcon width="40" height="40" className="!rotate-[45deg]" />
        </Button>
        <Button size="4" className="py-6 hover:cursor-pointer">
          <ThickArrowDownIcon width="40" height="40" />
        </Button>
        <Button color="plum" size="4" className="py-6 hover:cursor-pointer">
          <ThickArrowDownIcon width="40" height="40" className="!rotate-[-45deg]" />
        </Button>
      </Grid>
    </Flex>
  );
};
