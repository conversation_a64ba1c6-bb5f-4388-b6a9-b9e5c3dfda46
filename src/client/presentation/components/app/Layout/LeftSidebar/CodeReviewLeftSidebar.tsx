import { api } from '@/convex/_generated/api';
import NavigationSidebar from '@/src/client/presentation/components/app/Layout/LeftSidebar/NavigationSidebar';
import { SIDEBAR_SECTIONS } from '@/src/client/presentation/components/app/Layout/LeftSidebar/SIDEBAR_SECTIONS';
import { Badge } from '@radix-ui/themes';
import { fetchQuery } from 'convex/nextjs';
import { MessageCircle } from 'lucide-react';
import React, { type FC } from 'react';

type Props = {
  codeReviewId: string;
};

const CodeReviewLeftSidebar: FC<Props> = async ({ codeReviewId }) => {
  const codeSnippets = await fetchQuery(api.queries.codeReviewEditor.getCodeSnippetListViewModel, { codeReviewId });
  if (!codeSnippets) {
    return null;
  }

  const codeSnippetListItems = codeSnippets.map((snippet) => ({
    ...snippet,
    icon: <span className="text-xs">{snippet.language}</span>,
    badge: (
      <Badge variant="outline" color="indigo">
        <MessageCircle size={12} />
        <span className="text-xs">{snippet.feedbacks}</span>
      </Badge>
    ),
  }));

  const sections = {
    codeSnippets: {
      label: 'Code Snippets',
      items: codeSnippetListItems,
    },
    ...SIDEBAR_SECTIONS,
  };

  return <NavigationSidebar sections={sections} />;
};

export default CodeReviewLeftSidebar;
