import NavigationSidebar from '@/src/client/presentation/components/app/Layout/LeftSidebar/NavigationSidebar';
import { SIDEBAR_SECTIONS } from '@/src/client/presentation/components/app/Layout/LeftSidebar/SIDEBAR_SECTIONS';
import { Box, Card, Flex, Progress, Text } from '@radix-ui/themes';
import Image from 'next/image';
import React, { type FC } from 'react';

const CodeReviewLeftSidebar: FC = async () => {
  return (
    <NavigationSidebar sections={SIDEBAR_SECTIONS}>
      <Box px="3">
        <Card variant="ghost" className="hover:cursor-pointer">
          <Flex justify="between" mb="1" align="center" className="bg-[#11111180] rounded-md">
            <Flex direction="column" p="3">
              <Text size="2" weight="bold" color="orange">
                Stingrey
              </Text>
              <Text size="1">Level 1</Text>
            </Flex>
            <Flex direction="column" p="3" align="end">
              <Text size="1">
                Gold:{' '}
                <Text color="yellow" as="span">
                  1000
                </Text>
              </Text>
              <Text size="1">
                PA:{' '}
                <Text color="green" as="span">
                  300
                </Text>
                / 500
              </Text>
            </Flex>
          </Flex>

          <Box mb="1" position="relative" width="100%">
            <Flex justify="center" align="center" className="bg-[#11111180] rounded-md">
              <Image src="/characters/boy2.png" alt="Player1" width={180} height={180} className="object-contain" />
            </Flex>
          </Box>
          <Box p="3" className="bg-[#11111180] rounded-md">
            <Flex direction="column" gap="2">
              <Flex direction="column" gap="1">
                <Text size="1" weight="bold">
                  HP:{' '}
                  <Text color="red" as="span">
                    250
                  </Text>
                  / 2500
                </Text>
                <Progress color="red" value={25} size="2" />
              </Flex>
              <Flex direction="column" gap="1">
                <Text size="1" weight="bold">
                  EXP : 8000/ 10000
                </Text>
                <Progress color="blue" value={80} size="2" />
              </Flex>
            </Flex>
          </Box>
        </Card>
      </Box>
    </NavigationSidebar>
  );
};

export default CodeReviewLeftSidebar;
