'use client';

import { <PERSON><PERSON>, <PERSON>lex, TextArea } from '@radix-ui/themes';
import { useForm } from '@tanstack/react-form';
import type React from 'react';
import { type FC, useEffect, useRef } from 'react';

interface Props {
  onSubmit: (content: string) => void;
  autoFocus: boolean;
}

const PostCommentForm: FC<Props> = ({ onSubmit, autoFocus }) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const form = useForm({
    defaultValues: {
      comment: '',
    },
    onSubmit: async ({ value }) => {
      if (value.comment.trim()) {
        onSubmit(value.comment);
        form.reset();
      }
    },
  });

  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);

  const handleKeyDown = async (e: React.KeyboardEvent, field: { state: { value: string } }) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        return;
      }

      e.preventDefault();
      if (field.state.value.trim()) {
        await form.handleSubmit();
      }
    }
  };

  return (
    <form
      onSubmit={async (e) => {
        e.preventDefault();
        e.stopPropagation();
        await form.handleSubmit();
      }}
    >
      <form.Field name="comment">
        {(field) => (
          <TextArea
            ref={textareaRef}
            placeholder="Add a comment..."
            value={field.state.value}
            onChange={(e) => field.handleChange(e.target.value)}
            onKeyDown={(e) => handleKeyDown(e, field)}
            className="w-full min-h-[80px]"
          />
        )}
      </form.Field>

      <Flex justify="end" mt="3">
        <form.Subscribe
          selector={(state) => ({
            value: state.values.comment,
            isSubmitting: state.isSubmitting,
          })}
        >
          {(state) => (
            <Button type="submit" disabled={!state.value.trim() || state.isSubmitting} variant="solid" color="iris">
              Submit
            </Button>
          )}
        </form.Subscribe>
      </Flex>
    </form>
  );
};

export default PostCommentForm;
