'use client';

import PostCommentForm from '@/src/client/presentation/components/app/Comments/PostCommentForm/PostCommentForm';
import { usePublishCommentFeature } from '@/src/client/presentation/hooks/features/commands/usePublishCommentFeature';
import { useFeedbackId } from '@/src/client/presentation/hooks/utils/useFeedbackId/useFeedbackId';
import { Avatar, Box, Flex } from '@radix-ui/themes';
import { type FC, memo } from 'react';

type Props = {
  avatar: string;
  avatarFallback: string;
};

const PostCommentBox: FC<Props> = ({ avatar, avatarFallback }) => {
  const feedbackId = useFeedbackId();
  if (!feedbackId) {
    throw new Error('Feedback ID is not found in URL');
  }

  const { publishComment } = usePublishCommentFeature();

  const handleCommentSubmit = async (content: string) => {
    await publishComment({ feedbackId, content });
  };

  return (
    <Box className="p-3 rounded-md shadow-lg bg-[#11111140]">
      <Flex direction="column" gap="2">
        <Flex gap="3" align="start">
          <Avatar src={avatar} fallback={avatarFallback} size="3" />
          <Box className="flex-grow">
            <PostCommentForm onSubmit={handleCommentSubmit} autoFocus={false} />
          </Box>
        </Flex>
      </Flex>
    </Box>
  );
};

export default memo(PostCommentBox);
