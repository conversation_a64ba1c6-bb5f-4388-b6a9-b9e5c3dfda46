'use client';

import Comment from '@/src/client/presentation/components/app/Comments/Comment/Comment';
import CommentBox from '@/src/client/presentation/components/app/Comments/PostCommentBox/CommentBox';
import ReplyBox from '@/src/client/presentation/components/app/Comments/PostReplyBox/PostReplyBox';
import Reply from '@/src/client/presentation/components/app/Comments/Reply/Reply';
import { useLoadFeedbackCommentsFeature } from '@/src/client/presentation/hooks/features/queries/useLoadFeedbackCommentsFeature';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { Box, Flex, Text } from '@radix-ui/themes';
import { Terminal } from 'lucide-react';
import React, { memo, useState } from 'react';

interface Props {
  feedbackId: string;
}

const CommentList = ({ feedbackId }: Props) => {
  const [commentsParent] = useAutoAnimate();
  const [repliesParent] = useAutoAnimate();
  const [replyToCommentId, setReplyToCommentId] = useState<string | null>(null);

  const { viewModel } = useLoadFeedbackCommentsFeature(feedbackId);

  if (!viewModel) {
    return (
      <Flex align="center" justify="center">
        Loading comments...
      </Flex>
    );
  }

  return (
    <Box>
      <Flex justify="between" py="3">
        <Flex gap="2">
          <Terminal />
          <Text size="3">Comments ({viewModel.comments.length})</Text>
        </Flex>
      </Flex>

      <Flex direction="column" gap="3" ref={commentsParent} mb="3">
        {viewModel.comments.map((comment) => (
          <Flex key={comment.id} direction="column">
            <Comment comment={comment} replyToCommentId={replyToCommentId} setReplyToCommentId={setReplyToCommentId} />

            <Box ref={repliesParent}>
              {comment.replies.map((reply) => (
                <Reply
                  key={reply.id}
                  reply={reply}
                  comment={comment}
                  replyToCommentId={replyToCommentId}
                  setReplyToCommentId={setReplyToCommentId}
                />
              ))}
            </Box>

            {comment.id === replyToCommentId && (
              <Box ml="6">
                <ReplyBox
                  avatar={viewModel.user.avatar}
                  avatarFallback={viewModel.user.avatarFallback}
                  commentId={comment.id}
                />
              </Box>
            )}
          </Flex>
        ))}
      </Flex>

      <CommentBox avatar={viewModel.user.avatar} avatarFallback={viewModel.user.avatarFallback} />
    </Box>
  );
};

export default memo(CommentList);
