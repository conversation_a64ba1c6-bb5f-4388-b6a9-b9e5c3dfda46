'use client';

import SimpleComment from '@/src/client/presentation/components/app/Comments/Comment/SimpleComment';
import DateBlock from '@/src/client/presentation/components/ui/DateBlock/DateBlock';
import { useLoadMyCommentsFeature } from '@/src/client/presentation/hooks/features/queries/useLoadMyCommentsFeature';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { Box, Button, Flex, Text } from '@radix-ui/themes';
import Link from 'next/link';
import React, { type FC } from 'react';

const TOTAL_COMMENTS = 50;

const MyCommentList: FC = () => {
  let commentDate: string | null = null;
  const locale = useLocale();

  const { actions, viewModel } = useLoadMyCommentsFeature(TOTAL_COMMENTS);

  if (viewModel.noCommentFound) {
    return <Text size="2">No comment found...</Text>;
  }

  if (viewModel.isLoadingFirstPage) {
    return <Text size="2">Loading comments...</Text>;
  }

  return (
    <>
      {viewModel.containsComments && (
        <Flex direction="column" gap="3">
          {viewModel.comments.map((comment) => {
            const dateBlock =
              commentDate !== comment.creationDay ? (
                <Box mb="3">
                  <DateBlock dateToDisplay={comment.creationDay} />
                </Box>
              ) : null;

            commentDate = comment.creationDay;

            const userComment = (
              <Link
                href={`/${locale}/code-reviews/${comment.codeReview.id}?snippetId=${comment.codeSnippet.id}&feedbackId=${comment.feedback.id}`}
              >
                <Box>
                  <SimpleComment key={comment.id} comment={comment} />
                </Box>
              </Link>
            );

            return (
              <Box key={comment.id}>
                <Box>{dateBlock}</Box>

                <Flex direction="column" gap="3" key={comment.id}>
                  {userComment}
                </Flex>
              </Box>
            );
          })}
        </Flex>
      )}

      {viewModel.shouldDisplayLoadMoreButton && (
        <Flex justify="center" mb="6">
          <Button loading={viewModel.isLoading} onClick={() => actions.loadMore(TOTAL_COMMENTS)}>
            Load more...
          </Button>
        </Flex>
      )}
    </>
  );
};

export default MyCommentList;
