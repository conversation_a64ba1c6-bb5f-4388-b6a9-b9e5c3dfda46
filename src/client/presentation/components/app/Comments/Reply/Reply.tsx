import { Ava<PERSON>, Box, Button, Flex, Text } from '@radix-ui/themes';
import React, { type FC, memo } from 'react';

type Props = {
  comment: {
    id: string;
  };
  reply: {
    author: {
      name: string;
      avatar: string;
      avatarFallback: string;
      isCurrentUser: boolean;
    };
    content: string;
    createdAt: string;
  };
  replyToCommentId: string | null;
  setReplyToCommentId: (commentId: string | null) => void;
};

const Reply: FC<Props> = ({ reply, comment, replyToCommentId, setReplyToCommentId }) => (
  <Box p="3" ml="6" mt="3" className="bg-[#11111140] rounded-md">
    <Flex direction="column">
      <Flex align="start" gap="3">
        <Avatar src={reply.author.avatar} fallback={reply.author.avatarFallback} size="3" />
        <Flex direction="column" className="w-full">
          <Flex gap="2">
            <Text size="2" weight="bold" color={reply.author.isCurrentUser ? 'orange' : 'indigo'}>
              {reply.author.name}
            </Text>
            <Text size="2" color="gray">
              {reply.createdAt}
            </Text>
          </Flex>
          <Text size="2">{reply.content}</Text>
          <Flex mt="3" gap="4" align="center">
            <Button size="1" variant="ghost">
              Like
            </Button>
            <Button
              size="1"
              variant="ghost"
              onClick={() => setReplyToCommentId(comment.id === replyToCommentId ? null : comment.id)}
            >
              {comment.id === replyToCommentId ? 'Cancel' : 'Reply'}
            </Button>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  </Box>
);

export default memo(Reply);
