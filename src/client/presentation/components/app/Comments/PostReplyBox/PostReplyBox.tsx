import PostCommentForm from '@/src/client/presentation/components/app/Comments/PostCommentForm/PostCommentForm';
import { usePublishCommentFeature } from '@/src/client/presentation/hooks/features/commands/usePublishCommentFeature';
import { useFeedbackId } from '@/src/client/presentation/hooks/utils/useFeedbackId/useFeedbackId';
import { Avatar, Box, Flex } from '@radix-ui/themes';
import { type FC, memo } from 'react';

type Props = {
  avatar: string;
  avatarFallback: string;
  commentId: string;
};

const PostReplyBox: FC<Props> = ({ avatar, avatarFallback, commentId }) => {
  const feedbackId = useFeedbackId();
  if (!feedbackId) {
    throw new Error('Feedback ID is not found in URL');
  }

  const { publishComment } = usePublishCommentFeature();

  const handleReplySubmit = async (content: string) => {
    await publishComment({ feedbackId, content, parentId: commentId });
  };

  return (
    <Box className="p-3 rounded-md shadow-lg bg-[#11111140] mt-3">
      <Flex direction="column" gap="2">
        <Flex gap="3" align="start">
          <Avatar src={avatar} fallback={avatarFallback} size="3" />
          <Box className="flex-grow">
            <PostCommentForm onSubmit={(content) => handleReplySubmit(content)} autoFocus={true} />
          </Box>
        </Flex>
      </Flex>
    </Box>
  );
};

export default memo(PostReplyBox);
