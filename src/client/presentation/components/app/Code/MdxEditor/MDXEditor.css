@import "@mdxeditor/editor/style.css";

.mdx-container {
  isolation: isolate;
  pointer-events: auto;
}

.mdxeditor {
  border: 1px solid var(--gray-6);
  border-radius: var(--radius-2);
  background-color: var(--gray-1);
  width: 100%;
}

.mdxeditor:focus-within {
  border-color: var(--accent-9);
}

[data-theme="dark"] .mdxeditor {
  background-color: var(--gray-3);
  border-color: var(--gray-7);
}

[data-theme="dark"] .mdxeditor:focus-within {
  border-color: var(--accent-8);
}

.mdxeditor-toolbar {
  border-bottom: 1px solid var(--gray-6);
  background-color: var(--gray-2);
  padding: 0.5rem;
  pointer-events: auto;
  border-radius: 4px;
}

[data-theme="dark"] .mdxeditor-toolbar {
  background-color: var(--gray-4);
  border-color: var(--gray-7);
}

.mdxeditor-content-editable {
  padding: 1rem;
  min-height: 150px;
  width: 100%;
  pointer-events: auto;
}

.mdxeditor-placeholder {
  color: var(--gray-8);
}

[data-theme="dark"] .mdxeditor-placeholder {
  color: var(--gray-7);
}

.mdxeditor-root {
  width: 100%;
  display: block;
  pointer-events: auto;
}

.mdxeditor-toolbar button {
  all: unset;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
}

.mdxeditor-toolbar button:hover {
  background-color: var(--gray-4);
}

[data-theme="dark"] .mdxeditor-toolbar button:hover {
  background-color: var(--gray-6);
}
