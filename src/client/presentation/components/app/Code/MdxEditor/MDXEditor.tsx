'use client';

import './MDXEditor.css';
import {
  BlockTypeSelect,
  BoldItalicUnderlineToggles,
  CreateLink,
  InsertImage,
  ListsToggle,
  MDXEditor,
  type MDXEditorMethods,
  Separator,
  UndoRedo,
  headingsPlugin,
  imagePlugin,
  linkDialogPlugin,
  linkPlugin,
  listsPlugin,
  markdownShortcutPlugin,
  quotePlugin,
  thematicBreakPlugin,
  toolbarPlugin,
} from '@mdxeditor/editor';
import debounce from 'lodash-es/debounce';
import { type ForwardedRef, forwardRef, useCallback, useRef } from 'react';

interface MDXEditorProps {
  markdown: string;
  onChange: (markdown: string) => void;
  editorRef?: ForwardedRef<MDXEditorMethods>;
}

const MDXEditorComponent = ({ markdown, onChange, editorRef }: MDXEditorProps) => {
  const onChangeRef = useRef(onChange);
  onChangeRef.current = onChange;

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedOnChange = useCallback(
    debounce((value: string) => {
      onChangeRef.current(value);
    }, 300),
    []
  );

  return (
    <div className="mdx-container" onKeyDown={(e) => e.stopPropagation()}>
      <MDXEditor
        ref={editorRef}
        markdown={markdown}
        onChange={debouncedOnChange}
        plugins={[
          toolbarPlugin({
            toolbarContents: () => (
              <>
                <UndoRedo />
                <Separator />
                <BlockTypeSelect />
                <Separator />
                <BoldItalicUnderlineToggles />
                <Separator />
                <ListsToggle />
                <Separator />
                <CreateLink />
                <InsertImage />
              </>
            ),
          }),
          listsPlugin(),
          linkPlugin(),
          linkDialogPlugin(),
          quotePlugin(),
          headingsPlugin(),
          thematicBreakPlugin(),
          imagePlugin(),
          markdownShortcutPlugin(),
        ]}
        contentEditableClassName="prose dark:prose-invert max-w-full"
      />
    </div>
  );
};

export const MDXEditorWrapper = forwardRef<MDXEditorMethods, Omit<MDXEditorProps, 'editorRef'>>((props, ref) => {
  return <MDXEditorComponent {...props} editorRef={ref} />;
});

MDXEditorWrapper.displayName = 'MDXEditorWrapper';
