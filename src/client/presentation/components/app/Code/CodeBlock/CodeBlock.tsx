'use client';

import { useCodeContext } from '@/src/client/presentation/components/app/Code/CodeBlock/CodeContext';
import { type FC, memo, useCallback, useEffect, useRef, useState } from 'react';
import './CodeBlock.css';

interface CodeBlockProps {
  code: string;
}

const CodeBlock: FC<CodeBlockProps> = ({ code }) => {
  const { setSelectedCode, setSelectedLines, selectedLines } = useCodeContext();
  const [isSelecting, setIsSelecting] = useState(false);
  const selectionStartRef = useRef<number | null>(null);
  const currentSelectionRef = useRef<{ start: number; end: number } | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const linesRef = useRef<{ [key: number]: HTMLElement }>({});
  const [tempSelection, setTempSelection] = useState<{ start: number; end: number } | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;
    linesRef.current = {};
    const lines = containerRef.current.querySelectorAll('span.line');
    for (const line of containerRef.current.querySelectorAll('span.line')) {
      const lineNum = Number.parseInt((line as HTMLElement).dataset.line || '1');
      linesRef.current[lineNum] = line as HTMLElement;
    }

    if (selectedLines) {
      for (const [lineNumStr, line] of Object.entries(linesRef.current)) {
        const lineNum = Number.parseInt(lineNumStr);
        const isSelected = lineNum >= selectedLines.start && lineNum <= selectedLines.end;
        line.classList.toggle('selected', isSelected);
      }
    }
  }, [selectedLines]);

  const updateVisualSelection = useCallback(() => {
    if (!tempSelection) return;
    for (const [lineNumStr, line] of Object.entries(linesRef.current)) {
      const lineNum = Number.parseInt(lineNumStr);
      const isSelected = lineNum >= tempSelection.start && lineNum <= tempSelection.end;
      line.classList.toggle('selected', isSelected);
      line.classList.toggle('selecting', isSelected && isSelecting);
    }
  }, [isSelecting, tempSelection]);

  const updateContext = useCallback(
    (selection: { start: number; end: number } | null) => {
      if (!selection) {
        setSelectedCode('');
        setSelectedLines(null);
        return;
      }
      const selectedText: string[] = [];
      for (let i = selection.start; i <= selection.end; i++) {
        const line = linesRef.current[i];
        if (line) {
          line.classList.remove('selected');
          line.classList.remove('selecting');
          selectedText.push(line.outerHTML || '');
        }
      }
      setSelectedCode(selectedText.join('\n'));
      setSelectedLines(selection);
    },
    [setSelectedCode, setSelectedLines]
  );

  const getLineNumber = useCallback((element: HTMLElement | null): number | null => {
    if (!element) return null;
    const lineElement = element.closest('.line') as HTMLElement;
    if (!lineElement) return null;
    const lineAttribute = lineElement.dataset.line;
    return lineAttribute ? Number.parseInt(lineAttribute) : null;
  }, []);

  const handleSelectionStart = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (e.button !== 0) return;
      const lineNumber = getLineNumber(e.target as HTMLElement);
      if (lineNumber === null) {
        setTempSelection(null);
        updateContext(null);
        return;
      }
      setIsSelecting(true);
      selectionStartRef.current = lineNumber;
      const selection = { start: lineNumber, end: lineNumber };
      currentSelectionRef.current = selection;
      setTempSelection(selection);
      updateVisualSelection();
    },
    [getLineNumber, updateContext, updateVisualSelection]
  );

  const handleSelectionMove = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (!isSelecting || selectionStartRef.current === null) return;
      const currentLine = getLineNumber(e.target as HTMLElement);
      if (currentLine === null || currentLine === tempSelection?.end) return;
      const selection = {
        start: Math.min(selectionStartRef.current, currentLine),
        end: Math.max(selectionStartRef.current, currentLine),
      };
      currentSelectionRef.current = selection;
      setTempSelection(selection);
      updateVisualSelection();
    },
    [isSelecting, getLineNumber, tempSelection, updateVisualSelection]
  );

  useEffect(() => {
    const handleSelectionEnd = () => {
      if (isSelecting && currentSelectionRef.current) {
        updateContext(currentSelectionRef.current);
      }
      setIsSelecting(false);
    };
    document.addEventListener('mouseup', handleSelectionEnd);
    return () => document.removeEventListener('mouseup', handleSelectionEnd);
  }, [isSelecting, updateContext]);

  const handleContextMenu = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      const lineNumber = getLineNumber(e.target as HTMLElement);
      if (lineNumber === null) return;
      if (!selectedLines || lineNumber < selectedLines.start || lineNumber > selectedLines.end) {
        const newSelection = { start: lineNumber, end: lineNumber };
        currentSelectionRef.current = newSelection;
        setTempSelection(newSelection);
        updateContext(newSelection);
      }
    },
    [getLineNumber, selectedLines, updateContext]
  );

  useEffect(() => {
    updateVisualSelection();
  }, [updateVisualSelection]);

  return (
    <div
      ref={containerRef}
      onMouseDown={handleSelectionStart}
      onMouseMove={handleSelectionMove}
      onContextMenu={handleContextMenu}
      className="h-full w-full select-none"
      dangerouslySetInnerHTML={{ __html: code }}
    />
  );
};

export default memo(CodeBlock);
