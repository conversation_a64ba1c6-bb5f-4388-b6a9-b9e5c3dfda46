'use client';

import { Box } from '@radix-ui/themes';
import type { FC } from 'react';
import { useCodeContext } from '../CodeBlock/CodeContext';

const SelectedCodeBlock: FC = () => {
  const { selectedCode } = useCodeContext();

  return (
    selectedCode && (
      <Box className="selected-code">
        <pre className="text-sm bg-[#111111] rounded-md">
          <code className="language-typescript" dangerouslySetInnerHTML={{ __html: selectedCode }} />
        </pre>
      </Box>
    )
  );
};

export default SelectedCodeBlock;
