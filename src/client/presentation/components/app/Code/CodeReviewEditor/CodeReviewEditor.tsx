import { api } from '@/convex/_generated/api';
import CodeBlock from '@/src/client/presentation/components/app/Code/CodeBlock/CodeBlock';
import CodeReviewEditorContent from '@/src/client/presentation/components/app/Code/CodeReviewEditorContent/CodeReviewEditorContent';
import { ShikiCodeFormatter } from '@/src/server/infrastructure/formatters/CodeFormatter/ShikiCodeFormatter';
import { Text } from '@radix-ui/themes';
import { fetchQuery } from 'convex/nextjs';
import React from 'react';

type CodeReviewEditorProps = {
  snippetId: string;
};

const CodeReviewEditor = async ({ snippetId }: CodeReviewEditorProps) => {
  const activeCodeSnippet = await fetchQuery(api.queries.codeReviewEditor.getCodeSnippetViewModel, {
    codeSnippetId: snippetId,
  });
  const codeFormatter = new ShikiCodeFormatter();
  const code = await codeFormatter.format({
    code: activeCodeSnippet!.content,
    language: activeCodeSnippet!.language,
  });

  return (
    <CodeReviewEditorContent>
      {activeCodeSnippet && <CodeBlock code={code} />}
      {!activeCodeSnippet && (
        <Text size="2" className="text-white">
          Loading...
        </Text>
      )}
    </CodeReviewEditorContent>
  );
};

export default CodeReviewEditor;
