import ProfileCard from '@/src/client/presentation/components/app/Profile/ProfileCard/ProfileCard';
import ProfileStats from '@/src/client/presentation/components/app/Profile/ProfileStats/ProfileStats';
import { loadUserProfileByUserIdFeature } from '@/src/client/presentation/hooks/features/queries/loadUserProfileByUserIdFeature';
import { Box, Grid } from '@radix-ui/themes';
import React, { type FC } from 'react';

type Props = {
  userId: string;
};

const UserProfile: FC<Props> = async ({ userId }) => {
  const { userProfile } = await loadUserProfileByUserIdFeature(userId);

  return (
    <Grid columns={{ initial: '1', sm: '4' }} gapY="3" gapX={{ initial: '0', sm: '3' }}>
      <Box className="sm:col-span-3">
        <ProfileCard user={userProfile} />
      </Box>

      <Box className="sm:col-span-1">
        <ProfileStats stats={userProfile.stats} />
      </Box>
    </Grid>
  );
};

export default UserProfile;
