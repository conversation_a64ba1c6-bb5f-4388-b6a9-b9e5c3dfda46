import ShiningCard from '@/src/client/presentation/components/ui/ShiningCard/ShiningCard';
import { Flex, Heading, Text } from '@radix-ui/themes';
import { Code, MessageSquare } from 'lucide-react';
import React, { type FC } from 'react';

type Props = {
  stats: {
    feedbacks: number;
    comments: number;
    codeReviews: number;
  };
};

const ProfileStats: FC<Props> = ({ stats }) => (
  <Flex direction={{ initial: 'row', sm: 'column' }} gap="3" className="h-full">
    <ShiningCard className="p-4 flex-1" disableScaling selected>
      <Flex direction="column" align="center" justify="center" gap="2" className="h-full">
        <Flex align="center" gap="2">
          <MessageSquare size={20} className="text-indigo-400" />
          <Text size="3" weight="medium">
            Feedbacks
          </Text>
        </Flex>
        <Heading size="7" color="indigo">
          {stats.feedbacks}
        </Heading>
      </Flex>
    </ShiningCard>
    <ShiningCard className="p-4 flex-1" disableScaling selected>
      <Flex direction="column" align="center" justify="center" gap="2" className="h-full">
        <Flex align="center" gap="2">
          <MessageSquare size={20} className="text-violet-400" />
          <Text size="3" weight="medium">
            Comments
          </Text>
        </Flex>
        <Heading size="7" color="violet">
          {stats.comments}
        </Heading>
      </Flex>
    </ShiningCard>
    <ShiningCard className="p-4 flex-1" disableScaling selected>
      <Flex direction="column" align="center" justify="center" gap="2" className="h-full">
        <Flex align="center" gap="2">
          <Code size={20} className="text-plum-400" />
          <Text size="3" weight="medium">
            Code Reviews
          </Text>
        </Flex>
        <Heading size="7" color="plum">
          {stats.codeReviews}
        </Heading>
      </Flex>
    </ShiningCard>
  </Flex>
);

export default ProfileStats;
