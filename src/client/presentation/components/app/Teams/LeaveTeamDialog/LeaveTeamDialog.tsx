'use client';

import SectionSeparator from '@/src/client/presentation/components/ui/SectionSeparator/SectionSeparator';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { useToast } from '@/src/client/presentation/hooks/utils/useToast/useToast';
import { AlertDialog, Button, Flex, Text } from '@radix-ui/themes';
import { LogOut, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import type { FC, PropsWithChildren } from 'react';

type LeaveTeamDialogProps = PropsWithChildren & {
  teamId: string;
  teamName: string;
};

const LeaveTeamDialog: FC<LeaveTeamDialogProps> = ({ children, teamId, teamName }) => {
  const { toast } = useToast();
  const router = useRouter();
  const locale = useLocale();

  const handleLeave = async () => {
    console.log(`Leaving team: ${teamId} (${teamName})`);

    toast({
      title: 'Team left',
      description: `You have left the team "${teamName}".`,
      variant: 'success',
    });

    router.push(`/${locale}/lobby/teams`);
  };

  return (
    <AlertDialog.Root>
      <AlertDialog.Trigger>{children}</AlertDialog.Trigger>
      <AlertDialog.Content>
        <Flex justify="between">
          <AlertDialog.Title>
            <Flex align="center" gap="3">
              <LogOut size={20} />
              Leave Team
            </Flex>
          </AlertDialog.Title>

          <AlertDialog.Cancel>
            <Button variant="ghost" color="gray">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </AlertDialog.Cancel>
        </Flex>
        <AlertDialog.Description size="2">
          <Text>Are you sure you want to leave this team? You will lose access to team resources.</Text>
        </AlertDialog.Description>

        <Flex direction="column" gap="3">
          <SectionSeparator />

          <Flex direction="column" gap="4" className="h-full">
            <Flex gap="2" align="center" className="bg-amber-500/10 p-3 rounded-md">
              <Text size="2" color="amber">
                Leaving <strong>{teamName}</strong> will remove you from the team. You will need a new invitation to
                rejoin.
              </Text>
            </Flex>

            <Flex justify="end" gap="3">
              <AlertDialog.Cancel>
                <Button variant="soft" color="gray">
                  Cancel
                </Button>
              </AlertDialog.Cancel>

              <AlertDialog.Action>
                <Button color="amber" onClick={handleLeave}>
                  <LogOut size={16} />
                  Leave Team
                </Button>
              </AlertDialog.Action>
            </Flex>
          </Flex>
        </Flex>
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
};

export default LeaveTeamDialog;
