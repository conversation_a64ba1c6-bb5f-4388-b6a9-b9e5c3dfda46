import ShiningButton from '@/src/client/presentation/components/ui/ShiningButton/ShiningButton';
import { Box } from '@radix-ui/themes';
import type React from 'react';
import { forwardRef } from 'react';

type SendInvitationButtonProps = React.ComponentPropsWithoutRef<typeof ShiningButton>;

const SendInvitationButton = forwardRef<HTMLButtonElement, SendInvitationButtonProps>((props, ref) => {
  return (
    <ShiningButton ref={ref} {...props} size={{ initial: '3', md: '2' }}>
      <Box as="span" display={{ initial: 'none', md: 'inline' }}>
        Send invitation
      </Box>
      <Box as="span" display={{ initial: 'inline', md: 'none' }}>
        Create
      </Box>
    </ShiningButton>
  );
});

SendInvitationButton.displayName = 'SendInvitationButton';

export default SendInvitationButton;
