'use client';

import LoadingContent from '@/src/client/presentation/components/app/Layout/LoadingContent/LoadingContent';
import TeamCard from '@/src/client/presentation/components/app/Teams/TeamCard/TeamCard';
import CardGrid from '@/src/client/presentation/components/ui/CardGrid/CardGrid';
import { useUserTeamsFeature } from '@/src/client/presentation/hooks/features/queries/useUserTeamsFeature';
import { Box, Flex, Heading, Text } from '@radix-ui/themes';
import React, { type FC } from 'react';

const TeamsList: FC = () => {
  const { isLoading, ownedTeams, memberOfTeams } = useUserTeamsFeature();

  if (isLoading) {
    return <LoadingContent />;
  }

  const hasNoTeams = ownedTeams.length === 0 && memberOfTeams.length === 0;

  if (hasNoTeams) {
    return (
      <Box>
        <Text>You don&apos;t have any teams yet.</Text>
      </Box>
    );
  }

  return (
    <Flex direction="column" gap="6">
      {ownedTeams.length > 0 && (
        <Box>
          <Flex align="center" gap="1" mb="3">
            <Heading size="4">Teams you own</Heading>
            <Text size="2" color="gray">
              ({ownedTeams.length})
            </Text>
          </Flex>
          <CardGrid>
            {ownedTeams.map((team) => (
              <TeamCard
                key={team.teamId}
                teamId={team.teamId}
                teamName={team.teamName}
                members={team.members}
                isOwned={true}
              />
            ))}
          </CardGrid>
        </Box>
      )}

      {memberOfTeams.length > 0 && (
        <Box>
          <Flex align="center" gap="1" mb="3">
            <Heading size="4">Teams you&apos;re a member of</Heading>
            <Text size="2" color="gray">
              ({memberOfTeams.length})
            </Text>
          </Flex>
          <CardGrid>
            {memberOfTeams.map((team) => {
              if (!team) return null;
              return (
                <TeamCard
                  key={team.teamId}
                  teamId={team.teamId}
                  teamName={team.teamName}
                  members={team.members}
                  isOwned={false}
                />
              );
            })}
          </CardGrid>
        </Box>
      )}
    </Flex>
  );
};

export default TeamsList;
