import ShiningCard from '@/src/client/presentation/components/ui/ShiningCard/ShiningCard';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { Avatar, Badge, Box, Flex, Heading, Text } from '@radix-ui/themes';
import Link from 'next/link';
import { type FC, memo } from 'react';

type TeamMember = {
  userId: string;
  name: string;
  avatar: string;
  avatarFallback: string;
  isOwner: boolean;
};

type TeamCardProps = {
  teamId: string;
  teamName: string;
  members: TeamMember[];
  isOwned: boolean;
};

const MAX_DISPLAYED_MEMBERS = 5;

const TeamCard: FC<TeamCardProps> = ({ teamId, teamName, members, isOwned }) => {
  const locale = useLocale();
  const displayedMembers = members.slice(0, MAX_DISPLAYED_MEMBERS);
  const hasMoreMembers = members.length > MAX_DISPLAYED_MEMBERS;
  const remainingCount = members.length - MAX_DISPLAYED_MEMBERS;

  return (
    <Link href={`/${locale}/lobby/teams/${teamId}`} style={{ textDecoration: 'none' }}>
      <ShiningCard size="3" style={{ height: '100%' }}>
        <Flex direction="column" gap="3" style={{ height: '100%' }}>
          <Flex justify="between" align="center">
            <Heading size="4" trim="start">
              {teamName}
            </Heading>
            {isOwned && (
              <Box>
                <Badge color="plum">Owner</Badge>
              </Box>
            )}
          </Flex>

          <Box style={{ flexGrow: 1 }}>
            <Text size="2" color="gray">
              {members.length} {members.length === 1 ? 'member' : 'members'}
            </Text>
          </Box>

          <Flex align="center">
            <div className="flex -space-x-2 overflow-hidden">
              {displayedMembers.map((member, index) => (
                <Avatar
                  key={member.userId}
                  size="2"
                  src={member.avatar}
                  fallback={member.avatarFallback}
                  radius="full"
                  className="inline-block"
                  style={{ zIndex: 10 + index }}
                />
              ))}

              {hasMoreMembers && (
                <Avatar
                  variant="solid"
                  color="iris"
                  size="2"
                  radius="full"
                  fallback={`+${remainingCount}`}
                  className="inline-block"
                  style={{ zIndex: 10 * MAX_DISPLAYED_MEMBERS }}
                />
              )}
            </div>
          </Flex>
        </Flex>
      </ShiningCard>
    </Link>
  );
};

export default memo(TeamCard);
