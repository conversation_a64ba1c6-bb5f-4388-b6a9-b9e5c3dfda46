'use client';

import SectionSeparator from '@/src/client/presentation/components/ui/SectionSeparator/SectionSeparator';
import { useCharacterLimit } from '@/src/client/presentation/hooks/utils/useCharacterLimit/useCharacterLimit';
import { useToast } from '@/src/client/presentation/hooks/utils/useToast/useToast';
import { AlertDialog, Box, Button, Card, Flex, Text, TextField } from '@radix-ui/themes';
import { Edit2, Save, X } from 'lucide-react';
import { type FC, type FormEvent, type PropsWithChildren, useCallback } from 'react';

type TeamRenameDialogProps = PropsWithChildren & {
  teamId: string;
  currentName: string;
};

const TeamRenameDialog: FC<TeamRenameDialogProps> = ({ children, teamId, currentName }) => {
  const { toast } = useToast();
  const maxLength = 50;

  const {
    value: teamName,
    setValue: setTeamName,
    characterCount: teamNameCount,
    handleChange: teamNameHandleChange,
    maxLength: teamNameLimit,
  } = useCharacterLimit({ maxLength, initialValue: currentName });

  const isSaveDisabled = teamName.trim() === '' || teamName === currentName;

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    console.log(`Renaming team ${teamId} from "${currentName}" to "${teamName}"`);

    // This would be replaced with an actual mutation in the future
    toast({
      title: 'Team renamed successfully!',
      description: `Your team has been renamed to "${teamName}".`,
      variant: 'success',
    });
  };

  const clearValues = useCallback(
    (open: boolean) => {
      if (open) {
        setTeamName(currentName);
      }
    },
    [setTeamName, currentName]
  );

  return (
    <AlertDialog.Root onOpenChange={clearValues}>
      <AlertDialog.Trigger>{children}</AlertDialog.Trigger>
      <AlertDialog.Content>
        <Flex justify="between">
          <AlertDialog.Title>
            <Flex align="center" gap="3">
              <Edit2 size={20} />
              Rename Team
            </Flex>
          </AlertDialog.Title>

          <AlertDialog.Cancel>
            <Button variant="ghost" color="gray">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </AlertDialog.Cancel>
        </Flex>
        <AlertDialog.Description size="2">
          <Text>Change the name of your team.</Text>
        </AlertDialog.Description>

        <Flex direction="column" gap="3">
          <SectionSeparator />

          <form onSubmit={handleSubmit}>
            <Flex direction="column" gap="4" className="h-full">
              <Card className="w-full">
                <Flex direction="column" gap="4" className="h-full">
                  <Box className="bg-[#11111140] pl-2 pr-1 py-1 rounded-md">
                    <Flex align="center" gap="2">
                      <Text size="4" weight="bold">
                        Team name
                      </Text>
                    </Flex>
                  </Box>

                  <Flex direction="column" gap="2">
                    <div className="relative">
                      <TextField.Root
                        id="teamName"
                        className="!pe-16"
                        required
                        size="3"
                        placeholder="e.g. Frontend Team"
                        value={teamName}
                        maxLength={maxLength}
                        onChange={teamNameHandleChange}
                      >
                        <div className="pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-3 text-sm text-muted-foreground peer-disabled:opacity-50">
                          <Text as="div" size="1" wrap="pretty">
                            <Text as="span" color={teamNameCount > 0 ? 'indigo' : 'red'}>
                              {teamNameCount}/{teamNameLimit}
                            </Text>
                          </Text>
                        </div>
                      </TextField.Root>
                    </div>
                  </Flex>
                </Flex>
              </Card>

              <Flex justify="end" gap="3">
                <AlertDialog.Cancel>
                  <Button variant="soft" color="gray">
                    Cancel
                  </Button>
                </AlertDialog.Cancel>

                <AlertDialog.Action>
                  <Button type="submit" disabled={isSaveDisabled}>
                    <Save size={16} />
                    Save Changes
                  </Button>
                </AlertDialog.Action>
              </Flex>
            </Flex>
          </form>
        </Flex>
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
};

export default TeamRenameDialog;
