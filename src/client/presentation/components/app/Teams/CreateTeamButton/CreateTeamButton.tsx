import TeamCreationDialog from '@/src/client/presentation/components/app/Teams/TeamCreationDialog/TeamCreationDialog';
import ShiningButton from '@/src/client/presentation/components/ui/ShiningButton/ShiningButton';
import { Box } from '@radix-ui/themes';
import type React from 'react';
import { forwardRef } from 'react';

type CreateTeamButtonProps = React.ComponentPropsWithoutRef<typeof ShiningButton>;

const CreateTeamButton = forwardRef<HTMLButtonElement, CreateTeamButtonProps>((props, ref) => {
  return (
    <TeamCreationDialog>
      <ShiningButton ref={ref} {...props} size={{ initial: '3', md: '2' }}>
        <Box as="span" display={{ initial: 'none', md: 'inline' }}>
          Create a team
        </Box>
        <Box as="span" display={{ initial: 'inline', md: 'none' }}>
          Create
        </Box>
      </ShiningButton>
    </TeamCreationDialog>
  );
});

CreateTeamButton.displayName = 'CreateTeamButton';

export default CreateTeamButton;
