'use client';

import { useUserTeamInvitationsFeature } from '@/src/client/presentation/hooks/features/queries/useUserTeamInvitationsFeature';
import { Box, Flex, Text } from '@radix-ui/themes';
import { MailOpen } from 'lucide-react';
import type { FC } from 'react';
import TeamInvitationCard from '../TeamInvitationCard/TeamInvitationCard';

const TeamInvitationsList: FC = () => {
  const { isLoading, invitations, noInvitationsFound } = useUserTeamInvitationsFeature();

  if (isLoading) {
    return (
      <Flex justify="center" py="6">
        <Text>Loading invitations...</Text>
      </Flex>
    );
  }

  if (noInvitationsFound) {
    return (
      <Flex direction="column" align="center" gap="4" py="8">
        <Box className="p-4 rounded-full bg-[#11111140]">
          <MailOpen size={32} className="text-gray-400" />
        </Box>
        <Text align="center" size="3" color="gray">
          You don&apos;t have any pending team invitations.
        </Text>
      </Flex>
    );
  }

  return (
    <Flex direction="column" gap="4">
      {invitations.map((invitation) => (
        <TeamInvitationCard
          key={invitation.id}
          id={invitation.id}
          teamId={invitation.teamId}
          teamName={invitation.teamName}
          invitedBy={invitation.invitedBy}
          inviterAvatar={invitation.inviterAvatar}
          inviterAvatarFallback={invitation.inviterAvatarFallback}
          createdAt={invitation.createdAt}
        />
      ))}
    </Flex>
  );
};

export default TeamInvitationsList;
