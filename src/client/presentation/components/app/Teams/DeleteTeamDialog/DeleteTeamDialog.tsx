'use client';

import SectionSeparator from '@/src/client/presentation/components/ui/SectionSeparator/SectionSeparator';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { useToast } from '@/src/client/presentation/hooks/utils/useToast/useToast';
import { AlertDialog, Button, Flex, Text } from '@radix-ui/themes';
import { Trash2, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import type { FC, PropsWithChildren } from 'react';

type DeleteTeamDialogProps = PropsWithChildren & {
  teamId: string;
  teamName: string;
};

const DeleteTeamDialog: FC<DeleteTeamDialogProps> = ({ children, teamId, teamName }) => {
  const { toast } = useToast();
  const router = useRouter();
  const locale = useLocale();

  const handleDelete = async () => {
    console.log(`Deleting team: ${teamId} (${teamName})`);

    toast({
      title: 'Team deleted',
      description: 'Your team has been permanently deleted.',
      variant: 'success',
    });

    router.push(`/${locale}/lobby/teams`);
  };

  return (
    <AlertDialog.Root>
      <AlertDialog.Trigger>{children}</AlertDialog.Trigger>
      <AlertDialog.Content>
        <Flex justify="between">
          <AlertDialog.Title>
            <Flex align="center" gap="3">
              <Trash2 size={20} />
              Delete Team
            </Flex>
          </AlertDialog.Title>

          <AlertDialog.Cancel>
            <Button variant="ghost" color="gray">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </AlertDialog.Cancel>
        </Flex>
        <AlertDialog.Description size="2">
          <Text>Are you sure you want to delete this team? This action cannot be undone.</Text>
        </AlertDialog.Description>

        <Flex direction="column" gap="3">
          <SectionSeparator />

          <Flex direction="column" gap="4" className="h-full">
            <Flex gap="2" align="center" className="bg-red-500/10 p-3 rounded-md">
              <Text size="2" color="red">
                Deleting <strong>{teamName}</strong> will remove all team members and this action cannot be reversed.
              </Text>
            </Flex>

            <Flex justify="end" gap="3">
              <AlertDialog.Cancel>
                <Button variant="soft" color="gray">
                  Cancel
                </Button>
              </AlertDialog.Cancel>

              <AlertDialog.Action>
                <Button color="red" onClick={handleDelete}>
                  <Trash2 size={16} />
                  Delete Team
                </Button>
              </AlertDialog.Action>
            </Flex>
          </Flex>
        </Flex>
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
};

export default DeleteTeamDialog;
