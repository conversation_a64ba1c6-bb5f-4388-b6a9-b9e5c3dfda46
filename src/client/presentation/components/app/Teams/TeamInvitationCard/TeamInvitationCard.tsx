'use client';

import { useAcceptTeamInvitationFeature } from '@/src/client/presentation/hooks/features/commands/useAcceptTeamInvitationFeature';
import { useRejectTeamInvitationFeature } from '@/src/client/presentation/hooks/features/commands/useRejectTeamInvitationFeature';
import { useCurrentUserEmail } from '@/src/client/presentation/hooks/features/queries/useCurrentUserEmail';
import { Avatar, Box, Button, Card, Flex, Text } from '@radix-ui/themes';
import { Check, X } from 'lucide-react';
import { type FC, useState } from 'react';

type TeamInvitationCardProps = {
  id: string;
  teamId: string;
  teamName: string;
  invitedBy: string;
  inviterAvatar: string;
  inviterAvatarFallback: string;
  createdAt: string;
};

const TeamInvitationCard: FC<TeamInvitationCardProps> = ({
  id,
  teamId,
  teamName,
  invitedBy,
  inviterAvatar,
  inviterAvatarFallback,
  createdAt,
}) => {
  const { acceptTeamInvitation } = useAcceptTeamInvitationFeature();
  const { rejectTeamInvitation } = useRejectTeamInvitationFeature();
  const { email, isLoading: isLoadingEmail } = useCurrentUserEmail();
  const [isAccepting, setIsAccepting] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);

  const handleAccept = async () => {
    if (isLoadingEmail || !email) return;

    setIsAccepting(true);
    await acceptTeamInvitation({
      invitationId: id,
      userEmail: email,
    });
    setIsAccepting(false);
  };

  const handleReject = async () => {
    setIsRejecting(true);
    await rejectTeamInvitation(id);
    setIsRejecting(false);
  };

  const formattedDate = new Date(createdAt).toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  return (
    <Card>
      <Flex direction="column" gap="3">
        <Flex justify="between" align="center">
          <Flex direction="column" gap="1">
            <Text size="5" weight="bold">
              {teamName}
            </Text>
            <Flex align="center" gap="2">
              <Avatar size="1" src={inviterAvatar} fallback={inviterAvatarFallback} radius="full" />
              <Text size="2" color="gray">
                Invited by {invitedBy} on {formattedDate}
              </Text>
            </Flex>
          </Flex>
        </Flex>

        <Flex gap="2" justify="end">
          <Button color="red" variant="soft" onClick={handleReject} disabled={isRejecting || isAccepting}>
            <X size={16} />
            {isRejecting ? 'Rejecting...' : 'Reject'}
          </Button>
          <Button color="green" onClick={handleAccept} disabled={isRejecting || isAccepting}>
            <Check size={16} />
            {isAccepting ? 'Accepting...' : 'Accept'}
          </Button>
        </Flex>
      </Flex>
    </Card>
  );
};

export default TeamInvitationCard;
