import ShiningButton from '@/src/client/presentation/components/ui/ShiningButton/ShiningButton';
import { loadUserProfileByUserIdFeature } from '@/src/client/presentation/hooks/features/queries/loadUserProfileByUserIdFeature';
import { Avatar, Box, Flex, Heading, Table, Text } from '@radix-ui/themes';
import React, { type FC } from 'react';

type Props = {
  userId: string;
};

const OwnedTeamsTable: FC<Props> = async ({ userId }) => {
  const { userProfile } = await loadUserProfileByUserIdFeature(userId);
  const teams =
    userProfile.teams.filter((team): team is NonNullable<typeof team> => !!team && team.userRole === 'Owner') || [];

  return (
    <Box>
      <Flex align="center" gap="2" mb="3">
        <Heading size="5" color="plum">
          Owner of Teams
        </Heading>
        <Text size="2" color="gray">
          ({teams.length})
        </Text>
      </Flex>

      <Table.Root variant="surface">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeaderCell>Team Name</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Members</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell align="right">Actions</Table.ColumnHeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {teams.map((team) => (
            <Table.Row key={team.teamId}>
              <Table.Cell>
                <Text weight="bold">{team.teamName}</Text>
              </Table.Cell>
              <Table.Cell>
                <Flex align="center" gap="1">
                  <Flex className="flex -space-x-2 overflow-hidden">
                    {team.members.slice(0, 3).map((member) => (
                      <Avatar
                        key={member.userId}
                        size="1"
                        src={member.avatar}
                        fallback={member.avatarFallback}
                        radius="full"
                        className="inline-block border border-background"
                      />
                    ))}
                    {team.members.length > 3 && (
                      <Avatar
                        variant="solid"
                        color="iris"
                        size="1"
                        radius="full"
                        fallback={`+${team.members.length - 3}`}
                        className="inline-block border border-background"
                      />
                    )}
                  </Flex>
                  <Text size="1" color="gray">
                    {team.members.length} {team.members.length === 1 ? 'member' : 'members'}
                  </Text>
                </Flex>
              </Table.Cell>
              <Table.Cell align="right">
                <ShiningButton size="1" color="plum" variant="soft">
                  Request to Join
                </ShiningButton>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
    </Box>
  );
};

export default OwnedTeamsTable;
