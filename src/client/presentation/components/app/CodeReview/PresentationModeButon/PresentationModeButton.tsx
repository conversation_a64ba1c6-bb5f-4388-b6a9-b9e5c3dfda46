'use client';

import { useSidebar } from '@/src/client/presentation/components/ui/Sidebar/sidebar';
import { Button } from '@radix-ui/themes';
import { MonitorOff, MonitorPlay } from 'lucide-react';
import React, { type FC } from 'react';

const PresentationModeButton: FC = () => {
  const { toggleSidebar, left, right } = useSidebar();

  const onClickHandler = () => {
    toggleSidebar('left');
    toggleSidebar('right');
  };

  return (
    <Button data-sidebar="trigger" variant="outline" color="indigo" onClick={onClickHandler}>
      {left.state === 'expanded' && right.state === 'expanded' ? <MonitorPlay /> : <MonitorOff />}
      Presentation mode
    </Button>
  );
};

export default PresentationModeButton;
