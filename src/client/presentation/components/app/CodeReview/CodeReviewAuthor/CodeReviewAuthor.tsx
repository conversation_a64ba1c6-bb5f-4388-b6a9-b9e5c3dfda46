import { Avatar, Box, Flex, Text } from '@radix-ui/themes';
import React, { type FC } from 'react';

type Props = {
  name: string;
  avatar: string;
};

const CodeReviewAuthor: FC<Props> = ({ name, avatar }) => (
  <Flex align="start" gap="2" mb="3">
    <Avatar size={{ initial: '3', md: '2' }} src={avatar} fallback={name[0]} />
    <Box>
      <Text as="div" size={{ initial: '2', md: '1' }} color="gray">
        Proposed by
      </Text>
      <Text as="div" size={{ initial: '3', md: '2' }} weight="bold">
        {name}
      </Text>
    </Box>
  </Flex>
);

export default CodeReviewAuthor;
