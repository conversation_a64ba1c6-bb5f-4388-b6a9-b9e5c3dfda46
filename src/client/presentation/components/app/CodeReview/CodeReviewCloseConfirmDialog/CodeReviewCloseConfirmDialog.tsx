'use client';

import { useCodeReviewCloseConfirmDialog } from '@/src/client/presentation/components/app/CodeReview/CodeReviewCloseConfirmDialog/useCodeReviewCloseConfirmDialog';
import { Button, Dialog, Flex, Text } from '@radix-ui/themes';
import { MonitorOff } from 'lucide-react';
import type { FC, PropsWithChildren } from 'react';

type Props = PropsWithChildren<{
  codeReviewTitle: string;
  codeReviewId: string;
}>;

const CodeReviewCloseConfirmDialog: FC<Props> = ({ children, codeReviewId, codeReviewTitle }) => {
  const { closeCodeReviewHandler } = useCodeReviewCloseConfirmDialog(codeReviewId);

  return (
    <Dialog.Root>
      <Dialog.Trigger>{children}</Dialog.Trigger>
      <Dialog.Content maxWidth="450px">
        <Dialog.Title>Closing code review</Dialog.Title>
        <Dialog.Description className="flex flex-col gap-3">
          <Text as="span">Are you sure you want to close this code review?</Text>
          <Text as="span" color="iris">
            &#34;{codeReviewTitle}&#34;
          </Text>
        </Dialog.Description>

        <Flex gap="3" mt="6" justify="end">
          <Dialog.Close>
            <Button variant="soft" color="gray">
              Cancel
            </Button>
          </Dialog.Close>
          <Dialog.Close>
            <Button color="red" onClick={() => closeCodeReviewHandler(codeReviewTitle)}>
              <MonitorOff />
              Close code review
            </Button>
          </Dialog.Close>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
};

export default CodeReviewCloseConfirmDialog;
