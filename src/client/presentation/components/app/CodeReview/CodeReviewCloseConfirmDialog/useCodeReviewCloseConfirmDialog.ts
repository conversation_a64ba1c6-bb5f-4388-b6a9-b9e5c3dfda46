import { useCloseCodeReviewFeature } from '@/src/client/presentation/hooks/features/commands/useCloseCodeReviewFeature';
import { useToast } from '@/src/client/presentation/hooks/utils/useToast/useToast';
import { useCallback } from 'react';

export const useCodeReviewCloseConfirmDialog = (codeReviewId: string) => {
  const { closeCodeReview } = useCloseCodeReviewFeature();
  const { toast } = useToast();

  const closeCodeReviewHandler = useCallback(
    async (title: string) => {
      toast({
        title: 'Code review closed successfully!',
        description: `Users can no longer join the code review: ${title}.`,
        variant: 'success',
      });
      await closeCodeReview({ codeReviewId });
    },
    [closeCodeReview, codeReviewId, toast]
  );

  return {
    closeCodeReviewHandler,
  };
};
