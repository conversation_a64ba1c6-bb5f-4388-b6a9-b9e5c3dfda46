import { Button } from '@radix-ui/themes';
import { X } from 'lucide-react';
import { type ComponentProps, forwardRef } from 'react';

const CodeReviewDeleteButton = forwardRef<HTMLButtonElement, ComponentProps<typeof Button>>((props, ref) => (
  <Button ref={ref} {...props} color="gray" variant="ghost">
    <X size="20" />
  </Button>
));

CodeReviewDeleteButton.displayName = 'CodeReviewDeleteButton';

export default CodeReviewDeleteButton;
