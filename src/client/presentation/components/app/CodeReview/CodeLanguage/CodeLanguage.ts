import type { LanguageSupport as LS, StreamParser } from '@codemirror/language';
import type { Extension } from '@codemirror/state';

export type CodeLanguage = {
  label: string;
  value: string;
  extension: () => Promise<Extension>;
};

const importLegacy = () =>
  import('@codemirror/language').then(({ LanguageSupport, StreamLanguage }) => {
    return function legacy(parser: StreamParser<unknown>): LS {
      return new LanguageSupport(StreamLanguage.define(parser));
    };
  });

export const SUPPORTED_LANGUAGES: CodeLanguage[] = [
  {
    value: 'typescript',
    label: 'TS / TSX',
    extension: () =>
      import('@codemirror/lang-javascript').then(({ javascript }) => javascript({ jsx: true, typescript: true })),
  },
  {
    value: 'javascript',
    label: 'JS / JSX',
    extension: () => import('@codemirror/lang-javascript').then(({ javascript }) => javascript({ jsx: true })),
  },
  {
    value: 'java',
    label: 'Java',
    extension: () => import('@codemirror/lang-java').then(({ java }) => java()),
  },
  {
    value: 'kotlin',
    label: 'Kotlin',
    extension: () =>
      Promise.all([importLegacy(), import('@codemirror/legacy-modes/mode/clike')]).then(([cb, m]) => cb(m.kotlin)),
  },
  {
    value: 'css',
    label: 'CSS',
    extension: () => import('@codemirror/lang-css').then(({ css }) => css()),
  },
  {
    value: 'html',
    label: 'HTML',
    extension: () =>
      import('@codemirror/lang-html').then(({ html }) => html({ matchClosingTags: true, autoCloseTags: true })),
  },
  {
    value: 'php',
    label: 'PHP',
    extension: () => import('@codemirror/lang-php').then(({ php }) => php()),
  },
  {
    value: 'python',
    label: 'Python',
    extension: () => import('@codemirror/lang-python').then(({ python }) => python()),
  },
  {
    value: 'markdown',
    label: 'Markdown',
    extension: () => import('@codemirror/lang-markdown').then(({ markdown }) => markdown()),
  },
  {
    value: 'rust',
    label: 'Rust',
    extension: () => import('@codemirror/lang-rust').then(({ rust }) => rust()),
  },
  {
    value: 'cpp',
    label: 'C++',
    extension: () => import('@codemirror/lang-cpp').then(({ cpp }) => cpp()),
  },
  {
    value: 'xml',
    label: 'XML',
    extension: () => import('@codemirror/lang-xml').then(({ xml }) => xml()),
  },
  {
    value: 'ruby',
    label: 'Ruby',
    extension: () =>
      Promise.all([importLegacy(), import('@codemirror/legacy-modes/mode/ruby')]).then(([cb, m]) => cb(m.ruby)),
  },
  {
    value: 'json',
    label: 'JSON',
    extension: () => import('@codemirror/lang-json').then(({ json }) => json()),
  },
  {
    value: 'sql',
    label: 'SQL',
    extension: () => import('@codemirror/lang-sql').then(({ sql }) => sql()),
  },
];
