'use client';

import { RadioGroup, RadioGroupItem } from '@/src/client/presentation/components/ui/RadioGroup/radio-group';
import SectionSeparator from '@/src/client/presentation/components/ui/SectionSeparator/SectionSeparator';
import { useCreateCodeReviewFeature } from '@/src/client/presentation/hooks/features/commands/useCreateCodeReviewFeature';
import { useCharacterLimit } from '@/src/client/presentation/hooks/utils/useCharacterLimit/useCharacterLimit';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { useToast } from '@/src/client/presentation/hooks/utils/useToast/useToast';
import type { Extension } from '@codemirror/state';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { AlertDialog, Box, Button, Card, Flex, Select, Skeleton, Text, TextField } from '@radix-ui/themes';
import { githubDark } from '@uiw/codemirror-theme-github';
import CodeMirror from '@uiw/react-codemirror';
import { Coffee, PlusCircle, Rocket, Save, UserCheck, Users, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { type FC, type FormEvent, type PropsWithChildren, useCallback, useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { SUPPORTED_LANGUAGES } from '../CodeLanguage/CodeLanguage';

type CodeSnippet = {
  id: string;
  filename: string;
  content: string;
  language: string;
};

const CodeReviewCreationDialog: FC<PropsWithChildren> = ({ children }) => {
  const [snippets, setSnippets] = useState<CodeSnippet[]>([
    { id: uuidv4(), filename: '', content: '', language: SUPPORTED_LANGUAGES[0].value },
  ]);

  const locale = useLocale();
  const [parentRef] = useAutoAnimate();
  const [isCodeLoading, setIsCodeLoading] = useState(true);
  const { createCodeReview } = useCreateCodeReviewFeature();

  const maxLength = 120;
  const maxContentLength = 30000;

  const {
    value: title,
    setValue: setTitle,
    characterCount: codeReviewTitleCount,
    handleChange: codeReviewTitleHandleChange,
    maxLength: codeReviewLimit,
  } = useCharacterLimit({ maxLength });

  const router = useRouter();

  const { toast } = useToast();

  const [loadedExtensions, setLoadedExtensions] = useState<Record<string, Extension>>({});

  const getLanguageExtension = useCallback(
    async (langId: string) => {
      if (loadedExtensions[langId]) {
        return loadedExtensions[langId];
      }

      const language = SUPPORTED_LANGUAGES.find((lang) => lang.value === langId);
      if (!language) return [];

      try {
        const extension = await language.extension();
        setLoadedExtensions((prev) => ({ ...prev, [langId]: extension }));
        return extension;
      } catch (error) {
        console.error(`Failed to load language extension for ${langId}:`, error);
        toast({
          title: 'Oups!',
          description: 'Something went wrong while loading the code editor. Try again later.',
          variant: 'destructive',
        });
        return [];
      }
    },
    [loadedExtensions, toast]
  );

  const isSaveDisabled =
    title.trim() === '' || snippets.some((snippet) => snippet.filename.trim() === '' || snippet.content.trim() === '');

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    toast({
      title: 'Code review created successfully!',
      description: 'Your will find your code review in the "Ready to start" section.',
      variant: 'success',
    });
    await createCodeReview({ title, snippets });
    router.push(`/${locale}/lobby/code-reviews/ready`);
  };

  const addSnippet = () => {
    const newSnippet: CodeSnippet = {
      id: uuidv4(),
      filename: '',
      content: '',
      language: SUPPORTED_LANGUAGES[0].value,
    };
    setSnippets([...snippets, newSnippet]);
  };

  const removeSnippet = (id: string) => {
    if (snippets.length > 1) {
      setSnippets(snippets.filter((s) => s.id !== id));
    }
  };

  const updateSnippet = async (id: string, updates: Partial<CodeSnippet>) => {
    const updatedSnippets = snippets.map((snippet) => (snippet.id === id ? { ...snippet, ...updates } : snippet));
    setSnippets(updatedSnippets);

    if (updates.language && !loadedExtensions[updates.language]) {
      const extension = await getLanguageExtension(updates.language);
      setLoadedExtensions((prev) => ({
        ...prev,
        [updates.language!]: extension,
      }));
    }
  };

  const clearValues = useCallback(
    (open: boolean) => {
      if (open) {
        setTitle('');
        setSnippets([{ id: uuidv4(), filename: '', content: '', language: SUPPORTED_LANGUAGES[0].value }]);
      }
    },
    [setTitle]
  );

  useEffect(() => {
    const loadExtensions = async () => {
      for (const snippet of snippets) {
        if (!loadedExtensions[snippet.language]) {
          const extension = await getLanguageExtension(snippet.language);
          setLoadedExtensions((prev) => ({
            ...prev,
            [snippet.language]: extension,
          }));
        }
      }
    };

    loadExtensions().catch(console.error);
  }, [getLanguageExtension, loadedExtensions, snippets]);

  return (
    <AlertDialog.Root onOpenChange={clearValues}>
      <AlertDialog.Trigger>{children}</AlertDialog.Trigger>
      <AlertDialog.Content maxWidth="900px">
        <Flex justify="between">
          <AlertDialog.Title>
            <Flex align="center" gap="3">
              <Coffee />
              Create a code review
            </Flex>
          </AlertDialog.Title>

          <AlertDialog.Cancel>
            <Button variant="ghost" color="gray">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </AlertDialog.Cancel>
        </Flex>
        <AlertDialog.Description size="2">
          <Text>Set up your code review in just 3 steps!</Text>
        </AlertDialog.Description>

        <Flex direction="column" gap="3">
          <SectionSeparator />

          <form onSubmit={handleSubmit}>
            <Flex direction="column" gap="4" className="h-full">
              <Flex gap="4">
                <Card className="w-full">
                  <Flex direction="column" gap="4" className="h-full">
                    <Box className="bg-[#11111140] pl-2 pr-1 py-1 rounded-md">
                      <Flex align="center" gap="2">
                        <Text size="4" weight="bold">
                          1) Give a title to your code review
                        </Text>
                      </Flex>
                    </Box>

                    <Flex direction="column" gap="2">
                      <div className="relative">
                        <TextField.Root
                          id="title"
                          className="!pe-16"
                          required
                          size="3"
                          placeholder="e.g. Add to cart feature"
                          value={title}
                          maxLength={maxLength}
                          onChange={codeReviewTitleHandleChange}
                        >
                          <div className="pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-3 text-sm text-muted-foreground peer-disabled:opacity-50">
                            <Text as="div" size="1" wrap="pretty">
                              <Text as="span" color={codeReviewTitleCount > 0 ? 'indigo' : 'red'}>
                                {codeReviewTitleCount}/{codeReviewLimit}
                              </Text>
                            </Text>
                          </div>
                        </TextField.Root>
                      </div>
                    </Flex>
                  </Flex>
                </Card>

                <Card className="w-full">
                  <Flex direction="column" gap="4" className="h-full">
                    <Flex gap="3" justify="between" align="center" className="bg-[#11111140] pl-2 pr-1 py-1 rounded-md">
                      <Flex align="center" gap="2">
                        <Text size="4" weight="bold">
                          2) Select the visibility
                        </Text>
                      </Flex>
                    </Flex>

                    <RadioGroup className="grid-cols-3 max-w-[400px]" defaultValue="1">
                      <label className="relative flex cursor-pointer flex-col items-center gap-3 rounded-lg border border-input px-2 py-2 text-center shadow-sm shadow-black/5 outline-offset-2 transition-colors has-[[data-state=checked]]:border-ring has-[[data-state=checked]]:bg-accent has-[:focus-visible]:outline has-[:focus-visible]:outline-2 has-[:focus-visible]:outline-ring/70 has-[[data-disabled]]:cursor-not-allowed has-[[data-disabled]]:opacity-30">
                        <RadioGroupItem value="1" className="sr-only after:absolute after:inset-0" />
                        <Flex gap="2" align="center" className="h-[22px] text-indigo-200">
                          <Rocket size="20" />
                          <Text>Public</Text>
                        </Flex>
                      </label>

                      <label className="relative flex cursor-pointer flex-col items-center gap-3 rounded-lg border border-input px-2 py-2 text-center shadow-sm shadow-black/5 outline-offset-2 transition-colors has-[[data-state=checked]]:border-ring has-[[data-state=checked]]:bg-accent has-[:focus-visible]:outline has-[:focus-visible]:outline-2 has-[:focus-visible]:outline-ring/70 has-[[data-disabled]]:cursor-not-allowed has-[[data-disabled]]:opacity-30">
                        <RadioGroupItem value="3" disabled={true} className="sr-only after:absolute after:inset-0" />
                        <Flex gap="2" align="center" className="h-[22px] hover:cursor-not-allowed">
                          <Users size="20" />
                          <Text>Link</Text>
                        </Flex>
                      </label>

                      <label className="relative flex cursor-pointer flex-col items-center gap-3 rounded-lg border border-input px-2 py-2 text-center shadow-sm shadow-black/5 outline-offset-2 transition-colors has-[[data-state=checked]]:border-ring has-[[data-state=checked]]:bg-accent has-[:focus-visible]:outline has-[:focus-visible]:outline-2 has-[:focus-visible]:outline-ring/70 has-[[data-disabled]]:cursor-not-allowed has-[[data-disabled]]:opacity-30">
                        <RadioGroupItem value="2" disabled={true} className="sr-only after:absolute after:inset-0" />
                        <Flex gap="2" align="center" className="h-[22px] hover:cursor-not-allowed">
                          <UserCheck size="20" />
                          <Text>Private</Text>
                        </Flex>
                      </label>
                    </RadioGroup>
                  </Flex>
                </Card>
              </Flex>
              <Card className="w-full">
                <Flex direction="column" gap="3" className="h-full">
                  <Flex gap="3" justify="between" align="center" className="bg-[#11111140] pl-2 pr-1 py-1 rounded-md">
                    <Flex align="center" gap="2">
                      <Text size="4" weight="bold">
                        3) Add the code snippets you want to review
                      </Text>
                    </Flex>
                  </Flex>
                  <Box ref={parentRef} className="space-y-3">
                    {snippets.map((snippet) => (
                      <Card key={snippet.id} className="p-4">
                        <Flex justify="between" align="center" gap="4" className="mb-2">
                          <Flex direction="column" className="flex-1">
                            <div className="relative">
                              <TextField.Root
                                className="!pe-16"
                                size="3"
                                required
                                placeholder="Filename (e.g. main.js)"
                                value={snippet.filename}
                                maxLength={maxLength}
                                onChange={(e) =>
                                  updateSnippet(snippet.id, {
                                    filename: e.target.value,
                                  })
                                }
                              >
                                <div className="pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-3 text-sm text-muted-foreground peer-disabled:opacity-50">
                                  <Text as="div" size="1" wrap="pretty">
                                    <Text as="span" color={snippet.filename.length > 0 ? 'indigo' : 'red'}>
                                      {snippet.filename.length}/{maxLength}
                                    </Text>
                                  </Text>
                                </div>
                              </TextField.Root>
                            </div>
                          </Flex>

                          <Select.Root
                            size="3"
                            value={snippet.language}
                            onValueChange={(value) => updateSnippet(snippet.id, { language: value })}
                          >
                            <Select.Trigger />
                            <Select.Content>
                              <Select.Group>
                                <Select.Label>Languages</Select.Label>
                                {SUPPORTED_LANGUAGES.map((lang) => (
                                  <Select.Item key={lang.value} value={lang.value}>
                                    {lang.label}
                                  </Select.Item>
                                ))}
                              </Select.Group>
                            </Select.Content>
                          </Select.Root>
                          <Button
                            type="button"
                            color="red"
                            variant="ghost"
                            disabled={snippets.length <= 1}
                            onClick={() => removeSnippet(snippet.id)}
                          >
                            Remove
                          </Button>
                        </Flex>
                        <Skeleton loading={isCodeLoading}>
                          <div className="h-[400px] bg-[#111111] border border-gray-700 rounded-md">
                            <CodeMirror
                              onCreateEditor={() => setIsCodeLoading(false)}
                              value={snippet.content}
                              height="400px"
                              theme={githubDark}
                              extensions={[loadedExtensions[snippet.language] || []]}
                              placeholder="// Your code here..."
                              basicSetup={{
                                lineNumbers: true,
                                highlightActiveLine: false,
                                autocompletion: false,
                                foldGutter: false,
                                rectangularSelection: false,
                                closeBrackets: false,
                                allowMultipleSelections: false,
                              }}
                              onChange={(value) => {
                                updateSnippet(snippet.id, { content: value });
                              }}
                            />
                          </div>
                        </Skeleton>
                        <Flex justify="end" px="1" className="mt-1">
                          <Text as="div" size="1" wrap="pretty">
                            <Text as="span" color={snippet.content.length > 0 ? 'indigo' : 'red'}>
                              {snippet.content.length}/{maxContentLength}
                            </Text>
                          </Text>
                        </Flex>
                      </Card>
                    ))}
                  </Box>
                  <Button type="button" variant="outline" color="indigo" onClick={addSnippet}>
                    <PlusCircle />
                    Add Another Snippet
                  </Button>

                  <Flex justify="end" gap="3">
                    <AlertDialog.Cancel>
                      <Button variant="soft" color="gray">
                        Cancel
                      </Button>
                    </AlertDialog.Cancel>

                    <AlertDialog.Action>
                      <Button type="submit" disabled={isSaveDisabled}>
                        <Save />
                        Save
                      </Button>
                    </AlertDialog.Action>
                  </Flex>
                </Flex>
              </Card>
            </Flex>
          </form>
        </Flex>
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
};

export default CodeReviewCreationDialog;
