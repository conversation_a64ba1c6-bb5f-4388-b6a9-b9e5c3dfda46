import { useDeleteCodeReviewFeature } from '@/src/client/presentation/hooks/features/commands/useDeleteCodeReviewFeature';
import { useToast } from '@/src/client/presentation/hooks/utils/useToast/useToast';
import { useCallback } from 'react';

export const useCodeReviewDeleteConfirmDialog = (codeReviewId: string) => {
  const { deleteCodeReview } = useDeleteCodeReviewFeature();
  const { toast } = useToast();

  const deleteCodeReviewHandler = useCallback(
    async (title: string) => {
      toast({
        title: 'Code review deleted successfully!',
        description: `Code review: ${title} has been deleted.`,
        variant: 'success',
      });
      await deleteCodeReview({ codeReviewId });
    },
    [toast, deleteCodeReview, codeReviewId]
  );

  return {
    deleteCodeReviewHandler,
  };
};
