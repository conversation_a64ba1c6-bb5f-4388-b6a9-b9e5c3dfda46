import { Button } from '@radix-ui/themes';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import type { FC } from 'react';

type Props = {
  locale: string;
};

const BackToPublicCodeReviewsButton: FC<Props> = ({ locale }) => {
  return (
    <Link href={`/${locale}/lobby/code-reviews/public`}>
      <Button variant="ghost" color="gray">
        <ArrowLeft size={16} />
        Back to public code reviews
      </Button>
    </Link>
  );
};

export default BackToPublicCodeReviewsButton;
