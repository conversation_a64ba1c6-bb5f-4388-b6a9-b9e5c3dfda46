import { Box, Flex } from '@radix-ui/themes';
import { AutoTextSize } from 'auto-text-size';
import type { <PERSON> } from 'react';

type Props = {
  name: string;
};

const CodeReviewTitle: FC<Props> = ({ name }) => (
  <Box pb="3" className="absolute top-0 left-0 right-0 bottom-0">
    <Flex align="center" justify="center" width="100%" height="100%">
      <AutoTextSize mode="box" maxFontSizePx={28} className="text-center font-bold self-center">
        {name}
      </AutoTextSize>
    </Flex>
  </Box>
);

export default CodeReviewTitle;
