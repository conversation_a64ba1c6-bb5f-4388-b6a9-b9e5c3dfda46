import { Badge, Text, Tooltip } from '@radix-ui/themes';
import { Eye } from 'lucide-react';
import type { FC } from 'react';

type Props = {
  visibility: string;
};

const CodeReviewVisibility: FC<Props> = ({ visibility }) => {
  const message = visibility === 'public' ? 'Anyone can participate' : 'Restricted access';

  return (
    <Tooltip content={message}>
      <Badge variant="outline" color="green">
        <Eye size={12} />
        <Text size={{ initial: '3', md: '1' }}>{visibility}</Text>
      </Badge>
    </Tooltip>
  );
};

export default CodeReviewVisibility;
