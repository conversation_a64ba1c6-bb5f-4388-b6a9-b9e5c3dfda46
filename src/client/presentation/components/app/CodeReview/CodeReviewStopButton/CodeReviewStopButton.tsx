'use client';

import ShiningButton from '@/src/client/presentation/components/ui/ShiningButton/ShiningButton';
import { type ComponentProps, forwardRef } from 'react';

type Props = ComponentProps<typeof ShiningButton>;

const CodeReviewStopButton = forwardRef<HTMLButtonElement, Props>((props, ref) => (
  <ShiningButton ref={ref} {...props} color="red" variant="outline" className="w-full" size={{ initial: '3', md: '2' }}>
    Stop
  </ShiningButton>
));

CodeReviewStopButton.displayName = 'CodeReviewStop.Button';

export default CodeReviewStopButton;
