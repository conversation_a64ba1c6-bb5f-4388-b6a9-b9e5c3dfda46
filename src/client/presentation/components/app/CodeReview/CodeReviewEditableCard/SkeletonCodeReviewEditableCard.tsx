import { AspectRatio, Box, Card, Flex, Grid, Skeleton, Text } from '@radix-ui/themes';
import type { FC } from 'react';

const SkeletonCodeReviewEditableCard: FC = () => (
  <Card className="!flex flex-col h-full relative animate-pulse">
    <Flex justify="between">
      <Flex align="start" gap="2" mb="3">
        <Skeleton width="34px" height="34px" className="rounded-md" />
        <Box>
          <Text as="div" size="1" color="gray">
            <Skeleton>Proposed by</Skeleton>
          </Text>
          <Text as="div" size="2">
            <Skeleton>Username</Skeleton>
          </Text>
        </Box>
      </Flex>
    </Flex>

    <Box mb="3" className="relative">
      <AspectRatio ratio={16 / 9}>
        <Skeleton className="rounded-md w-full h-full" />
      </AspectRatio>
    </Box>

    <Flex gap="2" mb="3" justify="between">
      <Skeleton width="36px" height="20px" />

      <Flex gap="2">
        <Skeleton width="36px" height="20px" />
        <Skeleton width="36px" height="20px" />
      </Flex>
    </Flex>

    <Flex gap="1" mb="3">
      <Skeleton width="36px" height="20px" />
      <Skeleton width="36px" height="20px" />
      <Skeleton width="36px" height="20px" />
    </Flex>

    <Grid mt="auto" gap="3" columns="1">
      <Skeleton width="100%" height="32px" />
      <Skeleton width="100%" height="32px" />
    </Grid>
  </Card>
);

export default SkeletonCodeReviewEditableCard;
