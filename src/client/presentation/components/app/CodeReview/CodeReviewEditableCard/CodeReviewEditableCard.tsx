'use client';

import CodeReviewAuthor from '@/src/client/presentation/components/app/CodeReview/CodeReviewAuthor/CodeReviewAuthor';
import CodeReviewCloseConfirmDialog from '@/src/client/presentation/components/app/CodeReview/CodeReviewCloseConfirmDialog/CodeReviewCloseConfirmDialog';
import CodeReviewDeleteButton from '@/src/client/presentation/components/app/CodeReview/CodeReviewDeleteButton/CodeReviewDeleteButton';
import CodeReviewDeleteConfirmDialog from '@/src/client/presentation/components/app/CodeReview/CodeReviewDeleteConfirmDialog/CodeReviewDeleteConfirmDialog';
import CodeReviewEditButton from '@/src/client/presentation/components/app/CodeReview/CodeReviewEditButton/CodeReviewEditButton';
import CodeReviewEditDialog from '@/src/client/presentation/components/app/CodeReview/CodeReviewEditDialog/CodeReviewEditDialog';
import CodeReviewFeedbackCounter from '@/src/client/presentation/components/app/CodeReview/CodeReviewFeedbackCounter/CodeReviewFeedbackCounter';
import CodeReviewImage from '@/src/client/presentation/components/app/CodeReview/CodeReviewImage/CodeReviewImage';
import CodeReviewLanguages from '@/src/client/presentation/components/app/CodeReview/CodeReviewLanguages/CodeReviewLanguages';
import CodeReviewParticipateButton from '@/src/client/presentation/components/app/CodeReview/CodeReviewParticipateButton/CodeReviewParticipateButton';
import CodeReviewParticipateConfirmDialog from '@/src/client/presentation/components/app/CodeReview/CodeReviewParticipateConfirmDialog/CodeReviewParticipateConfirmDialog';
import CodeReviewReOpenButton from '@/src/client/presentation/components/app/CodeReview/CodeReviewReOpenButton/CodeReviewReOpenButton';
import CodeReviewReadFeedbacksButton from '@/src/client/presentation/components/app/CodeReview/CodeReviewReadFeedbacks/CodeReviewReadFeedbacks';
import CodeReviewSnippetsCounter from '@/src/client/presentation/components/app/CodeReview/CodeReviewSnippetsCounter/CodeReviewSnippetsCounter';
import CodeReviewStartButton from '@/src/client/presentation/components/app/CodeReview/CodeReviewStartButton/CodeReviewStartButton';
import CodeReviewStartConfirmDialog from '@/src/client/presentation/components/app/CodeReview/CodeReviewStartConfirmDialog/CodeReviewStartConfirmDialog';
import CodeReviewStopButton from '@/src/client/presentation/components/app/CodeReview/CodeReviewStopButton/CodeReviewStopButton';
import CodeReviewTitle from '@/src/client/presentation/components/app/CodeReview/CodeReviewTitle/CodeReviewTitle';
import CodeReviewVisibility from '@/src/client/presentation/components/app/CodeReview/CodeReviewVisibility/CodeReviewVisibility';
import ShiningCard from '@/src/client/presentation/components/ui/ShiningCard/ShiningCard';
import { Box, Flex, Grid } from '@radix-ui/themes';
import React, { type FC } from 'react';

type Props = {
  codeReview: {
    id: string;
    name: string;
    image: string;
    links: {
      participation: string;
      edition: string;
    };
    author: {
      name: string;
      avatar: string;
    };
    participants: number;
    status: string;
    visibility: string;
    isOwner: boolean;
    editable: boolean;
    totalCodeSnippets: number;
    totalFeedbacks: number;
    languages: string[];
  };
  locale: string;
};

const CodeReviewEditableCard: FC<Props> = ({ codeReview, locale }) => {
  return (
    <ShiningCard className="!flex flex-col h-full relative">
      <Flex justify="between">
        <CodeReviewAuthor name={codeReview.author.name} avatar={codeReview.author.avatar} />
        <CodeReviewDeleteConfirmDialog codeReviewId={codeReview.id} codeReviewTitle={codeReview.name}>
          <CodeReviewDeleteButton />
        </CodeReviewDeleteConfirmDialog>
      </Flex>

      <Box mb="3" className="relative">
        <CodeReviewImage image={codeReview.image} />
        <CodeReviewTitle name={codeReview.name} />
      </Box>

      <Flex gap="2" mb="3" justify="between">
        <CodeReviewVisibility visibility={codeReview.visibility} />

        <Flex gap="2">
          <CodeReviewSnippetsCounter count={codeReview.totalCodeSnippets} />
          <CodeReviewFeedbackCounter count={codeReview.totalFeedbacks} />
        </Flex>
      </Flex>

      <CodeReviewLanguages languages={codeReview.languages} />

      <Grid mt="auto" gap="3" columns="1">
        {codeReview.status === 'open' ? (
          <>
            <CodeReviewCloseConfirmDialog codeReviewId={codeReview.id} codeReviewTitle={codeReview.name}>
              <CodeReviewStopButton />
            </CodeReviewCloseConfirmDialog>

            <CodeReviewParticipateConfirmDialog
              codeReviewId={codeReview.id}
              codeReviewTitle={codeReview.name}
              codeReviewLink={`/${locale}/${codeReview.links.participation}`}
            >
              <CodeReviewParticipateButton />
            </CodeReviewParticipateConfirmDialog>
          </>
        ) : (
          <>
            {(codeReview.status === 'new' || codeReview.status === 'close') && (
              <CodeReviewEditDialog codeReviewId={codeReview.id} codeReviewTitle={codeReview.name}>
                <CodeReviewEditButton />
              </CodeReviewEditDialog>
            )}

            {codeReview.status === 'new' && (
              <CodeReviewStartConfirmDialog codeReviewId={codeReview.id} codeReviewTitle={codeReview.name}>
                <CodeReviewStartButton />
              </CodeReviewStartConfirmDialog>
            )}

            {codeReview.status === 'close' && (
              <>
                <CodeReviewStartConfirmDialog codeReviewId={codeReview.id} codeReviewTitle={codeReview.name}>
                  <CodeReviewReOpenButton />
                </CodeReviewStartConfirmDialog>

                <CodeReviewReadFeedbacksButton />
              </>
            )}
          </>
        )}
      </Grid>
    </ShiningCard>
  );
};

export default CodeReviewEditableCard;
