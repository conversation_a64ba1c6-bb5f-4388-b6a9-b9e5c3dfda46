import { Button } from '@radix-ui/themes';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import type { FC } from 'react';

type Props = {
  locale: string;
  codeReviewId: string;
  snippetId: string;
};

const BackToCodeReviewButton: FC<Props> = ({ locale, codeReviewId, snippetId }) => {
  return (
    <Link href={`/${locale}/code-reviews/${codeReviewId}?snippetId=${snippetId}`}>
      <Button variant="ghost" color="gray">
        <ArrowLeft size={16} />
        Back to the code review
      </Button>
    </Link>
  );
};

export default BackToCodeReviewButton;
