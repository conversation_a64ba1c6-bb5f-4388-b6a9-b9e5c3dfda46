import { Badge, Text, Tooltip } from '@radix-ui/themes';
import { CodeXml } from 'lucide-react';
import type { FC } from 'react';

type Props = {
  count: number;
};

const CodeReviewSnippetsCounter: FC<Props> = ({ count }) => (
  <Tooltip content={`${count} code snippet${count !== 1 ? 's' : ''}`}>
    <Badge variant="outline" color="indigo">
      <CodeXml size={12} />
      <Text size={{ initial: '3', md: '1' }}>{count}</Text>
    </Badge>
  </Tooltip>
);

export default CodeReviewSnippetsCounter;
