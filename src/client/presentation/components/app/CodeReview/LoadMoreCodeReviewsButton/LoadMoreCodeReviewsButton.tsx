import ShiningButton from '@/src/client/presentation/components/ui/ShiningButton/ShiningButton';
import { Coffee } from 'lucide-react';
import { type ComponentPropsWithoutRef, forwardRef } from 'react';

type LoadMoreCodeReviewsButtonProps = ComponentPropsWithoutRef<typeof ShiningButton> & {
  isLoading: boolean;
  isDisabled: boolean;
};

const LoadMoreCodeReviewsButton = forwardRef<HTMLButtonElement, LoadMoreCodeReviewsButtonProps>(
  ({ isLoading, isDisabled, ...props }, ref) => {
    return (
      <ShiningButton disabled={isDisabled} loading={isLoading} size={{ initial: '3', md: '2' }} ref={ref} {...props}>
        <Coffee />
        Load more...
      </ShiningButton>
    );
  }
);

LoadMoreCodeReviewsButton.displayName = 'LoadMoreCodeReviewsButton';

export default LoadMoreCodeReviewsButton;
