'use client';

import { useParticipateToCodeReviewFeature } from '@/src/client/presentation/hooks/features/commands/useParticipateToCodeReviewFeature';
import { Button, Dialog, Flex, Text } from '@radix-ui/themes';
import { Rocket } from 'lucide-react';
import Link from 'next/link';
import { type FC, type PropsWithChildren, useCallback } from 'react';

interface Props {
  codeReviewId: string;
  codeReviewTitle: string;
  codeReviewLink: string;
}

const CodeReviewParticipateConfirmDialog: FC<PropsWithChildren<Props>> = ({
  children,
  codeReviewId,
  codeReviewLink,
  codeReviewTitle,
}) => {
  const { participateToCodeReview } = useParticipateToCodeReviewFeature();

  const participateToCodeReviewHandler = useCallback(async () => {
    await participateToCodeReview({ codeReviewId });
  }, [codeReviewId, participateToCodeReview]);

  return (
    <Dialog.Root>
      <Dialog.Trigger>{children}</Dialog.Trigger>
      <Dialog.Content maxWidth="450px">
        <Dialog.Title>Joining code review</Dialog.Title>
        <Dialog.Description className="flex flex-col gap-3">
          <Text as="span">Do you really want to join the code review ?</Text>
          <Text as="span" color="indigo">
            &#34;{codeReviewTitle}&#34;
          </Text>
        </Dialog.Description>

        <Flex direction={{ initial: 'column', md: 'row' }} gap="3" mt="4" justify="end">
          <Dialog.Close>
            <Button variant="soft" color="gray" size={{ initial: '3', md: '2' }}>
              Cancel
            </Button>
          </Dialog.Close>
          <Button asChild color="iris" onClick={participateToCodeReviewHandler} size={{ initial: '3', md: '2' }}>
            <Link href={codeReviewLink}>
              <Rocket size="20" />
              Join the code review !
            </Link>
          </Button>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
};

export default CodeReviewParticipateConfirmDialog;
