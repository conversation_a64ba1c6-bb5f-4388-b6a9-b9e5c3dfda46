import { AspectRatio } from '@radix-ui/themes';
import Image from 'next/image';
import React from 'react';

type Props = {
  image: string;
};

const CodeReviewImage = ({ image }: Props) => (
  <AspectRatio ratio={16 / 9}>
    {image !== '' && (
      <Image
        src={image}
        alt="Code Review Avatar"
        height={100}
        width={100}
        className="opacity-15"
        style={{
          width: '100%',
          height: '100%',
          borderRadius: 'var(--radius-2)',
        }}
      />
    )}
  </AspectRatio>
);

export default CodeReviewImage;
