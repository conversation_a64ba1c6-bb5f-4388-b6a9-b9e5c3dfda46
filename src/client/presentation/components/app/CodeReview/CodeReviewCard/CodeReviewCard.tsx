'use client';

import CodeReviewAuthor from '@/src/client/presentation/components/app/CodeReview/CodeReviewAuthor/CodeReviewAuthor';
import CodeReviewFeedbackCounter from '@/src/client/presentation/components/app/CodeReview/CodeReviewFeedbackCounter/CodeReviewFeedbackCounter';
import CodeReviewImage from '@/src/client/presentation/components/app/CodeReview/CodeReviewImage/CodeReviewImage';
import CodeReviewLanguages from '@/src/client/presentation/components/app/CodeReview/CodeReviewLanguages/CodeReviewLanguages';
import CodeReviewParticipateButton from '@/src/client/presentation/components/app/CodeReview/CodeReviewParticipateButton/CodeReviewParticipateButton';
import CodeReviewParticipateConfirmDialog from '@/src/client/presentation/components/app/CodeReview/CodeReviewParticipateConfirmDialog/CodeReviewParticipateConfirmDialog';
import CodeReviewSnippetsCounter from '@/src/client/presentation/components/app/CodeReview/CodeReviewSnippetsCounter/CodeReviewSnippetsCounter';
import CodeReviewTitle from '@/src/client/presentation/components/app/CodeReview/CodeReviewTitle/CodeReviewTitle';
import CodeReviewVisibility from '@/src/client/presentation/components/app/CodeReview/CodeReviewVisibility/CodeReviewVisibility';
import ShiningCard from '@/src/client/presentation/components/ui/ShiningCard/ShiningCard';
import { Box, Flex, Grid } from '@radix-ui/themes';
import React, { type FC } from 'react';

type Props = {
  codeReview: {
    id: string;
    name: string;
    image: string;
    links: {
      participation: string;
      edition: string;
    };
    author: {
      name: string;
      avatar: string;
    };
    participants: number;
    status: string;
    visibility: string;
    isOwner: boolean;
    editable: boolean;
    totalCodeSnippets: number;
    totalFeedbacks: number;
    languages: string[];
  };
  locale: string;
};

const CodeReviewCard: FC<Props> = ({ codeReview, locale }) => {
  return (
    <ShiningCard className="!flex flex-col h-full relative">
      <Flex justify="between">
        <CodeReviewAuthor name={codeReview.author.name} avatar={codeReview.author.avatar} />
      </Flex>

      <Box mb="3" className="relative">
        <CodeReviewImage image={codeReview.image} />
        <CodeReviewTitle name={codeReview.name} />
      </Box>

      <Flex gap="2" mb="3" justify="between">
        <CodeReviewVisibility visibility={codeReview.visibility} />

        <Flex gap="2">
          <CodeReviewSnippetsCounter count={codeReview.totalCodeSnippets} />
          <CodeReviewFeedbackCounter count={codeReview.totalFeedbacks} />
        </Flex>
      </Flex>

      <CodeReviewLanguages languages={codeReview.languages} />

      <Grid mt="auto" gap="3" columns="1">
        <CodeReviewParticipateConfirmDialog
          codeReviewId={codeReview.id}
          codeReviewTitle={codeReview.name}
          codeReviewLink={`/${locale}/${codeReview.links.participation}`}
        >
          <CodeReviewParticipateButton size={{ initial: '3', md: '2' }} />
        </CodeReviewParticipateConfirmDialog>
      </Grid>
    </ShiningCard>
  );
};

export default CodeReviewCard;
