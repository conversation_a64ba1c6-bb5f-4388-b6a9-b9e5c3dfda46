import { Badge, Text, Tooltip } from '@radix-ui/themes';
import { Users } from 'lucide-react';
import type { FC } from 'react';

interface Props {
  count: number;
}

const CodeReviewParticipantCounter: FC<Props> = ({ count }) => (
  <Tooltip content={`${count} participant${count !== 1 ? 's' : ''}`}>
    <Badge variant="outline" color="indigo">
      <Users size={12} />
      <Text size="1">{0}</Text>
    </Badge>
  </Tooltip>
);

export default CodeReviewParticipantCounter;
