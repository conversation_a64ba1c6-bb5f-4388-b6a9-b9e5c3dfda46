import {createTestingStore} from '@/src/client/Shared/specs/helpers/store/createTestingStore';
import {getDeckName} from '@/src/client/DeckBuilding/application/queries/getDeckName/getDeckName';

describe('getDeckName', () => {
  describe('When the deck has no name', () => {
    it('should return null', () => {
      // Arrange
      const {getState} = createTestingStore();

      // Act
      const name = getDeckName(getState());

      // Assert
      expect(name).toBeNull();
    });
  });

  describe('When the deck has a name', () => {
    it('should return it', () => {
      // Arrange
      const {getState} = createTestingStore({deckBuilder: {name: 'Deck'}});

      // Act
      const name = getDeckName(getState());

      // Assert
      expect(name).toBe('Deck');
    });
  });
});
