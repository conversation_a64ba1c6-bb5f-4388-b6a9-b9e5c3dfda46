import {applyFilterToCard} from "@/src/client/DeckBuilding/application/queries/applyFilterToCard/applyFilterToCard";
import {Filter} from "@/src/client/DeckBuilding/domain/Catalog/Filter";

describe('applyFilterToCard', () => {
  describe('When the filter is a string', () => {
    describe('When the card matches the filter exactly', () => {
      it('should return true', () => {
        // Arrange
        const card = {data: {color: 'AMBER'}};
        const filter: Filter = {id: '1', name: 'AMBER', text: 'Amber', dataProperty: 'color', dataType: 'string', value: 'AMBER', order: 1};

        // Act
        const result = applyFilterToCard(card, filter);

        // Assert
        expect(result).toBeTrue();
      });
    });

    describe('When the card matches the filter partially (with dash)', () => {
      it('should return true', () => {
        // Arrange
        const card = {data: {color: 'AMBER-STEEL'}};
        const filter: Filter = {id: '1', name: '<PERSON>BE<PERSON>', text: 'Amber', dataProperty: 'color', dataType: 'string', value: 'AMBER', order: 1};

        // Act
        const result = applyFilterToCard(card, filter);

        // Assert
        expect(result).toBeTrue();
      });
    });

    describe('When the card does not match the filter', () => {
      it('should return false', () => {
        // Arrange
        const card = {data: {color: 'AMETHYST'}};
        const filter: Filter = {id: '1', name: 'AMBER', text: 'Amber', dataProperty: 'color', dataType: 'string', value: 'AMBER', order: 1};

        // Act
        const result = applyFilterToCard(card, filter);

        // Assert
        expect(result).toBeFalse();
      });
    });
  });

  describe('When the filter is a number', () => {
    describe('When the card matches the filter', () => {
      it('should return true', () => {
        // Arrange
        const card = {data: {cost: 4}};
        const filter: Filter = {id: '1', name: 'COST_4', text: 'Cost 4', dataProperty: 'cost', dataType: 'number', value: 4, order: 1};

        // Act
        const result = applyFilterToCard(card, filter);

        // Assert
        expect(result).toBeTrue();
      });
    });

    describe('When the card does not match the filter', () => {
      it('should return false', () => {
        // Arrange
        const card = {data: {cost: 5}};
        const filter: Filter = {id: '1', name: 'COST_4', text: 'Cost 4', dataProperty: 'cost', dataType: 'number', value: 4, order: 1};

        // Act
        const result = applyFilterToCard(card, filter);

        // Assert
        expect(result).toBeFalse();
      });
    });
  });

  describe('When the filter is a boolean', () => {
    describe('When the card matches the filter', () => {
      it('should return true', () => {
        // Arrange
        const card = {data: {inkable: true}};
        const filter: Filter = {id: '1', name: 'INKABLE', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1};

        // Act
        const result = applyFilterToCard(card, filter);

        // Assert
        expect(result).toBeTrue();
      });
    });

    describe('When the card does not match the filter', () => {
      it('should return false', () => {
        // Arrange
        const card = {data: {inkable: false}};
        const filter: Filter = {id: '1', name: 'INKABLE', text: 'Inkable', dataProperty: 'inkable', dataType: 'boolean', value: true, order: 1};

        // Act
        const result = applyFilterToCard(card, filter);

        // Assert
        expect(result).toBeFalse();
      });
    });
  });

  describe('When the filter is a string array', () => {
    describe('When the card includes the value in the array', () => {
      it('should return true', () => {
        // Arrange
        const card = {data: {subtypes: ['STORYBORN']}};
        const filter: Filter = {
          id: '1',
          name: 'STORYBORN',
          text: 'Storyborn',
          dataProperty: 'subtypes',
          dataType: 'string[]',
          value: 'STORYBORN',
          order: 1
        };

        // Act
        const result = applyFilterToCard(card, filter);

        // Assert
        expect(result).toBeTrue();
      });
    });

    describe('When the card does not include the value in the array', () => {
      it('should return false', () => {
        // Arrange
        const card = {data: {subtypes: ['FLOODBORN']}};
        const filter: Filter = {
          id: '1',
          name: 'STORYBORN',
          text: 'Storyborn',
          dataProperty: 'subtypes',
          dataType: 'string[]',
          value: 'STORYBORN',
          order: 1
        };

        // Act
        const result = applyFilterToCard(card, filter);

        // Assert
        expect(result).toBeFalse();
      });
    });
  });

  describe('When the filter is not supported', () => {
    it('should return false', () => {
      // Arrange
      const card = {data: {color: 'AMBER'}};
      const filter: Filter = {id: '1', name: 'AMBER', text: 'Amber', dataProperty: 'color', dataType: 'unsupported' as never, value: 'AMBER', order: 1};

      // Act
      const result = applyFilterToCard(card, filter);

      // Assert
      expect(result).toBeFalse();
    });
  });
});
