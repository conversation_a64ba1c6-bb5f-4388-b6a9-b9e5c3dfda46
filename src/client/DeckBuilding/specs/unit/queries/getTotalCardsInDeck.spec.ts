import {getTotalCardsInDeck} from "@/src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck";
import {GOOFY_GROUNDBREAKING_CHEF, PINOCCHIO_STRINGS_ATTACHED} from "@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards";
import {createTestingStore} from "@/src/client/Shared/specs/helpers/store/createTestingStore";

describe('getTotalCardsInDeck', () => {
  describe('When no card found in the deck', () => {
    it('should return 0', () => {
      // Arrange
      const {getState} = createTestingStore();

      // Act
      const total = getTotalCardsInDeck(getState());

      // Assert
      expect(total).toBe(0);
    });
  });

  describe('When one card with quantity 1 is found in the deck', () => {
    it('should return the total quantity of 1', () => {
      // Arrange
      const {getState} = createTestingStore({
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
        },
      });

      // Act
      const total = getTotalCardsInDeck(getState());

      // Assert
      expect(total).toBe(1);
    });
  });

  describe('When two cards with quantity 1 and 2 are found in the deck', () => {
    it('should return the total quantity of 3', () => {
      // Arrange
      const {getState} = createTestingStore({
        deckBuilder: {
          cardsInDeck: {
            [GOOFY_GROUNDBREAKING_CHEF.id]: {
              card: GOOFY_GROUNDBREAKING_CHEF,
              quantity: 1,
            },
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 2,
            },
          },
        },
      });

      // Act
      const total = getTotalCardsInDeck(getState());

      // Assert
      expect(total).toBe(3);
    });
  });
});