import {createTestingStore} from "@/src/client/Shared/specs/helpers/store/createTestingStore";
import {loadDeckDraft} from "@/src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft";
import {PINOCCHIO_STRINGS_ATTACHED} from "@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards";
import {FakeDeckDraftService} from "@/src/client/DeckBuilding/specs/helpers/fakes/FakeDeckDraftService";
import {getCardsInDeck} from "@/src/client/DeckBuilding/application/queries/getCardsInDeck/getCardsInDeck";

const draft = {
  name: 'My deck',
  cards: [{cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 2}],
};

describe('loadDeckDraft', () => {
  describe('When a draft exists', () => {
    it('should load the draft into the deck builder', async () => {
      // Arrange
      const deckDraftService = new FakeDeckDraftService(draft);
      const {dispatch, getState} = createTestingStore({
        catalog: {cards: {[PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED}},
      }, {deckDraftService});

      // Act
      await dispatch(loadDeckDraft());

      // Assert
      expect(getCardsInDeck(getState())).toEqual([
        {...PINOCCHIO_STRINGS_ATTACHED, quantity: 2},
      ]);
    });
  });

  describe('When no draft exists', () => {
    it('should not modify the deck builder', async () => {
      // Arrange
      const deckDraftService = new FakeDeckDraftService(null);
      const {dispatch, getState} = createTestingStore({}, {deckDraftService});

      // Act
      await dispatch(loadDeckDraft());

      // Assert
      expect(getCardsInDeck(getState())).toEqual([]);
    });
  });
});
