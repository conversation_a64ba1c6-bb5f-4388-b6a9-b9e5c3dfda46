import {createTestingStore} from "@/src/client/Shared/specs/helpers/store/createTestingStore";
import {PINOCCHIO_STRINGS_ATTACHED} from "@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards";
import {getTotalCardsInDeck} from "@/src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck";
import {removeCardFromDeck} from "@/src/client/DeckBuilding/application/commands/removeCardFromDeck/removeCardFromDeck";
import {findDeckCardById} from "@/src/client/DeckBuilding/application/queries/findDeckCardById/findDeckCardById";

describe('RemoveCardFromDeck', () => {
  describe('When the card is in the deck', () => {
    describe('When the card is not at minimum quantity', () => {
      it('should decrement its quantity in the deck', async () => {
        // Arrange
        const {dispatch, getState} = createTestingStore({
          deckBuilder: {
            cardsInDeck: {
              [PINOCCHIO_STRINGS_ATTACHED.id]: {
                card: PINOCCHIO_STRINGS_ATTACHED,
                quantity: 3,
              },
            },
          },
          catalog: {
            cards: {
              [PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED,
            },
          }
        });

        // Act
        await dispatch(removeCardFromDeck({cardId: PINOCCHIO_STRINGS_ATTACHED.id}));

        // Assert
        expect(getTotalCardsInDeck(getState())).toBe(2);
        expect(findDeckCardById(getState(), PINOCCHIO_STRINGS_ATTACHED.id).quantity).toBe(2);
      });
    });

    describe('When the card is at minimum quantity', () => {
      it('should not decrement its quantity in the deck', async () => {
        // Arrange
        const {dispatch, getState} = createTestingStore({
          deckBuilder: {
            cardsInDeck: {
              [PINOCCHIO_STRINGS_ATTACHED.id]: {
                card: PINOCCHIO_STRINGS_ATTACHED,
                quantity: 1,
              },
            },
          },
          catalog: {
            cards: {
              [PINOCCHIO_STRINGS_ATTACHED.id]: {
                ...PINOCCHIO_STRINGS_ATTACHED,
                minDeckQuantity: 1,
              },
            },
          }
        });

        // Act
        await dispatch(removeCardFromDeck({cardId: PINOCCHIO_STRINGS_ATTACHED.id}));

        // Assert
        expect(getTotalCardsInDeck(getState())).toBe(1);
        expect(findDeckCardById(getState(), PINOCCHIO_STRINGS_ATTACHED.id).quantity).toBe(1);
      });

      it('should delete the card from the deck', async () => {
        // Arrange
        const {dispatch, getState} = createTestingStore({
          deckBuilder: {
            cardsInDeck: {
              [PINOCCHIO_STRINGS_ATTACHED.id]: {
                card: PINOCCHIO_STRINGS_ATTACHED,
                quantity: 1,
              },
            },
          },
          catalog: {
            cards: {
              [PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED,
            },
          }
        });

        // Act
        await dispatch(removeCardFromDeck({cardId: PINOCCHIO_STRINGS_ATTACHED.id}));

        // Assert
        expect(getTotalCardsInDeck(getState())).toBe(0);
        expect(findDeckCardById(getState(), PINOCCHIO_STRINGS_ATTACHED.id)).toBeUndefined();
      });
    });
  });
});