import {createTestingStore} from "@/src/client/Shared/specs/helpers/store/createTestingStore";
import {loadDeckIntoBuilder} from "@/src/client/DeckBuilding/application/commands/loadDeckIntoBuilder/loadDeckIntoBuilder";
import {PINOCCHIO_STRINGS_ATTACHED} from "@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards";
import {findDeckCardById} from "@/src/client/DeckBuilding/application/queries/findDeckCardById/findDeckCardById";

describe('loadDeckIntoBuilder', () => {
  describe('When loading a deck', () => {
    it('should populate the deck builder with the deck cards and name', async () => {
      // Arrange
      const {dispatch, getState} = createTestingStore({
        catalog: {
          cards: {[PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED},
        }
      });

      // Act
      await dispatch(loadDeckIntoBuilder({deck: {name: 'My deck', cards: [{cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 2}]}}));

      // Assert
      expect(findDeckCardById(getState(), PINOCCHIO_STRINGS_ATTACHED.id).quantity).toBe(2);
      expect(getState().deckBuilder.name).toBe('My deck');
    });
  });
});
