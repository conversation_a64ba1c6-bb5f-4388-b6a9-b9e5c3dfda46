import {createTestingStore} from '@/src/client/Shared/specs/helpers/store/createTestingStore';
import {saveDeckDraft} from '@/src/client/DeckBuilding/application/commands/saveDeckDraft/saveDeckDraft';
import {FakeDeckDraftService} from '@/src/client/DeckBuilding/specs/helpers/fakes/FakeDeckDraftService';
import {PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards';

describe('saveDeckDraft', () => {
  describe('When called', () => {
    it('should persist the current deck', async () => {
      // Arrange
      const deckDraftService = new FakeDeckDraftService();
      const {dispatch} = createTestingStore({
        deckBuilder: {
          name: 'My deck',
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {card: PINOCCHIO_STRINGS_ATTACHED, quantity: 2},
          },
        },
      }, {deckDraftService});

      // Act
      await dispatch(saveDeckDraft());

      // Assert
      expect(deckDraftService.loadDraft()).toEqual({
        name: 'My deck',
        cards: [{cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 2}],
      });
    });
  });
});
