import {createTestingStore} from "@/src/client/Shared/testing/store/createTestingStore";
import {switchDeckBuilderView} from "@/src/client/DeckBuilding/application/commands/switchDeckBuilderView/switchDeckBuilderView";
import {getDeckBuilderView} from "@/src/client/DeckBuilding/application/queries/getDeckBuilderView/getDeckBuilderView";

import {FakeLocationService} from "@/src/client/Shared/infrastructure/services/location/FakeLocationService";

describe('switchDeckBuilderView', () => {
  describe('When the view is switched to deck', () => {
    it('should update the current view', async () => {
      // Arrange
      const locationService = new FakeLocationService();
      const {dispatch, getState} = createTestingStore({}, {locationService});

      // Act
      await dispatch(switchDeckBuilderView({view: 'deck'}));

      // Assert
      expect(getDeckBuilderView(getState())).toBe('deck');
    });
  });

  describe('When the view is switched to catalog', () => {
    it('should update the current view', async () => {
      // Arrange
      const locationService = new FakeLocationService();
      const {dispatch, getState} = createTestingStore({ deckBuilder: { view: 'deck' } }, {locationService});

      // Act
      await dispatch(switchDeckBuilderView({view: 'catalog'}));

      // Assert
      expect(getDeckBuilderView(getState())).toBe('catalog');
    });
  });
});
