import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {clearDeckDraft} from '@/src/client/DeckBuilding/application/commands/clearDeckDraft/clearDeckDraft';
import {FakeDeckDraftService} from '@/src/client/DeckBuilding/specs/helpers/fakes/FakeDeckDraftService';

describe('clearDeckDraft', () => {
  describe('When a draft exists', () => {
    it('should remove it from the service', async () => {
      // Arrange
      const deckDraftService = new FakeDeckDraftService({name: 'draft', cards: []});
      const {dispatch} = createTestingStore({}, {deckDraftService});

      // Act
      await dispatch(clearDeckDraft());

      // Assert
      expect(deckDraftService.hasDraft()).toBeFalse();
    });
  });
});
