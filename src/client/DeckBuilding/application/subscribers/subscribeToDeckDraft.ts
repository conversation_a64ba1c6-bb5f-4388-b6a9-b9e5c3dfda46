import {AnyAction, Store} from '@reduxjs/toolkit';
import {RootState, AppDispatch} from '@/src/client/Shared/appStore/appStore';
import {saveDeckDraft} from '@/src/client/DeckBuilding/application/commands/saveDeckDraft/saveDeckDraft';
import {initialDeckBuilderState} from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer';

export const subscribeToDeckDraft = (
  store: Store<RootState, AnyAction> & {dispatch: AppDispatch},
) => {
  const buildDraft = (state: RootState['deckBuilder']) => ({
    name: state.name,
    cards: Object.values(state.cardsInDeck).map(({card, quantity}) => ({
      cardId: card.id,
      quantity,
    })),
  });

  let previous = JSON.stringify(buildDraft(initialDeckBuilderState));
  store.subscribe(() => {
    const state = store.getState();
    const current = JSON.stringify(buildDraft(state.deckBuilder));
    if (current !== previous) {
      previous = current;
      store.dispatch(saveDeckDraft());
    }
  });
};
