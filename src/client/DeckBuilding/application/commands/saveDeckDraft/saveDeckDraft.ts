import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {ThunkExtra} from "@/src/client/Shared/store/appStore/thunkExtra";

export const saveDeckDraft = createAsyncThunk<void, void, {state: RootState; extra: ThunkExtra}>(
  'deckBuilder/saveDraft',
  async (_, {getState, extra: {deckDraftService}}) => {
    const draft = ({
      name: getState().deckBuilder.name,
      cards: Object.values(getState().deckBuilder.cardsInDeck).map(({card, quantity}) => ({
        cardId: card.id,
        quantity,
      })),
    });
    deckDraftService.saveDraft(draft);
  }
);
