import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState, ThunkExtra} from '@/src/client/Shared/appStore/appStore';
import {deckLoadedEvent} from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents';
import {getCatalogCardById} from '@/src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById';

export const loadDeckDraft = createAsyncThunk<void, void, {state: RootState; extra: ThunkExtra}>(
  'deckBuilder/loadDraft',
  async (_, {dispatch, getState, extra: {deckDraftService}}) => {
    const draft = deckDraftService.loadDraft();
    if (!draft) return;
    const state = getState();
    const cards = draft.cards.map(({cardId, quantity}) => ({
      card: getCatalogCardById(state, cardId),
      quantity,
    }));
    dispatch(deckLoadedEvent({name: draft.name ?? '', cards}));
  }
);
