'use client';

import {Flex, Heading, <PERSON>rollArea, SegmentedControl, Text, TextField,} from '@radix-ui/themes';
import DeckBuildingCard
  from '@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuildingCard/DeckBuildingCard';
import {MagnifyingGlassIcon} from '@radix-ui/react-icons';
import {useGameId} from '@/src/client/Shared/infrastructure/hooks/useGameId/useGameId';
import {useLocale} from '@/src/client/Shared/infrastructure/hooks/useLocale/useLocale';
import {useCardsByGameId} from '@/src/client/DeckBuilding/infrastructure/hooks/useCatalogCardsByGameId/useCardsByGameId';
import {memo, useMemo, useEffect, useState} from 'react';
import {useDeckBuilder} from '@/src/client/DeckBuilding/infrastructure/hooks/useDeckBuilder/useDeckBuilder';
import {useVirtualizer} from '@tanstack/react-virtual';
import {chunk} from "@/src/client/Shared/infrastructure/lib/chunk";
import {useResponsiveColumnCount} from "@/src/client/Shared/infrastructure/hooks/useResponsiveColumnCount";
import {useDebounce} from "@/src/client/Shared/infrastructure/hooks/useDebounce/useDebounce";
import TotalCardsInDeckCounter from "@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/TotalCardsInDeckCounter/TotalCardsInDeckCounter";

const CARD_HEIGHT = 384;
const GAP = 8;

const DeckBuilderCardsGridComponent = () => {
  const gameId = useGameId();
  const locale = useLocale();
  const {displayedCards, isLoading} = useCardsByGameId({gameId});
  const {
    deckCards,
    addCardToDeck,
    removeCard,
    showCardDetails,
    searchCatalog,
    search,
    view,
    switchView,
  } = useDeckBuilder();

  const [localSearch, setLocalSearch] = useState(search);
  const debouncedSearch = useDebounce(localSearch, 300);

  useEffect(() => {
    setLocalSearch(search);
  }, [search]);

  useEffect(() => {
    if (debouncedSearch !== search) {
      searchCatalog(debouncedSearch);
    }
  }, [debouncedSearch, search, searchCatalog]);

  const quantities = useMemo(() => {
    const map = new Map<string, number>();
    deckCards.forEach(c => {
      if (typeof c.quantity === 'number') {
        map.set(c.id, c.quantity);
      }
    });
    return map;
  }, [deckCards]);

  const {columnCount, containerRef} = useResponsiveColumnCount();
  const cardRows = useMemo(
    () => chunk(displayedCards, columnCount),
    [displayedCards, columnCount]
  );

  const rowVirtualizer = useVirtualizer({
    count: cardRows.length,
    getScrollElement: () => containerRef.current,
    estimateSize: () => CARD_HEIGHT + GAP,
    overscan: 4,
    measureElement: (el) => el.getBoundingClientRect().height,
  });

  useEffect(() => {
    rowVirtualizer.measure();
  }, [columnCount, rowVirtualizer]);

  const virtualRows = rowVirtualizer.getVirtualItems();
  const totalSize = rowVirtualizer.getTotalSize();

  return (
    <Flex direction="column" gap="3" className="h-full">
      <Flex justify="between" align="center">
        {view === 'catalog' && (
          <Flex direction="column" pl="1">
            <Heading size="4">Search results</Heading>
            <Text as="span" size="2" color="gray">
              {displayedCards.length}/{displayedCards.length} cards found
            </Text>
          </Flex>
        )}
        {view === 'deck' && (
          <TotalCardsInDeckCounter/>
        )}

        <Flex align="center" gap="2">
          <SegmentedControl.Root
            value={view}
            onValueChange={(value: 'catalog' | 'deck') => switchView(value)}
            size="2"
            radius="medium"
          >
            <SegmentedControl.Item value="catalog">Catalog</SegmentedControl.Item>
            <SegmentedControl.Item value="deck">Deck</SegmentedControl.Item>
          </SegmentedControl.Root>

          <TextField.Root
            placeholder="Search for cards..."
            size="2"
            mr="4"
            type="search"
            style={{width: '300px'}}
            value={localSearch}
            onChange={e => setLocalSearch(e.target.value)}
          >
            <TextField.Slot>
              <MagnifyingGlassIcon height="16" width="16"/>
            </TextField.Slot>
          </TextField.Root>
        </Flex>
      </Flex>

      {isLoading && (
        <Flex direction="column" align="center" p="4" className="h-full">
          <Text size="4" color="gray" align="center" mt="4">
            Loading cards...
          </Text>
        </Flex>
      )}

      {!isLoading && (
        <ScrollArea
          type="always"
          scrollbars="vertical"
          className="h-full"
          ref={containerRef}
        >
          <div
            style={{
              height: totalSize,
              position: 'relative',
              width: '100%',
            }}
          >
            {virtualRows.map(virtualRow => {
              const cardsForRow = cardRows[virtualRow.index] ?? [];

              return (
                <div
                  key={virtualRow.key}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    transform: `translateY(${virtualRow.start}px)`,
                    display: 'grid',
                    gridTemplateColumns: `repeat(${columnCount}, minmax(0, 1fr))`,
                    gap: `${GAP}px`,
                    width: '100%',
                    paddingRight: '16px',
                    alignItems: 'start',
                  }}
                >
                  {cardsForRow.map(card => (
                    <div key={card.id} style={{width: '100%'}}>
                      <DeckBuildingCard
                        card={card}
                        locale={locale}
                        quantity={
                          quantities.get(card.id) ?? card.minDeckQuantity
                        }
                        addCardToDeck={addCardToDeck}
                        removeCard={removeCard}
                        showCardDetails={showCardDetails}
                      />
                    </div>
                  ))}
                </div>
              );
            })}
          </div>
        </ScrollArea>
      )}
    </Flex>
  );
};

export default memo(DeckBuilderCardsGridComponent);
