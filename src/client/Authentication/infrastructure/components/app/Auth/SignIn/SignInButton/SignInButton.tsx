'use client';

import {GitHubLogoIcon} from '@radix-ui/react-icons';
import {type FC, memo} from 'react';
import {useSignInForm} from "@/src/client/Authentication/infrastructure/components/app/Auth/SignIn/SignInForm/useSignInForm";
import ShiningButton from "@/src/client/Shared/infrastructure/components/ShiningButton/ShiningButton";

const SignInButton: FC = () => {
  const {isLoading, handleSignIn} = useSignInForm();

  return (
    <ShiningButton loading={isLoading} size={{initial: '4', md: '3'}} onClick={handleSignIn} color="blue">
      <GitHubLogoIcon width="20" height="20"/>
      GitHub
    </ShiningButton>
  );
};

export default memo(SignInButton);
