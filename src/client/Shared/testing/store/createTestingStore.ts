import {configureStore} from "@reduxjs/toolkit";
import {rootInitialState, rootReducers, RootState} from "@/src/client/Shared/application/store/appStore";
import {deepMerge} from "@/src/client/Shared/helpers/DeepMerge/deepMerge";

import {LocationService} from "@/src/client/Shared/application/services/LocationService";
import {DeckDraftService} from "@/src/client/DeckBuilding/application/ports/DeckDraftService";
import {FakeLocationService} from "@/src/client/Shared/infrastructure/services/location/FakeLocationService";
import {FakeDeckDraftService} from "@/src/client/DeckBuilding/specs/helpers/fakes/FakeDeckDraftService";
import {subscribeToDeckDraft} from "@/src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft";
import {setDeckDraftService} from "@/src/client/Shared/infrastructure/store/reduxStore";

type OverrideState = Partial<{
  [K in keyof RootState]: Partial<RootState[K]>
}>;

export const createTestingStore = (
  overrides: OverrideState = {},
  extra?: { locationService?: LocationService; deckDraftService?: DeckDraftService; withDraftSubscriber?: boolean }
) => {
  const deckDraft = extra?.deckDraftService ?? new FakeDeckDraftService();
  setDeckDraftService(deckDraft);
  const store = configureStore({
    reducer: rootReducers,
    preloadedState: Object.fromEntries(
      Object.entries(rootInitialState).map(([key, initial]) => {
        const override = overrides[key as keyof RootState];
        const merged = override ? deepMerge(initial, override) : initial;
        return [key, merged];
      })
    ) as unknown as RootState,
    middleware: (getDefaultMiddleware) => {
      return getDefaultMiddleware({
        thunk: {
          extraArgument: {
            locationService: extra?.locationService ?? new FakeLocationService(),
            deckDraftService: deckDraft,
          },
        },
      });
    },
  });
  if (extra?.withDraftSubscriber) {
    subscribeToDeckDraft(store);
  }
  return store;
}
