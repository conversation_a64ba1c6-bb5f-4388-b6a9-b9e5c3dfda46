export const buildMatchUrl = (locale: string, matchId: string) => {
  return `/${locale}/matches/${matchId}`;
};

export const buildGameListUrl = (locale: string) => {
  return `/${locale}/games`;
};

export const buildGameUrl = (locale: string, gameId: string) => {
  return `/${locale}/games/${gameId}`;
};

export const buildPlayGameUrl = (locale: string, gameId: string) => {
  return `/${locale}/games/${gameId}/play`;
};

export const buildSignInUrl = (locale: string) => {
  return `/${locale}/signin`;
};

export const buildAccessDeniedUrl = (locale: string) => {
  return `/${locale}/access-denied`;
};

export const buildEditDeckUrl = (
  locale: string,
  gameId: string,
  deckId: string,
) => {
  return `/${locale}/games/${gameId}/deck-builder/${deckId}`;
};

export const buildEditDeckUrlWithQuery = (
  locale: string,
  gameId: string,
  deckId: string,
  query: string,
) => {
  const base = buildEditDeckUrl(locale, gameId, deckId);
  return query ? `${base}${query}` : base;
};
