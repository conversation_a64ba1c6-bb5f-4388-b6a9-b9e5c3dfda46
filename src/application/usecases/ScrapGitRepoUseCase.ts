import { IGitService } from "@/application/ports/IGitService";
import { IFileSystemService } from "@/application/ports/IFileSystemService";
import { IScrapPresenter } from "@/application/ports/IScrapPresenter";
import { ScrapGitRepoRequest } from "@/application/usecases/ScrapGitRepoRequest";
import { IGitRepoRepository } from "@/application/ports/IGitRepoRepository";
import { GitRepo } from "@/domain/GitRepo";

export class ScrapGitRepoUseCase {
    constructor(
        private readonly gitService: IGitService,
        private readonly fileSystemService: IFileSystemService,
        private readonly presenter: IScrapPresenter,
        private readonly gitRepoRepository: IGitRepoRepository
    ) {}

    async execute(request: ScrapGitRepoRequest): Promise<void> {
        const { repositoryUrl, destination } = request;

        await this.fileSystemService.deleteDirectory(destination);

        this.presenter.startCloningProject();
        await this.gitService.cloneRepository(repositoryUrl, destination);
        this.presenter.finishCloningProject();

        const gitRepo = new GitRepo(repositoryUrl, destination);
        const tree = await this.gitService.getRepositoryTree(destination);

        await this.fileSystemService.deleteDirectory(destination);

        const treeFileContent = tree
            .filter(file => request.folders.some(folder => file.name.startsWith(folder)))
            .map(file => `==== File: ${file.name}\n${file.content}\n`)
            .join("\n");

        console.log("===========================");
        console.log(treeFileContent);
        console.log("===========================");

        const contentChunks = this.fileSystemService.splitContent(treeFileContent);

        for (let i = 0; i < contentChunks.length; i++) {
            const chunkFilePath = `${gitRepo.getTreeFilePath().replace('.txt', '')}_${i + 1}.txt`;
            await this.fileSystemService.writeFile(chunkFilePath, contentChunks[i]);
        }

        this.gitRepoRepository.save(gitRepo);
        this.presenter.presentTree(`${destination}/repository-tree_*.txt`);
    }
}
