import type { DataModel } from '@/convex/_generated/dataModel';
import type { AuthenticationGateway, User } from '@/src/server/application/ports/AuthenticationGateway';
import { UserNotAuthenticatedError } from '@/src/server/domain/User/errors/UserNotAuthenticatedError';
import { UserNotRegisteredError } from '@/src/server/domain/User/errors/UserNotRegisteredError';
import type { GenericMutationCtx, GenericQueryCtx } from 'convex/server';

export class ConvexAuthenticationGateway implements AuthenticationGateway {
  private readonly ctx: GenericMutationCtx<DataModel> | GenericQueryCtx<DataModel>;

  constructor(ctx: GenericMutationCtx<DataModel> | GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async getAuthenticatedUser(): Promise<User> {
    const identity = await this.ctx.auth.getUserIdentity();

    if (identity === null) {
      throw new UserNotAuthenticatedError();
    }

    const convexId = identity.subject.split('|')[0];

    const user = await this.ctx.db
      .query('craftUsers')
      .filter((q) => q.eq(q.field('convexUserId'), convexId))
      .first();

    if (!user) {
      throw new UserNotRegisteredError();
    }

    return {
      id: user.craftUserId,
      authId: convexId,
      name: user.name,
      image: user.avatar,
      email: user.email!,
      createdAt: user.createdAt,
      fallback: user.name.slice(0, 1).toUpperCase(),
    };
  }
}
