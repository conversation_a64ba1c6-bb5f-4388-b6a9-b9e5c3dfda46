import type {AuthenticationGateway, User} from '@/src/server/application/ports/AuthenticationGateway';
import type {GenericMutationCtx, GenericQueryCtx} from "convex/server";
import type {DataModel} from "@/convex/_generated/dataModel";
import {BotNotRegisteredError} from "@/src/server/domain/Bot/errors/BotNotRegisteredError";
import {OrphanBotError} from "@/src/server/domain/Bot/errors/OrphanBotError";

export class BotAuthenticationGateway implements AuthenticationGateway {
  private readonly ctx: GenericMutationCtx<DataModel> | GenericQueryCtx<DataModel>;
  private readonly apiKey: string;

  constructor(ctx: GenericMutationCtx<DataModel> | GenericQueryCtx<DataModel>, apiKey: string) {
    this.ctx = ctx;
    this.apiKey = apiKey;
  }

  async getAuthenticatedUser(): Promise<User> {
    const bot = await this.ctx.db
      .query('bots')
      .filter((q) => q.eq(q.field('apiKey'), this.apiKey))
      .first();

    if (!bot) {
      throw new BotNotRegisteredError();
    }

    const user = await this.ctx.db
      .query('craftUsers')
      .filter((q) => q.eq(q.field('craftUserId'), bot.ownerId))
      .first();

    if (!user) {
      throw new OrphanBotError();
    }

    return {
      id: bot.botId,
      authId: bot.botId,
      name: `${user.name}'s Craft Bot`,
      email: '<EMAIL>',
      createdAt: Number(bot.createdAt),
      image: `https://api.dicebear.com/9.x/bottts/svg?seed=${bot.botId}`,
      fallback: 'B',
    };
  }
}
