import type { TranslationServicePort } from '@/src/server/application/ports/TranslationServicePort';
import { getTranslations } from 'next-intl/server';

export class NextIntlTranslationService implements TranslationServicePort {
  async getTranslations(namespace: string): Promise<(key: string) => string> {
    const t = await getTranslations(namespace);
    return (key: string, params?: Record<string, string>) => t(key, params);
  }
}
