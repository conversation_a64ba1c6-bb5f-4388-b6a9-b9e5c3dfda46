import type { CryptoPort } from '@/src/server/application/ports/CryptoPort';
import type { IdentityProvider } from '@/src/server/application/ports/IdentityProvider';

export class UuidIdentityProvider implements IdentityProvider {
  private readonly crypto: CryptoPort;

  constructor(crypto: CryptoPort) {
    this.crypto = crypto;
  }

  generateId(): string {
    return this.crypto.randomUUID();
  }
}
