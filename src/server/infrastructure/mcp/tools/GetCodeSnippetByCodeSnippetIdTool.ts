import {api} from "@/convex/_generated/api";
import {ConvexHttpClient} from "convex/browser";
import {Context, UserError} from "fastmcp";
import {z} from "zod";

export class GetCodeSnippetByCodeSnippetIdTool {
  private readonly convexClient: ConvexHttpClient;
  public readonly name = 'get-code-snippet-by-id';
  public readonly description = 'Get a code snippet by its ID';
  public readonly parameters = z.object({
    codeSnippetId: z.string().describe('The ID of the code snippet to retrieve'),
  });

  constructor(convexClient: ConvexHttpClient) {
    this.convexClient = convexClient;
  }

  public async execute(args: z.infer<typeof this.parameters>, {log, session}: Context<{ apiKey: string }>) {
    try {
      log.info("Getting code snippet", {codeSnippetId: args.codeSnippetId});

      const codeSnippet = await this.convexClient.query(
        api.queries.bot.getCodeSnippetByCodeSnippetId.endpoint,
        {
          codeSnippetId: args.codeSnippetId,
          apiKey: session!.apiKey
        }
      );

      if (!codeSnippet) {
        log.warn("Code snippet not found", {codeSnippetId: args.codeSnippetId});
        return `⚠️ Code snippet with ID ${args.codeSnippetId} not found`;
      }

      log.info("Code snippet retrieved", {codeSnippet});

      return `✅ Code snippet retrieved successfully: ${JSON.stringify(codeSnippet)}`;
    } catch (err: any) {
      log.error("Failed to get code snippet", {error: err.message});
      throw new UserError(`Failed to get code snippet: ${err.message || 'Unknown error'}`);
    }
  }
}
