import {api} from "@/convex/_generated/api";
import {ConvexHttpClient} from "convex/browser";
import {Context, UserError} from "fastmcp";
import {z} from "zod";

export class GetCodeSnippetsByCodeReviewIdTool {
  private readonly convexClient: ConvexHttpClient;
  public readonly name = 'get-code-snippets-by-code-review-id';
  public readonly description = 'Get the code snippets for a code review';
  public readonly parameters = z.object({
    codeReviewId: z.string().describe('The ID of the code review to get the code snippets from'),
  });

  constructor(convexClient: ConvexHttpClient) {
    this.convexClient = convexClient;
  }

  public async execute(args: z.infer<typeof this.parameters>, {log, session}: Context<{ apiKey: string }>) {
    try {
      log.info("Getting code snippets", {codeReviewId: args.codeReviewId});

      const codeSnippets = await this.convexClient.query(
        api.queries.bot.getCodeSnippetsByCodeReviewId.endpoint,
        {
          codeReviewId: args.codeReviewId,
          apiKey: session!.apiKey
        }
      );

      log.info("Code snippets retrieved", {codeSnippets});

      return `✅ Code snippets retrieved successfully: ${JSON.stringify(codeSnippets)}`;
    } catch (err: any) {
      log.error("Failed to get code snippets", {error: err.message});
      throw new UserError(`Failed to get code snippets: ${err.message || 'Unknown error'}`);
    }
  }
}