import {api} from "@/convex/_generated/api";
import {ConvexHttpClient} from "convex/browser";
import {Context, UserError} from "fastmcp";
import {z} from "zod";

export class GetFeedbacksByCodeSnippetIdTool {
  private readonly convexClient: ConvexHttpClient;
  public readonly name = 'get-feedbacks-by-code-snippet-id';
  public readonly description = 'Get all feedbacks for a specific code snippet';
  public readonly parameters = z.object({
    codeSnippetId: z.string().describe('The ID of the code snippet to get feedbacks for'),
    apiKey: z.string().describe('API key for authentication'),
  });

  constructor(convexClient: ConvexHttpClient) {
    this.convexClient = convexClient;
  }

  public async execute(args: z.infer<typeof this.parameters>, {log, session}: Context<{ apiKey: string }>) {
    try {
      log.info("Getting feedbacks for code snippet", {codeSnippetId: args.codeSnippetId});

      const result = await this.convexClient.query(
        api.queries.bot.getFeedbacksByCodeSnippetId.endpoint,
        {
          codeSnippetId: args.codeSnippetId,
          apiKey: session!.apiKey
        }
      );

      log.info("Feedbacks retrieved", {
        codeSnippetId: args.codeSnippetId,
        feedbackCount: result.feedbacks.length
      });

      return `✅ Retrieved ${result.feedbacks.length} feedbacks for code snippet ${args.codeSnippetId}: ${JSON.stringify(result.feedbacks)}`;
    } catch (err: any) {
      log.error("Failed to get feedbacks", {error: err.message});
      throw new UserError(`Failed to get feedbacks: ${err.message || 'Unknown error'}`);
    }
  }
}
