import {z} from "zod";
import {ConvexHttpClient} from 'convex/browser';
import {api} from '@/convex/_generated/api';
import {Context, UserError} from "fastmcp";

export class StartCodeReviewTool {
  private readonly convexClient: ConvexHttpClient;
  public readonly name = 'start-code-review';
  public readonly description = 'Start a code review from its ID';
  public readonly parameters = z.object({
    apiKey: z.string().describe('The API key to authenticate the request'),
    codeReviewId: z.string().describe('The ID (uuid) of the code review to start'),
  });

  constructor(convexClient: ConvexHttpClient) {
    this.convexClient = convexClient;
  }

  public async execute(args: z.infer<typeof this.parameters>, {log, session}: Context<{ apiKey: string }>) {
    try {
      log.info("Starting code review", {codeReviewId: args.codeReviewId});

      await this.convexClient.mutation(
        api.mutations.bot.startCodeReview.endpoint,
        {
          codeReviewId: args.codeReviewId,
          apiKey: session!.apiKey
        }
      );

      log.info("Code review started", {codeReviewId: args.codeReviewId});

      return `✅ Code review started successfully for ID: ${args.codeReviewId}`;
    } catch (err: any) {
      log.error("Failed to start code review", {error: err.message});
      throw new UserError(`Failed to start code review: ${err.message || 'Unknown error'}`);
    }
  }
}