import {z} from "zod";
import {api} from "@/convex/_generated/api";
import {ConvexHttpClient} from "convex/browser";
import {Context, UserError} from "fastmcp";

export class PublishCommentTool {
  private readonly convexClient: ConvexHttpClient;
  public readonly name = 'publish-comment';
  public readonly description = 'Publish a comment on a feedback';
  public readonly parameters = z.object({
    feedbackId: z.string().describe('ID of the feedback to comment on'),
    content: z.string().describe('Content of the comment'),
    parentId: z.string().optional().describe('ID of the parent comment (for replies)'),
    apiKey: z.string().describe('API key for authentication'),
  });

  constructor(convexClient: ConvexHttpClient) {
    this.convexClient = convexClient;
  }

  public async execute(args: z.infer<typeof this.parameters>, {log, session}: Context<{ apiKey: string }>) {
    try {
      log.info("Publishing comment", {
        feedbackId: args.feedbackId,
        hasParent: !!args.parentId
      });

      const commentId = await this.convexClient.mutation(
        api.mutations.bot.publishComment.endpoint,
        {
          feedbackId: args.feedbackId,
          content: args.content,
          parentId: args.parentId,
          apiKey: session!.apiKey
        }
      );

      log.info("Comment published", {commentId});

      return `✅ Comment published successfully with ID: ${commentId}`;
    } catch (err: any) {
      log.error("Failed to publish comment", {error: err.message});
      throw new UserError(`Failed to publish comment: ${err.message || 'Unknown error'}`);
    }
  }
}