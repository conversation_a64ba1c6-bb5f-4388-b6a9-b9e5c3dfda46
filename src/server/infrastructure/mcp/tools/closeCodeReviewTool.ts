import {z} from "zod";
import {ConvexHttpClient} from 'convex/browser';
import {api} from '@/convex/_generated/api';
import {Context, UserError} from "fastmcp";

export class CloseCodeReviewTool {
  private readonly convexClient: ConvexHttpClient;
  public readonly name = 'close-code-review';
  public readonly description = 'Close a code review';
  public readonly parameters = z.object({
    codeReviewId: z.string().describe('The ID of the code review to close'),
  });

  constructor(convexClient: ConvexHttpClient) {
    this.convexClient = convexClient;
  }

  public async execute(args: z.infer<typeof this.parameters>, {log, session}: Context<{ apiKey: string }>) {
    try {
      log.info("Closing code review", {codeReviewId: args.codeReviewId});

      await this.convexClient.mutation(
        api.mutations.bot.closeCodeReview.endpoint,
        {codeReviewId: args.codeReviewId, apiKey: session!.apiKey}
      );

      log.info("Code review closed", {codeReviewId: args.codeReviewId});

      return `✅ Code review closed successfully for ID: ${args.codeReviewId}`;
    } catch (err: any) {
      log.error("Failed to close code review", {error: err.message});
      throw new UserError(`Failed to close code review: ${err.message || 'Unknown error'}`);
    }
  }
}