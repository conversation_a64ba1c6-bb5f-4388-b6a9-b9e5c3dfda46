import type { CommentRepository } from '@/src/server/application/ports/CommentRepository';
import type { Comment } from '@/src/server/domain/Comment/Comment';
import { CommentNotFoundError } from '@/src/server/domain/Comment/errors/CommentNotFoundError';
import { Result } from '@evyweb/simple-ddd-toolkit';

export class InMemoryCommentRepository implements CommentRepository {
  private readonly comments: Comment[] = [];

  async findById(commentId: string): Promise<Result<Comment, CommentNotFoundError>> {
    const comment = this.comments.find((c) => c.id() === commentId);
    if (!comment) {
      return Result.fail(new CommentNotFoundError());
    }
    return Result.ok(comment);
  }

  async save(comment: Comment): Promise<void> {
    const index = this.comments.findIndex((c) => c.id() === comment.id());
    if (index !== -1) {
      this.comments[index] = comment;
    } else {
      this.comments.push(comment);
    }
  }

  async delete(commentId: string): Promise<void> {
    const index = this.comments.findIndex((c) => c.id() === commentId);
    if (index !== -1) {
      const comment = this.comments[index];
      comment.delete();
      this.comments[index] = comment;
    }
  }
}
