import type { DataModel } from '@/convex/_generated/dataModel';
import type { CodeReviewParticipationRepository } from '@/src/server/application/ports/CodeReviewParticipationRepository';
import { CodeReviewParticipation } from '@/src/server/domain/CodeReviewParticipation/CodeReviewParticipation';
import { Result } from '@evyweb/simple-ddd-toolkit';
import type { GenericDatabaseWriter } from 'convex/server';

export class ConvexCodeReviewParticipationRepository implements CodeReviewParticipationRepository {
  private readonly db: GenericDatabaseWriter<DataModel>;

  constructor(db: GenericDatabaseWriter<DataModel>) {
    this.db = db;
  }

  async save(participation: CodeReviewParticipation): Promise<void> {
    await this.db.insert('codeReviewParticipation', {
      codeReviewParticipationId: participation.id(),
      userId: participation.get('userId').get('value'),
      codeReviewId: participation.get('codeReviewId').get('value'),
      date: participation.get('date').get('value'),
    });
  }

  async getAll(): Promise<CodeReviewParticipation[]> {
    throw new Error('not implemented');
  }

  async findByUserIdAndCodeReviewIdAndDate(
    userId: string,
    codeReviewId: string,
    date: string
  ): Promise<Result<CodeReviewParticipation, Error>> {
    const participation = await this.db
      .query('codeReviewParticipation')
      .withIndex('by_userId_and_codeReviewId_and_date', (q) =>
        q.eq('userId', userId).eq('codeReviewId', codeReviewId).eq('date', date)
      )
      .first();

    if (!participation) {
      return Result.fail(new Error('Participation not found'));
    }

    return Result.ok(
      CodeReviewParticipation.create({
        id: participation.codeReviewParticipationId,
        userId: participation.userId,
        codeReviewId: participation.codeReviewId,
        date: participation.date,
      }).getValue()
    );
  }
}
