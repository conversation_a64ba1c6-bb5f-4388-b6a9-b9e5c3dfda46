import type { DataModel } from '@/convex/_generated/dataModel';
import type { CodeSnippetRepository } from '@/src/server/application/ports/CodeSnippetRepository';
import { CodeSnippet } from '@/src/server/domain/CodeSnippet/CodeSnippet';
import { asyncMap } from 'convex-helpers';
import { getOneFrom } from 'convex-helpers/server/relationships';
import type { GenericDatabaseWriter } from 'convex/server';

export class ConvexCodeSnippetRepository implements CodeSnippetRepository {
  private readonly db: GenericDatabaseWriter<DataModel>;

  constructor(db: GenericDatabaseWriter<DataModel>) {
    this.db = db;
  }

  async save(codeSnippetToSave: CodeSnippet): Promise<void> {
    const existingCodeSnippet = await getOneFrom(this.db, 'codeSnippets', 'by_codeSnippetId', codeSnippetToSave.id());

    if (existingCodeSnippet === null) {
      await this.db.insert('codeSnippets', {
        codeSnippetId: codeSnippetToSave.id(),
        codeReviewId: codeSnippetToSave.get('codeReviewId').get('value'),
        content: codeSnippetToSave.get('content').get('value'),
        filename: codeSnippetToSave.get('filename').get('value'),
        language: codeSnippetToSave.get('language').get('value'),
      });
    } else {
      await this.db.replace(existingCodeSnippet._id, {
        ...existingCodeSnippet,
        content: codeSnippetToSave.get('content').get('value'),
        filename: codeSnippetToSave.get('filename').get('value'),
        language: codeSnippetToSave.get('language').get('value'),
      });
    }
  }

  async getAll(): Promise<CodeSnippet[]> {
    throw new Error('not implemented');
  }

  async saveAll(codeSnippets: CodeSnippet[]): Promise<void> {
    await asyncMap(codeSnippets, (codeSnippet) => this.save(codeSnippet));
  }

  async findByCodeSnippetId(id: string): Promise<CodeSnippet | null> {
    const existingCodeSnippet = await getOneFrom(this.db, 'codeSnippets', 'by_codeSnippetId', id);

    if (existingCodeSnippet === null) {
      return null;
    }

    return CodeSnippet.fromRaw({
      id: existingCodeSnippet.codeSnippetId,
      filename: existingCodeSnippet.filename,
      content: existingCodeSnippet.content,
      language: existingCodeSnippet.language,
      codeReviewId: existingCodeSnippet.codeReviewId,
    });
  }
}
