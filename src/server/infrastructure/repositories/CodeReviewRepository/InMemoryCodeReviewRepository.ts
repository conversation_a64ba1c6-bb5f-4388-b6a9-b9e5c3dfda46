import type { CodeReviewRepository } from '@/src/server/application/ports/CodeReviewRepository';
import type { CodeReview } from '@/src/server/domain/CodeReview/CodeReview';
import { CodeReviewNotFoundError } from '@/src/server/domain/CodeReview/errors/CodeReviewNotFoundError';
import { Result } from '@evyweb/simple-ddd-toolkit';

export class InMemoryCodeReviewRepository implements CodeReviewRepository {
  private codeReviews: CodeReview[] = [];

  async getAll(): Promise<CodeReview[]> {
    return this.codeReviews;
  }

  async save(codeReview: CodeReview): Promise<void> {
    const index = this.codeReviews.findIndex((c) => c.get('id').equals(codeReview.get('id')));
    if (index !== -1) {
      this.codeReviews[index] = codeReview;
    } else {
      this.codeReviews.push(codeReview);
    }
  }

  async findById(id: string): Promise<Result<CodeReview, CodeReviewNotFoundError>> {
    const codeReview = this.codeReviews.find((c) => c.get('id').get('value') === id);
    if (!codeReview) {
      return Result.fail(new CodeReviewNotFoundError());
    }

    return Result.ok(codeReview);
  }
}
