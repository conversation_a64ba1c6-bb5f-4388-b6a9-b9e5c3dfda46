import type { DataModel } from '@/convex/_generated/dataModel';
import type { CodeReviewRepository } from '@/src/server/application/ports/CodeReviewRepository';
import { CodeReview } from '@/src/server/domain/CodeReview/CodeReview';
import { CodeReviewNotFoundError } from '@/src/server/domain/CodeReview/errors/CodeReviewNotFoundError';
import { Result } from '@evyweb/simple-ddd-toolkit';
import { getOneFrom } from 'convex-helpers/server/relationships';
import type { GenericDatabaseWriter } from 'convex/server';

export class ConvexCodeReviewRepository implements CodeReviewRepository {
  private readonly db: GenericDatabaseWriter<DataModel>;

  constructor(db: GenericDatabaseWriter<DataModel>) {
    this.db = db;
  }

  async save(codeReviewToSave: CodeReview): Promise<void> {
    const existingCodeReview = await getOneFrom(this.db, 'codeReviews', 'by_codeReviewId', codeReviewToSave.id());

    const updatedValues = {
      title: codeReviewToSave.get('title').get('value'),
      authorId: codeReviewToSave.get('authorId'),
      status: codeReviewToSave.get('status'),
      participantIds: codeReviewToSave.get('participantIds'),
      visibility: codeReviewToSave.get('visibility'),
    };

    if (existingCodeReview === null) {
      await this.db.insert('codeReviews', {
        codeReviewId: codeReviewToSave.id(),
        ...updatedValues,
      });
    } else {
      await this.db.replace(existingCodeReview._id, {
        ...existingCodeReview,
        ...updatedValues,
      });
    }
  }

  async getAll(): Promise<CodeReview[]> {
    throw new Error('not implemented');
  }

  async findById(id: string): Promise<Result<CodeReview, CodeReviewNotFoundError>> {
    const codeReviewDocument = await getOneFrom(this.db, 'codeReviews', 'by_codeReviewId', id);
    if (codeReviewDocument) {
      const codeReview = CodeReview.fromRaw({
        id: codeReviewDocument.codeReviewId,
        title: codeReviewDocument.title,
        authorId: codeReviewDocument.authorId,
        status: codeReviewDocument.status,
        visibility: codeReviewDocument.visibility,
        participantIds: codeReviewDocument.participantIds,
      });

      return Result.ok(codeReview);
    }

    return Result.fail(new CodeReviewNotFoundError());
  }
}
