import type { DataModel } from '@/convex/_generated/dataModel';
import type { TeamInvitationRepository } from '@/src/server/application/ports/TeamInvitationRepository';
import { TeamInvitation, type TeamInvitationStatus } from '@/src/server/domain/TeamInvitation/TeamInvitation';
import { TeamInvitationNotFoundError } from '@/src/server/domain/TeamInvitation/errors/TeamInvitationNotFoundError';
import { TeamInvitationId } from '@/src/server/domain/TeamInvitation/valueObjects/TeamInvitationId';
import { Result } from '@evyweb/simple-ddd-toolkit';
import { getOneFrom } from 'convex-helpers/server/relationships';
import type { GenericDatabaseWriter } from 'convex/server';

export class ConvexTeamInvitationRepository implements TeamInvitationRepository {
  private readonly db: GenericDatabaseWriter<DataModel>;

  constructor(db: GenericDatabaseWriter<DataModel>) {
    this.db = db;
  }

  async findById(invitationId: string): Promise<Result<TeamInvitation, TeamInvitationNotFoundError>> {
    const invitationDocument = await getOneFrom(this.db, 'teamInvitations', 'by_teamInvitationId', invitationId);

    if (!invitationDocument) {
      return Result.fail(new TeamInvitationNotFoundError());
    }

    return Result.ok(
      TeamInvitation.create({
        id: TeamInvitationId.createFrom(invitationDocument.teamInvitationId),
        teamId: invitationDocument.teamId,
        email: invitationDocument.email,
        status: invitationDocument.status as TeamInvitationStatus,
        invitedBy: invitationDocument.invitedBy,
      }).getValue()
    );
  }

  async findByEmailAndTeamId(
    email: string,
    teamId: string
  ): Promise<Result<TeamInvitation, TeamInvitationNotFoundError>> {
    const invitationDocument = await this.db
      .query('teamInvitations')
      .withIndex('by_teamId_and_email', (q) => q.eq('teamId', teamId).eq('email', email))
      .first();

    if (!invitationDocument) {
      return Result.fail(new TeamInvitationNotFoundError());
    }

    return Result.ok(
      TeamInvitation.create({
        id: TeamInvitationId.createFrom(invitationDocument.teamInvitationId),
        teamId: invitationDocument.teamId,
        email: invitationDocument.email,
        status: invitationDocument.status as TeamInvitationStatus,
        invitedBy: invitationDocument.invitedBy,
      }).getValue()
    );
  }

  async save(invitation: TeamInvitation): Promise<void> {
    const existingInvitation = await getOneFrom(
      this.db,
      'teamInvitations',
      'by_teamInvitationId',
      invitation.get('id').get('value')
    );

    if (existingInvitation === null) {
      const now = new Date().toISOString();
      await this.db.insert('teamInvitations', {
        teamInvitationId: invitation.get('id').get('value'),
        teamId: invitation.get('teamId'),
        email: invitation.get('email'),
        status: invitation.get('status'),
        invitedBy: invitation.get('invitedBy'),
        createdAt: now,
      });
    } else {
      await this.db.replace(existingInvitation._id, {
        ...existingInvitation,
        status: invitation.get('status'),
      });
    }
  }
}
