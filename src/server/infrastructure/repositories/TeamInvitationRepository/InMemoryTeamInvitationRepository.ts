import type { TeamInvitationRepository } from '@/src/server/application/ports/TeamInvitationRepository';
import type { TeamInvitation } from '@/src/server/domain/TeamInvitation/TeamInvitation';
import { TeamInvitationNotFoundError } from '@/src/server/domain/TeamInvitation/errors/TeamInvitationNotFoundError';
import { Result } from '@evyweb/simple-ddd-toolkit';

export class InMemoryTeamInvitationRepository implements TeamInvitationRepository {
  private readonly invitations: TeamInvitation[] = [];

  async findById(invitationId: string): Promise<Result<TeamInvitation, TeamInvitationNotFoundError>> {
    const invitation = this.invitations.find((i) => i.get('id').get('value') === invitationId);

    if (!invitation) {
      return Result.fail(new TeamInvitationNotFoundError());
    }

    return Result.ok(invitation);
  }

  async findByEmailAndTeamId(
    email: string,
    teamId: string
  ): Promise<Result<TeamInvitation, TeamInvitationNotFoundError>> {
    const invitation = this.invitations.find((i) => i.get('email') === email && i.get('teamId') === teamId);

    if (!invitation) {
      return Result.fail(new TeamInvitationNotFoundError());
    }

    return Result.ok(invitation);
  }

  async save(invitation: TeamInvitation): Promise<void> {
    const index = this.invitations.findIndex((i) => i.get('id').get('value') === invitation.get('id').get('value'));

    if (index !== -1) {
      this.invitations[index] = invitation;
    } else {
      this.invitations.push(invitation);
    }
  }
}
