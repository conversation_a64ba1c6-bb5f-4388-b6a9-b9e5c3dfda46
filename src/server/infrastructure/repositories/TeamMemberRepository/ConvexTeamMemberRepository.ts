import type { DataModel } from '@/convex/_generated/dataModel';
import type { TeamMemberRepository } from '@/src/server/application/ports/TeamMemberRepository';
import { TeamMember } from '@/src/server/domain/TeamMember/TeamMember';
import { TeamMemberNotFoundError } from '@/src/server/domain/TeamMember/errors/TeamMemberNotFoundError';
import { TeamMemberId } from '@/src/server/domain/TeamMember/valueObjects/TeamMemberId';
import { Result } from '@evyweb/simple-ddd-toolkit';
import { getOneFrom } from 'convex-helpers/server/relationships';
import type { GenericDatabaseWriter } from 'convex/server';

export class ConvexTeamMemberRepository implements TeamMemberRepository {
  private readonly db: GenericDatabaseWriter<DataModel>;

  constructor(db: GenericDatabaseWriter<DataModel>) {
    this.db = db;
  }

  async findById(teamMemberId: string): Promise<Result<TeamMember, TeamMemberNotFoundError>> {
    const teamMemberDocument = await getOneFrom(this.db, 'teamMembers', 'by_teamMemberId', teamMemberId);

    if (!teamMemberDocument) {
      return Result.fail(new TeamMemberNotFoundError());
    }

    return Result.ok(
      TeamMember.create({
        id: TeamMemberId.createFrom(teamMemberDocument.teamMemberId),
        teamId: teamMemberDocument.teamId,
        userId: teamMemberDocument.userId,
        addedBy: teamMemberDocument.addedBy,
        createdAt: teamMemberDocument.createdAt,
      }).getValue()
    );
  }

  async findByTeamIdAndUserId(teamId: string, userId: string): Promise<Result<TeamMember, TeamMemberNotFoundError>> {
    const teamMemberDocument = await this.db
      .query('teamMembers')
      .withIndex('by_teamId_and_userId', (q) => q.eq('teamId', teamId).eq('userId', userId))
      .first();

    if (!teamMemberDocument) {
      return Result.fail(new TeamMemberNotFoundError());
    }

    return Result.ok(
      TeamMember.create({
        id: TeamMemberId.createFrom(teamMemberDocument.teamMemberId),
        teamId: teamMemberDocument.teamId,
        userId: teamMemberDocument.userId,
        addedBy: teamMemberDocument.addedBy,
        createdAt: teamMemberDocument.createdAt,
      }).getValue()
    );
  }

  async save(teamMember: TeamMember): Promise<void> {
    const existingTeamMember = await getOneFrom(this.db, 'teamMembers', 'by_teamMemberId', teamMember.id());

    if (existingTeamMember === null) {
      await this.db.insert('teamMembers', {
        teamMemberId: teamMember.id(),
        teamId: teamMember.get('teamId'),
        userId: teamMember.get('userId'),
        addedBy: teamMember.get('addedBy'),
        createdAt: teamMember.get('createdAt'),
      });
    } else {
      await this.db.replace(existingTeamMember._id, {
        ...existingTeamMember,
        teamId: teamMember.get('teamId'),
        userId: teamMember.get('userId'),
        addedBy: teamMember.get('addedBy'),
        createdAt: teamMember.get('createdAt'),
      });
    }
  }
}
