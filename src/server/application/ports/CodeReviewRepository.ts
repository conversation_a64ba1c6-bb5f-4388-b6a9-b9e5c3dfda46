import type { CodeReview } from '@/src/server/domain/CodeReview/CodeReview';
import type { CodeReviewNotFoundError } from '@/src/server/domain/CodeReview/errors/CodeReviewNotFoundError';
import type { Result } from '@evyweb/simple-ddd-toolkit';

export interface CodeReviewRepository {
  getAll(): Promise<CodeReview[]>;

  save(codeReview: CodeReview): Promise<void>;

  findById(id: string): Promise<Result<CodeReview, CodeReviewNotFoundError>>;
}
