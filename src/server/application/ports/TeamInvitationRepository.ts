import type { TeamInvitation } from '@/src/server/domain/TeamInvitation/TeamInvitation';
import type { TeamInvitationNotFoundError } from '@/src/server/domain/TeamInvitation/errors/TeamInvitationNotFoundError';
import type { Result } from '@evyweb/simple-ddd-toolkit';

export interface TeamInvitationRepository {
  findById(invitationId: string): Promise<Result<TeamInvitation, TeamInvitationNotFoundError>>;

  findByEmailAndTeamId(email: string, teamId: string): Promise<Result<TeamInvitation, TeamInvitationNotFoundError>>;

  save(invitation: TeamInvitation): Promise<void>;
}
