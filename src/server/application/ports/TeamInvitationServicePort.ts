import type { NotTeamOwnerError } from '@/src/server/domain/Team/errors/NotTeamOwnerError';
import type { TeamNotFoundError } from '@/src/server/domain/Team/errors/TeamNotFoundError';
import type { EmailMismatchError } from '@/src/server/domain/TeamInvitation/errors/EmailMismatchError';
import type { InvitationNotPendingError } from '@/src/server/domain/TeamInvitation/errors/InvitationNotPendingError';
import type { TeamInvitationAlreadyExistsError } from '@/src/server/domain/TeamInvitation/errors/TeamInvitationAlreadyExistsError';
import type { TeamInvitationNotFoundError } from '@/src/server/domain/TeamInvitation/errors/TeamInvitationNotFoundError';
import type { TeamMemberAlreadyExistsError } from '@/src/server/domain/TeamMember/errors/TeamMemberAlreadyExistsError';
import type { Result } from '@evyweb/simple-ddd-toolkit';

export interface TeamInvitationServicePort {
  canSendInvitation(params: {
    currentUserId: string;
    teamId: string;
    email: string;
  }): Promise<Result<void, TeamNotFoundError | NotTeamOwnerError | TeamInvitationAlreadyExistsError>>;

  canAcceptInvitation(params: {
    currentUserId: string;
    invitationId: string;
    userEmail: string;
  }): Promise<
    Result<
      void,
      TeamInvitationNotFoundError | InvitationNotPendingError | EmailMismatchError | TeamMemberAlreadyExistsError
    >
  >;
}
