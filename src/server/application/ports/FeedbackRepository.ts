import type { Feedback } from '@/src/server/domain/Feedback/Feedback';
import type { FeedbackNotFoundError } from '@/src/server/domain/Feedback/errors/FeedbackNotFoundError';
import type { Result } from '@evyweb/simple-ddd-toolkit';

export interface FeedbackRepository {
  save(feedback: Feedback): Promise<void>;

  getAll(): Promise<Feedback[]>;

  findByFeedbackId(feedbackId: string): Promise<Result<Feedback, FeedbackNotFoundError>>;

  delete(id: string): Promise<void>;
}
