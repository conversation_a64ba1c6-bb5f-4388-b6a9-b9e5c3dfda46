import type { CodeReviewParticipation } from '@/src/server/domain/CodeReviewParticipation/CodeReviewParticipation';
import type { Result } from '@evyweb/simple-ddd-toolkit';

export interface CodeReviewParticipationRepository {
  save(participation: CodeReviewParticipation): Promise<void>;

  getAll(): Promise<CodeReviewParticipation[]>;

  findByUserIdAndCodeReviewIdAndDate(
    userId: string,
    codeReviewId: string,
    date: string
  ): Promise<Result<CodeReviewParticipation, Error>>;
}
