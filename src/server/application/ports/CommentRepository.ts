import type { Comment } from '@/src/server/domain/Comment/Comment';
import type { CommentNotFoundError } from '@/src/server/domain/Comment/errors/CommentNotFoundError';
import type { Result } from '@evyweb/simple-ddd-toolkit';

export interface CommentRepository {
  findById(commentId: string): Promise<Result<Comment, CommentNotFoundError>>;

  save(comment: Comment): Promise<void>;

  delete(commentId: string): Promise<void>;
}
