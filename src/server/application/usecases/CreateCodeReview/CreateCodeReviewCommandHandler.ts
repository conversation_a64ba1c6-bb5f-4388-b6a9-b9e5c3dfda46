import type { CodeReviewRepository } from '@/src/server/application/ports/CodeReviewRepository';
import type { IdentityProvider } from '@/src/server/application/ports/IdentityProvider';
import type { CreateCodeReviewCommand } from '@/src/server/application/usecases/CreateCodeReview/CreateCodeReviewCommand';
import type { CreateCodeReviewResponse } from '@/src/server/application/usecases/CreateCodeReview/CreateCodeReviewResponse';
import { CodeReview } from '@/src/server/domain/CodeReview/CodeReview';
import { Result } from '@evyweb/simple-ddd-toolkit';

export class CreateCodeReviewCommandHandler {
  private readonly codeReviewRepository: CodeReviewRepository;
  private readonly identityProvider: IdentityProvider;

  constructor(codeReviewRepository: CodeReviewRepository, identityProvider: IdentityProvider) {
    this.codeReviewRepository = codeReviewRepository;
    this.identityProvider = identityProvider;
  }

  async handle({ title, userId }: CreateCodeReviewCommand): Promise<CreateCodeReviewResponse> {
    const codeReviewId = this.identityProvider.generateId();
    const codeReview = CodeReview.organize(codeReviewId, title, userId).getValue();
    await this.codeReviewRepository.save(codeReview);
    return Result.ok(codeReviewId);
  }
}
