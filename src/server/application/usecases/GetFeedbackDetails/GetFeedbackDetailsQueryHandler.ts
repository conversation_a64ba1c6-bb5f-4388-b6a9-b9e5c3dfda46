import { api } from '@/convex/_generated/api';
import type { CodeFormatter } from '@/src/server/application/ports/CodeFormatter';
import type { GetFeedbackDetailsQuery } from '@/src/server/application/usecases/GetFeedbackDetails/GetFeedbackDetailsQuery';
import type { GetFeedbackDetailsResponse } from '@/src/server/application/usecases/GetFeedbackDetails/GetFeedbackDetailsResponse';
import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { fetchQuery } from 'convex/nextjs';

export class GetFeedbackDetailsQueryHandler {
  private readonly codeFormatter: CodeFormatter;

  constructor(codeFormatter: CodeFormatter) {
    this.codeFormatter = codeFormatter;
  }

  async handle({ feedbackId }: GetFeedbackDetailsQuery): Promise<GetFeedbackDetailsResponse> {
    const feedbackDetails = await fetchQuery(
      api.queries.feedbackDetails.endpoint,
      { feedbackId },
      { token: await convexAuthNextjsToken() }
    );
    const code = await this.codeFormatter.formatCodeSnippet(
      feedbackDetails.codeSnippet,
      feedbackDetails.feedback.subType
    );

    return {
      title: feedbackDetails.feedback.title,
      content: feedbackDetails.feedback.content,
      code,
    };
  }
}
