import type { CodeReviewRepository } from '@/src/server/application/ports/CodeReviewRepository';
import { UnauthorizedDeleteCodeReviewError } from '@/src/server/domain/CodeReview/errors/UnauthorizedDeleteCodeReviewError';
import type { DeleteCodeReviewCommand } from './DeleteCodeReviewCommand';

export class DeleteCodeReviewCommandHandler {
  private readonly codeReviewRepository: CodeReviewRepository;

  constructor(codeReviewRepository: CodeReviewRepository) {
    this.codeReviewRepository = codeReviewRepository;
  }

  async handle({ codeReviewId, userId }: DeleteCodeReviewCommand): Promise<void> {
    const codeReviewResult = await this.codeReviewRepository.findById(codeReviewId);

    if (codeReviewResult.isFail()) {
      throw codeReviewResult.getError();
    }

    const codeReview = codeReviewResult.getValue();

    if (codeReview.isNotCreatedBy(userId)) {
      throw new UnauthorizedDeleteCodeReviewError();
    }

    codeReview.delete();

    await this.codeReviewRepository.save(codeReview);
  }
}
