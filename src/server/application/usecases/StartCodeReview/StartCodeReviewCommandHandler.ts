import type { CodeReviewRepository } from '@/src/server/application/ports/CodeReviewRepository';
import type { StartCodeReviewCommand } from './StartCodeReviewCommand';

export class StartCodeReviewCommandHandler {
  private readonly codeReviewRepository: CodeReviewRepository;

  constructor(codeReviewRepository: CodeReviewRepository) {
    this.codeReviewRepository = codeReviewRepository;
  }

  async handle({ codeReviewId }: StartCodeReviewCommand): Promise<void> {
    const codeReviewResult = await this.codeReviewRepository.findById(codeReviewId);

    if (codeReviewResult.isFail()) {
      throw codeReviewResult.getError();
    }

    const codeReview = codeReviewResult.getValue();
    codeReview.start();

    await this.codeReviewRepository.save(codeReview);
  }
}
