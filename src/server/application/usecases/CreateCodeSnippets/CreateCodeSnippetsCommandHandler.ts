import type { CodeSnippetRepository } from '@/src/server/application/ports/CodeSnippetRepository';
import type { IdentityProvider } from '@/src/server/application/ports/IdentityProvider';
import type { CreateCodeSnippetsCommand } from '@/src/server/application/usecases/CreateCodeSnippets/CreateCodeSnippetsCommand';
import type { CreateCodeSnippetsResponse } from '@/src/server/application/usecases/CreateCodeSnippets/CreateCodeSnippetsResponse';
import { CodeSnippet } from '@/src/server/domain/CodeSnippet/CodeSnippet';
import { Result } from '@evyweb/simple-ddd-toolkit';

export class CreateCodeSnippetsCommandHandler {
  private readonly codeSnippetRepository: CodeSnippetRepository;
  private readonly identityProvider: IdentityProvider;

  constructor(codeSnippetRepository: CodeSnippetRepository, identityProvider: IdentityProvider) {
    this.codeSnippetRepository = codeSnippetRepository;
    this.identityProvider = identityProvider;
  }

  async handle({ snippets, codeReviewId }: CreateCodeSnippetsCommand): Promise<CreateCodeSnippetsResponse> {
    const codeSnippets = snippets.map((snippet) => {
      const snippetId = this.identityProvider.generateId();
      const codeSnippetToReview = CodeSnippet.submitForReview(
        snippetId,
        snippet.filename,
        snippet.content,
        snippet.language,
        codeReviewId
      );
      if (codeSnippetToReview.isFail()) {
        throw codeSnippetToReview.getError();
      }
      return codeSnippetToReview.getValue();
    });

    await this.codeSnippetRepository.saveAll(codeSnippets);
    return Result.ok(undefined);
  }
}
