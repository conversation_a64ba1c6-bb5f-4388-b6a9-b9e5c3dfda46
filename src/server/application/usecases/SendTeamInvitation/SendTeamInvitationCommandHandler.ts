import type { IdentityProvider } from '@/src/server/application/ports/IdentityProvider';
import type { TeamInvitationRepository } from '@/src/server/application/ports/TeamInvitationRepository';
import type { TeamInvitationServicePort } from '@/src/server/application/ports/TeamInvitationServicePort';
import { TeamInvitation } from '@/src/server/domain/TeamInvitation/TeamInvitation';
import { TeamInvitationId } from '@/src/server/domain/TeamInvitation/valueObjects/TeamInvitationId';
import { Result } from '@evyweb/simple-ddd-toolkit';
import type { SendTeamInvitationCommand } from './SendTeamInvitationCommand';
import type { SendTeamInvitationResponse } from './SendTeamInvitationResponse';

export class SendTeamInvitationCommandHandler {
  private readonly teamInvitationService: TeamInvitationServicePort;
  private readonly teamInvitationRepository: TeamInvitationRepository;
  private readonly identityProvider: IdentityProvider;

  constructor(
    teamInvitationService: TeamInvitationServicePort,
    teamInvitationRepository: TeamInvitationRepository,
    identityProvider: IdentityProvider
  ) {
    this.teamInvitationService = teamInvitationService;
    this.teamInvitationRepository = teamInvitationRepository;
    this.identityProvider = identityProvider;
  }

  async handle({ currentUserId, teamId, email }: SendTeamInvitationCommand): Promise<SendTeamInvitationResponse> {
    const canSendInvitationResult = await this.teamInvitationService.canSendInvitation({
      currentUserId,
      teamId,
      email,
    });

    if (canSendInvitationResult.isFail()) {
      return Result.fail(canSendInvitationResult.getError());
    }

    const invitationId = this.identityProvider.generateId();

    const invitation = TeamInvitation.createPendingInvitation({
      id: TeamInvitationId.createFrom(invitationId),
      teamId,
      email,
      invitedBy: currentUserId,
    }).getValue();

    await this.teamInvitationRepository.save(invitation);

    return Result.ok(invitationId);
  }
}
