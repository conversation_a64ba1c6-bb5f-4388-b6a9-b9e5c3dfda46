import type { EmailMismatchError } from '@/src/server/domain/TeamInvitation/errors/EmailMismatchError';
import type { InvitationNotPendingError } from '@/src/server/domain/TeamInvitation/errors/InvitationNotPendingError';
import type { TeamInvitationNotFoundError } from '@/src/server/domain/TeamInvitation/errors/TeamInvitationNotFoundError';
import type { TeamMemberAlreadyExistsError } from '@/src/server/domain/TeamMember/errors/TeamMemberAlreadyExistsError';
import type { Result } from '@evyweb/simple-ddd-toolkit';

export type AcceptTeamInvitationResponse = Result<
  void,
  TeamInvitationNotFoundError | InvitationNotPendingError | EmailMismatchError | TeamMemberAlreadyExistsError
>;
