import type { CommentRepository } from '@/src/server/application/ports/CommentRepository';
import { UnauthorizedCommentDeletionError } from '@/src/server/domain/Comment/errors/UnauthorizedCommentDeletionError';
import { Result } from '@evyweb/simple-ddd-toolkit';
import type { DeleteCommentCommand } from './DeleteCommentCommand';
import type { DeleteCommentResponse } from './DeleteCommentResponse';

export class DeleteCommentCommandHandler {
  private readonly commentRepository: CommentRepository;

  constructor(commentRepository: CommentRepository) {
    this.commentRepository = commentRepository;
  }

  async handle({ commentId, userId }: DeleteCommentCommand): Promise<DeleteCommentResponse> {
    const commentResult = await this.commentRepository.findById(commentId);

    if (commentResult.isFail()) {
      return Result.fail(commentResult.getError());
    }

    const comment = commentResult.getValue();

    if (comment.isNotWrittenBy(userId)) {
      return Result.fail(new UnauthorizedCommentDeletionError());
    }

    comment.delete();

    await this.commentRepository.save(comment);

    return Result.ok(void 0);
  }
}
