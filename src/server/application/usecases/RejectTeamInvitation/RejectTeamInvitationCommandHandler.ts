import type { TeamInvitationRepository } from '@/src/server/application/ports/TeamInvitationRepository';
import { InvitationNotPendingError } from '@/src/server/domain/TeamInvitation/errors/InvitationNotPendingError';
import { Result } from '@evyweb/simple-ddd-toolkit';
import type { RejectTeamInvitationCommand } from './RejectTeamInvitationCommand';
import type { RejectTeamInvitationResponse } from './RejectTeamInvitationResponse';

export class RejectTeamInvitationCommandHandler {
  private readonly teamInvitationRepository: TeamInvitationRepository;

  constructor(teamInvitationRepository: TeamInvitationRepository) {
    this.teamInvitationRepository = teamInvitationRepository;
  }

  async handle({ invitationId }: RejectTeamInvitationCommand): Promise<RejectTeamInvitationResponse> {
    const invitationResult = await this.teamInvitationRepository.findById(invitationId);

    if (invitationResult.isFail()) {
      return Result.fail(invitationResult.getError());
    }

    const invitation = invitationResult.getValue();

    if (!invitation.isPending()) {
      return Result.fail(new InvitationNotPendingError());
    }

    invitation.decline();
    await this.teamInvitationRepository.save(invitation);

    return Result.ok(void 0);
  }
}
