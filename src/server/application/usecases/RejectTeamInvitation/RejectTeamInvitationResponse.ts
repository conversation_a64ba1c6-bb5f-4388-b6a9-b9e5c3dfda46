import type { InvitationNotPendingError } from '@/src/server/domain/TeamInvitation/errors/InvitationNotPendingError';
import type { TeamInvitationNotFoundError } from '@/src/server/domain/TeamInvitation/errors/TeamInvitationNotFoundError';
import type { Result } from '@evyweb/simple-ddd-toolkit';

export type RejectTeamInvitationResponse = Result<void, TeamInvitationNotFoundError | InvitationNotPendingError>;
