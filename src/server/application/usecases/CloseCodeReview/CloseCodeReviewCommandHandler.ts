import type { CodeReviewRepository } from '@/src/server/application/ports/CodeReviewRepository';
import type { CloseCodeReviewCommand } from './CloseCodeReviewCommand';

export class CloseCodeReviewCommandHandler {
  private readonly codeReviewRepository: CodeReviewRepository;

  constructor(codeReviewRepository: CodeReviewRepository) {
    this.codeReviewRepository = codeReviewRepository;
  }

  async handle(command: CloseCodeReviewCommand): Promise<void> {
    const codeReviewResult = await this.codeReviewRepository.findById(command.codeReviewId);

    if (codeReviewResult.isFail()) {
      throw codeReviewResult.getError();
    }

    const codeReview = codeReviewResult.getValue();
    codeReview.close();

    await this.codeReviewRepository.save(codeReview);
  }
}
