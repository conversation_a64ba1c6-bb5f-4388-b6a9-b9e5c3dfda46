import type { FeedbackRepository } from '@/src/server/application/ports/FeedbackRepository';
import type { Feedback } from '@/src/server/domain/Feedback/Feedback';
import { UnauthorizedFeedbackDeletionError } from '@/src/server/domain/Feedback/errors/UnauthorizedFeedbackDeletionError';
import { Result } from '@evyweb/simple-ddd-toolkit';
import type { DeleteFeedbackCommand } from './DeleteFeedbackCommand';
import type { DeleteFeedbackResponse } from './DeleteFeedbackResponse';

export class DeleteFeedbackCommandHandler {
  private readonly repository: FeedbackRepository;

  constructor(repository: FeedbackRepository) {
    this.repository = repository;
  }

  async handle({ feedbackId, userId }: DeleteFeedbackCommand): Promise<DeleteFeedbackResponse> {
    const feedbackResult = await this.repository.findByFeedbackId(feedbackId);

    if (feedbackResult.isFail()) {
      return Result.fail(feedbackResult.getError());
    }

    const feedback = feedbackResult.getValue();
    const authResult = this.checkAuthorization(feedback, userId);

    if (authResult.isFail()) {
      return Result.fail(authResult.getError());
    }

    await this.repository.delete(feedbackId);

    return Result.ok(void 0);
  }

  private checkAuthorization(feedback: Feedback, userId: string): Result<void, UnauthorizedFeedbackDeletionError> {
    if (feedback.isNotWrittenBy(userId)) {
      return Result.fail(new UnauthorizedFeedbackDeletionError());
    }
    return Result.ok(void 0);
  }
}
