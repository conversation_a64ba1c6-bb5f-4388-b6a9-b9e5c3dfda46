import type { FeedbackRepository } from '@/src/server/application/ports/FeedbackRepository';
import type { IdentityProvider } from '@/src/server/application/ports/IdentityProvider';
import { Feedback } from '@/src/server/domain/Feedback/Feedback';
import type { FeedbackSubTypeValue } from '@/src/server/domain/Feedback/valueObjects/FeedbackSubType';
import type { FeedbackTypeValue } from '@/src/server/domain/Feedback/valueObjects/FeedbackType';
import { Result } from '@evyweb/simple-ddd-toolkit';
import type { PostCommentFeedbackCommand } from './PostCommentFeedbackCommand';
import type { PostCommentFeedbackResponse } from './PostCommentFeedbackResponse';

export class PostCommentFeedbackCommandHandler {
  constructor(
    private readonly repository: FeedbackRepository,
    private readonly identityProvider: IdentityProvider
  ) {}

  async handle(command: PostCommentFeedbackCommand): Promise<PostCommentFeedbackResponse> {
    const feedbackId = this.identityProvider.generateId();

    const feedbackCreation = Feedback.post({
      id: feedbackId,
      codeSnippetId: command.codeSnippetId,
      title: command.title,
      content: command.content,
      userId: command.userId,
      startLine: command.startLine,
      endLine: command.endLine,
      type: command.type as FeedbackTypeValue,
      subType: command.subType as FeedbackSubTypeValue,
      deleted: false,
    });

    if (feedbackCreation.isFail()) {
      return Result.fail(feedbackCreation.getError());
    }

    await this.repository.save(feedbackCreation.getValue());

    return Result.ok(feedbackId);
  }
}
