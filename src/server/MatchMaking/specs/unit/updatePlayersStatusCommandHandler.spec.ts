import {describe, it, expect, beforeEach} from 'vitest';
import {UpdatePlayersStatusCommandHandler} from '@/src/MatchMaking/server/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler';
import {InMemoryAppUserRepository} from '@/src/Authentication/server/infrastructure/repositories/InMemoryAppUserRepository';
import {AppUser} from '@/src/Authentication/server/domain/AppUser/AppUser';

let repository: InMemoryAppUserRepository;
let handler: UpdatePlayersStatusCommandHandler;

beforeEach(() => {
  repository = new InMemoryAppUserRepository();
  handler = new UpdatePlayersStatusCommandHandler(repository);
  repository.add(AppUser.fromSnapshot({id: '00000000-0000-0000-0000-000000000001', authId: 'a1', name: 'U1', image: 'img', email: 'u1@test'}));
  repository.add(AppUser.fromSnapshot({id: '00000000-0000-0000-0000-000000000002', authId: 'a2', name: 'U2', image: 'img', email: 'u2@test'}));
});

describe('UpdatePlayersStatusCommandHandler', () => {
  it('should update the status of all players', async () => {
    // Arrange
    const command = {players: ['00000000-0000-0000-0000-000000000001','00000000-0000-0000-0000-000000000002'], status: 'playing'};

    // Act
    await handler.handle(command);

    // Assert
    const user1 = await repository.findById('00000000-0000-0000-0000-000000000001');
    const user2 = await repository.findById('00000000-0000-0000-0000-000000000002');
    expect(user1?.toSnapshot().status).toBe('playing');
    expect(user2?.toSnapshot().status).toBe('playing');
  });

  it('should ignore unknown players', async () => {
    // Arrange
    const command = {players: ['not-found'], status: 'idle'};

    // Act
    await handler.handle(command);

    // Assert
    const user1 = await repository.findById('00000000-0000-0000-0000-000000000001');
    const user2 = await repository.findById('00000000-0000-0000-0000-000000000002');
    expect(user1?.toSnapshot().status).toBeUndefined();
    expect(user2?.toSnapshot().status).toBeUndefined();
  });
});
