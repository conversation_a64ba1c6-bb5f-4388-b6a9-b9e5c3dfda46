import {describe, it, expect, beforeEach} from 'vitest';
import {UpdateSinglePlayerStatusCommandHandler} from '@/src/server/MatchMaking/application/commands/MatchMaking/UpdateSinglePlayerStatus/UpdateSinglePlayerStatusCommandHandler';
import {InMemoryAppUserRepository} from '@/src/server/Authentication/infrastructure/repositories/InMemoryAppUserRepository';
import {AppUser} from '@/src/server/Authentication/domain/AppUser/AppUser';

let repository: InMemoryAppUserRepository;
let handler: UpdateSinglePlayerStatusCommandHandler;

beforeEach(() => {
  repository = new InMemoryAppUserRepository();
  handler = new UpdateSinglePlayerStatusCommandHandler(repository);
  repository.add(AppUser.fromSnapshot({id: '00000000-0000-0000-0000-000000000001', authId: 'a1', name: 'U1', image: 'img', email: 'u1@test'}));
});

describe('UpdateSinglePlayerStatusCommandHandler', () => {
  it('should update the status of the player', async () => {
    // Arrange
    const command = {playerId: '00000000-0000-0000-0000-000000000001', status: 'waiting'};

    // Act
    await handler.handle(command);

    // Assert
    const user = await repository.findById('00000000-0000-0000-0000-000000000001');
    expect(user?.toSnapshot().status).toBe('waiting');
  });

  it('should ignore unknown player ids', async () => {
    // Arrange
    const command = {playerId: 'not-found', status: 'idle'};

    // Act
    await handler.handle(command);

    // Assert
    const user = await repository.findById('00000000-0000-0000-0000-000000000001');
    expect(user?.toSnapshot().status).toBeUndefined();
  });
});
