import {UpdateSinglePlayerStatusCommand} from "./UpdateSinglePlayerStatusCommand";
import {AppUserRepository} from "@/src/Authentication/server/application/ports/AppUserRepository";
export class UpdateSinglePlayerStatusCommandHandler {
  private readonly repository: AppUserRepository;

  constructor(repository: AppUserRepository) {
    this.repository = repository;
  }

  async handle({playerId, status}: UpdateSinglePlayerStatusCommand) {
    const user = await this.repository.findById(playerId);
    if (user) {
      user.setStatus(status);
      await this.repository.save(user);
    }
  }
}
