import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {
  LoadGameByIdQuery
} from "@/src/server/Gaming/application/queries/LoadGameById/LoadGameByIdQuery";

export class LoadGameByIdQueryHandler {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async handle({gameId}: LoadGameByIdQuery) {
    const game = await this.ctx.db.get(gameId as Id<"games">);

    if (!game) {
      return {
        error: "Game not found",
        data: null
      };
    }

    return {
      error: null,
      data: {
        id: game._id,
        name: game.name
      }
    };
  }
}
