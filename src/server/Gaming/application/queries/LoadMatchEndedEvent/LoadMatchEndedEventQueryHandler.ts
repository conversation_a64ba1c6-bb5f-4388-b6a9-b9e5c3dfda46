import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {
  LoadMatchEndedEventQuery
} from "@/src/server/Gaming/application/queries/LoadMatchEndedEvent/LoadMatchEndedEventQuery";

export class LoadMatchEndedEventQueryHandler {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async handle({matchId}: LoadMatchEndedEventQuery) {
    return await this.ctx.db
      .query("matchEvents")
      .withIndex('by_matchId_and_type', (q) => q.eq("matchId", matchId as Id<"matches">).eq("type", "MatchEnded"))
      .first();
  }
}
