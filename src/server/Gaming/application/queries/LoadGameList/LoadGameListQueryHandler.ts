import {GenericQueryCtx} from "convex/server";
import {DataModel} from "@/convex/_generated/dataModel";
import {
  LoadGameListQuery
} from "@/src/server/Gaming/application/queries/LoadGameList/LoadGameListQuery";

export class LoadGameListQueryHandler {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async handle({}: LoadGameListQuery) {
    const games = await this.ctx.db.query("games").collect();

    return {
      data: games.map((game) => ({
        id: game._id,
        name: game.name,
      })),
    };
  }
}
