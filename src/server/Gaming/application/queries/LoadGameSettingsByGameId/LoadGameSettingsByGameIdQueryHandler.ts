import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {
  LoadGameSettingsByGameIdQuery
} from "@/src/server/Gaming/application/queries/LoadGameSettingsByGameId/LoadGameSettingsByGameIdQuery";

export class LoadGameSettingsByGameIdQueryHandler {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async handle({gameId}: LoadGameSettingsByGameIdQuery) {
    const settings = await this.ctx.db
      .query("gameSettings")
      .withIndex("by_gameId", (q) => q.eq("gameId", gameId as Id<"games">))
      .first();

    return {
      error: null,
      data: settings
    };
  }
}
