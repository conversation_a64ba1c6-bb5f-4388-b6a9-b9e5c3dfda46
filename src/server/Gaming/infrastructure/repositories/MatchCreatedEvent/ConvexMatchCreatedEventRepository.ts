import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {MatchCreatedEventRepository} from "@/src/server/Gaming/application/ports/MatchCreatedEventRepository";
import {MatchCreatedEvent} from "@/src/server/Gaming/domain/Match/MatchCreatedEvent";

export class ConvexMatchCreatedEventRepository implements MatchCreatedEventRepository {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async getByGameId(gameId: string): Promise<MatchCreatedEvent[]> {
    const docs = await this.ctx.db
      .query("matchmakingEvents")
      .withIndex('by_gameId_and_type', (q) => q.eq("gameId", gameId as Id<"games">).eq("type", "MatchCreated"))
      .order('desc')
      .collect();
    return docs as unknown as MatchCreatedEvent[];
  }
}
