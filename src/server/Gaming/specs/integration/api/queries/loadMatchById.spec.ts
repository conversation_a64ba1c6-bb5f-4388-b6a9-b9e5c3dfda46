import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";
import {ADMIN_IDENTITY, SOPHIE_APP_USER, SOPHIE_IDENTITY} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";

describe('loadMatchById', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    asSophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    createAppUser(asAdmin, SOPHIE_APP_USER);
  });

  describe('When the match exists', () => {
    it('should return it', async () => {
      // Arrange
      const gameId = await asAdmin.run(ctx => ctx.db.insert('games', {name: 'Test'})) as Id<'games'>;
      const matchId = await asAdmin.run(ctx => ctx.db.insert('matches', {
        gameId,
        players: ['u1','u2'],
        status: 'setup',
        createdAt: Date.now(),
      })) as Id<'matches'>;

      // Act
      const result = await loadMatch(asSophie, matchId);

      // Assert
      expect(result.data!.id).toBe(matchId);
      expect(result.data!.players).toEqual(['u1','u2']);
    });
  });

  describe('When the match does not exist', () => {
    it('should return an error', async () => {
      // Act
      const result = await loadMatch(asSophie, 'non-existing');

      // Assert
      expect(result.error).toBe('Match not found');
      expect(result.data).toBeNull();
    });
  });

  function loadMatch(user: TestConvexForDataModel<DataModel>, matchId: string) {
    return user.query(api.queries.match.loadMatchById.endpoint, {matchId});
  }
});
