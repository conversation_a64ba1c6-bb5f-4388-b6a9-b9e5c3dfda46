import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {
  LoadDecksByUserIdAndGameIdQuery
} from "@/src/server/DeckBuilding/application/queries/LoadDecksByUserIdAndGameId/LoadDecksByUserIdAndGameIdQuery";

export class LoadDecksByUserIdAndGameIdQueryHandler {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async handle({gameId, userId}: LoadDecksByUserIdAndGameIdQuery) {
    return await this.ctx.db
      .query("decks")
      .withIndex("by_playerId_and_gameId", (q) => q.eq("playerId", userId as Id<"users">).eq("gameId", gameId as Id<"games">))
      .collect();
  }
}
