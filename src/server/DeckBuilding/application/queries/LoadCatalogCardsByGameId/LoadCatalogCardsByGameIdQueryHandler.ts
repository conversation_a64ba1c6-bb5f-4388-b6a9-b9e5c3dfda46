import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {
  LoadCatalogCardsByGameIdQuery
} from "@/src/server/DeckBuilding/application/queries/LoadCatalogCardsByGameId/LoadCatalogCardsByGameIdQuery";

export class LoadCatalogCardsByGameIdQueryHandler {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async handle({gameId}: LoadCatalogCardsByGameIdQuery) {
    const cards = await this.ctx.db
      .query("catalogCards")
      .withIndex("by_gameId", (q) => q.eq("gameId", gameId as Id<"games">))
      .take(100);

    if (!cards) {
      return {
        error: "Catalog cards not found",
        data: null
      };
    }

    return {
      error: null,
      data: {
        cards: cards.map((card) => ({
          id: card!._id,
          name: card!.name,
          image: card!.image,
          minDeckQuantity: card!.minDeckQuantity,
          maxDeckQuantity: card!.maxDeckQuantity,
        }))
      }
    };
  }
}
