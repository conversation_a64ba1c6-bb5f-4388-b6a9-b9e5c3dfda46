import { FeedbackRequestBuilder } from '@/src/server/specs/helpers/factories/FeedbackRequestBuilder';
import { ANY_VALID_FEEDBACK_CONTENT } from '@/src/server/specs/helpers/factories/fakeFeedbackContents';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';

export const VALID_COMMENT_FEEDBACK = FeedbackRequestBuilder.aFeedbackRequest()
  .withContent(ANY_VALID_FEEDBACK_CONTENT)
  .withLineRange(1, 5)
  .build();

export const VALID_COMMENT_REQUEST = {
  codeSnippetId: VALID_COMMENT_FEEDBACK.codeSnippetId,
  title: VALID_COMMENT_FEEDBACK.title,
  content: VALID_COMMENT_FEEDBACK.content,
  startLine: VALID_COMMENT_FEEDBACK.startLine ?? undefined,
  endLine: VALID_COMMENT_FEEDBACK.endLine ?? undefined,
};

export const VALID_COMMENT_EXPECTATION = {
  id: createFakeId('1'),
  codeSnippetId: VALID_COMMENT_FEEDBACK.codeSnippetId,
  title: VALID_COMMENT_FEEDBACK.title,
  content: VALID_COMMENT_FEEDBACK.content,
  startLine: VALID_COMMENT_FEEDBACK.startLine ?? undefined,
  endLine: VALID_COMMENT_FEEDBACK.endLine ?? undefined,
  userId: createFakeId('1'),
};
