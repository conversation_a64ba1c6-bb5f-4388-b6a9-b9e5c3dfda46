import { ParticipateToCodeReviewCommandHandler } from '@/src/server/application/usecases/ParticipateToCodeReview/ParticipateToCodeReviewCommandHandler';
import { CodeReviewParticipation } from '@/src/server/domain/CodeReviewParticipation/CodeReviewParticipation';
import { FakeIdentityProvider } from '@/src/server/infrastructure/providers/IdentityProvider/FakeIdentityProvider';
import { InMemoryCodeReviewParticipationRepository } from '@/src/server/infrastructure/repositories/CodeReviewParticipationRepository/InMemoryCodeReviewParticipationRepository';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { Result } from '@evyweb/simple-ddd-toolkit';

type ParticipateToCodeReviewRequest = {
  codeReviewId: string;
  userId: string;
};

export class ParticipateToCodeReviewSteps {
  private readonly repository: InMemoryCodeReviewParticipationRepository;
  private readonly commandHandler: ParticipateToCodeReviewCommandHandler;
  private readonly identityProvider: FakeIdentityProvider;

  private currentRequest: ParticipateToCodeReviewRequest | null = null;

  private result: Result<string, Error> = Result.fail(new Error('NO RESULT FOUND IN SCENARIO'));

  constructor() {
    this.identityProvider = new FakeIdentityProvider();
    this.identityProvider.setNextIds(createFakeId('1'));
    this.repository = new InMemoryCodeReviewParticipationRepository();
    this.commandHandler = new ParticipateToCodeReviewCommandHandler(this.repository, this.identityProvider);
  }

  givenCurrentDateIs(today: string) {
    vi.setSystemTime(new Date(today));
  }

  givenRequest(request: { codeReviewId: string; userId: string }) {
    this.currentRequest = request;
  }

  async whenTheUserParticipatesToTheCodeReview() {
    this.result = await this.commandHandler.handle({
      codeReviewId: this.currentRequest!.codeReviewId,
      userId: this.currentRequest!.userId,
    });
  }

  async thenCodeReviewParticipationShouldBeSavedWith(expected: {
    id: string;
    userId: string;
    codeReviewId: string;
    date: string;
  }) {
    const result = await this.repository.getAll();
    expect(result.length).toEqual(1);
    const codeReviewParticipation = result[0];
    expect(codeReviewParticipation.get('id').get('value')).toEqual(expected.id);
    expect(codeReviewParticipation.get('userId').get('value')).toEqual(expected.userId);
    expect(codeReviewParticipation.get('codeReviewId').get('value')).toEqual(expected.codeReviewId);
    expect(codeReviewParticipation.get('date').get('value')).toEqual(expected.date);
    expect(this.result.getValue()).toEqual(expected.id);
  }

  async givenCodeReviewParticipationExists(existing: {
    id: string;
    userId: string;
    codeReviewId: string;
    date: string;
  }) {
    const codeReviewParticipation = CodeReviewParticipation.create(existing).getValue();
    await this.repository.save(codeReviewParticipation);
  }
}
