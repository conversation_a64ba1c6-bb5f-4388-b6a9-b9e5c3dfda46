import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { AcceptTeamInvitationSteps } from '@/src/server/specs/unit/AcceptTeamInvitation/AcceptTeamInvitationSteps';

describe('Feature: Accepting a Team Invitation', () => {
  let _: AcceptTeamInvitationSteps;

  beforeEach(() => {
    _ = new AcceptTeamInvitationSteps();
  });

  describe('Scenario: User accepts a pending invitation with matching email', () => {
    it('should successfully accept the invitation and add the user as a team member', async () => {
      const invitationId = createFakeId('1');
      const teamId = createFakeId('2');
      const userId = createFakeId('3');
      const email = '<EMAIL>';
      const invitedBy = createFakeId('4');

      await _.givenAPendingInvitationExists({
        id: invitationId,
        teamId,
        email,
        invitedBy,
      });

      await _.whenUserAcceptsInvitation(userId, invitationId, email);

      await _.thenInvitationShouldBeAccepted(invitationId);
      await _.thenUserShouldBeAddedAsTeamMember(userId, teamId);
    });
  });

  describe('Scenario: User tries to accept a non-existent invitation', () => {
    it('should fail with invitation not found error', async () => {
      const nonExistentInvitationId = createFakeId('5');
      const userId = createFakeId('6');
      const email = '<EMAIL>';

      await _.whenUserAcceptsInvitation(userId, nonExistentInvitationId, email);

      await _.thenRequestShouldFailWithInvitationNotFound();
    });
  });

  describe('Scenario: User tries to accept an already accepted invitation', () => {
    it('should fail as the invitation is already accepted', async () => {
      const invitationId = createFakeId('7');
      const teamId = createFakeId('8');
      const userId = createFakeId('9');
      const email = '<EMAIL>';
      const invitedBy = createFakeId('10');

      await _.givenAnAcceptedInvitationExists({
        id: invitationId,
        teamId,
        email,
        invitedBy,
      });

      await _.whenUserAcceptsInvitation(userId, invitationId, email);

      await _.thenRequestShouldFailAsInvitationIsNotPending();
    });
  });

  describe('Scenario: User tries to accept an invitation with non-matching email', () => {
    it('should fail with email mismatch error', async () => {
      const invitationId = createFakeId('11');
      const teamId = createFakeId('12');
      const userId = createFakeId('13');
      const invitationEmail = '<EMAIL>';
      const userEmail = '<EMAIL>';
      const invitedBy = createFakeId('14');

      await _.givenAPendingInvitationExists({
        id: invitationId,
        teamId,
        email: invitationEmail,
        invitedBy,
      });

      await _.whenUserAcceptsInvitation(userId, invitationId, userEmail);

      await _.thenRequestShouldFailWithEmailMismatch();
    });
  });

  describe('Scenario: User tries to accept an invitation but is already a team member', () => {
    it('should fail as the user is already a team member', async () => {
      const invitationId = createFakeId('15');
      const teamId = createFakeId('16');
      const userId = createFakeId('17');
      const email = '<EMAIL>';
      const invitedBy = createFakeId('18');

      await _.givenAPendingInvitationExists({
        id: invitationId,
        teamId,
        email,
        invitedBy,
      });

      await _.givenUserIsAlreadyATeamMember(userId, teamId);

      await _.whenUserAcceptsInvitation(userId, invitationId, email);

      await _.thenRequestShouldFailAsUserIsAlreadyATeamMember();
    });
  });
});
