import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { CreateCodeReviewSteps } from '@/src/server/specs/unit/CreateCodeReview/CreateCodeReviewSteps';

describe('Feature: Creating a code review', () => {
  let _: CreateCodeReviewSteps;

  beforeEach(() => {
    _ = new CreateCodeReviewSteps();
    _.givenCurrentDateIs('2025-02-23');
    _.givenCurrentUser(createFakeId('1'));
  });

  describe('Scenario: The user can create a code review', () => {
    beforeEach(() => {
      _.givenRequest({
        title: 'ANY_VALID_TITLE',
      });
    });

    it('should create a new code review', async () => {
      await _.whenTheUserCreatesTheCodeReview();
      await _.thenCodeReviewShouldBeSavedWith({
        id: createFakeId('1'),
        title: 'ANY_VALID_TITLE',
        authorId: createFakeId('1'),
      });
    });
  });
});
