import { CodeSnippetNotFoundError } from '@/src/server/domain/CodeSnippet/errors/CodeSnippetNotFoundError';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { UpdateCodeSnippetsSteps } from '@/src/server/specs/unit/UpdateCodeSnippets/UpdateCodeSnippetsSteps';

describe('Feature: Updating code snippets', () => {
  let _: UpdateCodeSnippetsSteps;

  beforeEach(() => {
    _ = new UpdateCodeSnippetsSteps();
    _.givenCurrentDateIs('2025-02-23');
  });

  describe('Scenario: The user can update existing code snippets for a code review', () => {
    beforeEach(() => {
      _.givenExistingCodeSnippets([
        {
          id: createFakeId('1'),
          content: 'console.log("original snippet 1");',
          filename: 'original-snippet-1',
          language: 'javascript',
          codeReviewId: createFakeId('1'),
        },
        {
          id: createFakeId('2'),
          content: 'console.log("original snippet 2");',
          filename: 'original-snippet-2',
          language: 'typescript',
          codeReviewId: createFakeId('1'),
        },
      ]);

      _.givenRequest({
        codeReviewId: createFakeId('1'),
        snippets: [
          {
            id: createFakeId('1'),
            content: 'console.log("updated snippet 1");',
            filename: 'updated-snippet-1',
            language: 'javascript',
          },
          {
            id: createFakeId('2'),
            content: 'console.log("updated snippet 2");',
            filename: 'updated-snippet-2',
            language: 'typescript',
          },
        ],
      });
    });

    it('should update the code snippets', async () => {
      await _.whenTheUserUpdatesTheCodeSnippets();
      await _.thenCodeSnippetsShouldBeUpdatedWith([
        {
          id: createFakeId('1'),
          content: 'console.log("updated snippet 1");',
          filename: 'updated-snippet-1',
          language: 'javascript',
        },
        {
          id: createFakeId('2'),
          content: 'console.log("updated snippet 2");',
          filename: 'updated-snippet-2',
          language: 'typescript',
        },
      ]);
    });
  });

  describe('Scenario: The user tries to update non-existent code snippets', () => {
    beforeEach(() => {
      _.givenRequest({
        codeReviewId: createFakeId('1'),
        snippets: [
          {
            id: createFakeId('999'),
            content: 'console.log("non-existent snippet");',
            filename: 'non-existent-snippet',
            language: 'javascript',
          },
        ],
      });
    });

    it('should return a code snippet not found error', async () => {
      await _.whenTheUserUpdatesTheCodeSnippets();
      _.thenShouldReceiveError(new CodeSnippetNotFoundError());
    });
  });
});
