import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { CloseCodeReviewSteps } from '@/src/server/specs/unit/CloseCodeReview/CloseCodeReviewSteps';

describe('Feature: Closing a code review', () => {
  let _: CloseCodeReviewSteps;

  beforeEach(() => {
    _ = new CloseCodeReviewSteps();
    _.givenCurrentDateIs('2025-02-23');
  });

  describe('Scenario: The user can close a code review', () => {
    beforeEach(() => {
      _.givenCurrentUser(createFakeId('1'));
      _.givenCodeReviewExists({
        id: createFakeId('1'),
        title: 'ANY_VALID_TITLE',
        authorId: createFakeId('1'),
        status: 'open',
      });
      _.givenRequest({ codeReviewId: createFakeId('1') });
    });

    it('should change the code review status to close', async () => {
      await _.whenTheUserClosesTheCodeReview();
      await _.thenCodeReviewShouldBeClosed();
    });
  });

  describe('Scenario: The user cannot close a code review', () => {
    describe('Scenario outline: The code review does not exist', () => {
      beforeEach(() => {
        _.givenCurrentUser(createFakeId('1'));
        _.givenRequest({ codeReviewId: createFakeId('1') });
      });

      it('should throw a CodeReviewNotFoundError', async () => {
        await _.whenTheUserClosesTheCodeReview();
        await _.thenCodeReviewNotFoundErrorShouldBeThrown();
      });
    });
  });
});
