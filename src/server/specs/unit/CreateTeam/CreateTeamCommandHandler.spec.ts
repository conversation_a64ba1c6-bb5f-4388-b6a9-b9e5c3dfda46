import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { JOHN } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { CreateTeamSteps } from '@/src/server/specs/unit/CreateTeam/CreateTeamSteps';

describe('Feature: Creating a Team', () => {
  let _: CreateTeamSteps;

  beforeEach(() => {
    _ = new CreateTeamSteps();
  });

  describe('Scenario: the user successfully creates a team', () => {
    const TEAM_ID = createFakeId('1');
    const TEAM_NAME = 'My Awesome Team';

    it('should successfully create the team', async () => {
      _.givenCurrentUserIs(JOHN);
      _.givenRequest({ name: TEAM_NAME });

      await _.whenTheUserCreatesTheTeam();

      await _.thenTeamShouldBeCreatedWith({
        teamId: TEAM_ID,
        expected: {
          name: TEAM_NAME,
          ownerId: JOHN.craftUserId,
        },
      });
    });
  });
});
