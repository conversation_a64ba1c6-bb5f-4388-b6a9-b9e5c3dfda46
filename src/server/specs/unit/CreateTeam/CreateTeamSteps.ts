import { CreateTeamCommand } from '@/src/server/application/usecases/CreateTeam/CreateTeamCommand';
import { CreateTeamCommandHandler } from '@/src/server/application/usecases/CreateTeam/CreateTeamCommandHandler';
import { FakeIdentityProvider } from '@/src/server/infrastructure/providers/IdentityProvider/FakeIdentityProvider';
import { InMemoryTeamRepository } from '@/src/server/infrastructure/repositories/TeamRepository/InMemoryTeamRepository';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';

export class CreateTeamSteps {
  private currentUserId: string | null = null;
  private currentRequest: { name: string } | null = null;
  private error: Error | null = null;
  private readonly teamRepository: InMemoryTeamRepository;
  private readonly commandHandler: CreateTeamCommandHandler;
  private readonly identityProvider: FakeIdentityProvider;

  constructor() {
    this.teamRepository = new InMemoryTeamRepository();
    this.identityProvider = new FakeIdentityProvider();
    this.identityProvider.setNextIds(createFakeId('1'));
    this.commandHandler = new CreateTeamCommandHandler(this.teamRepository, this.identityProvider);
  }

  givenCurrentDateIs(currentDate: string) {
    vi.setSystemTime(currentDate);
  }

  givenCurrentUserIs({ craftUserId }: { craftUserId: string }) {
    this.currentUserId = craftUserId;
  }

  givenRequest(request: { name: string }) {
    this.currentRequest = request;
  }

  async whenTheUserCreatesTheTeam() {
    try {
      const command = new CreateTeamCommand(this.currentUserId!, this.currentRequest!.name);
      await this.commandHandler.handle(command);
    } catch (e) {
      this.error = e as Error;
    }
  }

  async thenTeamShouldBeCreatedWith({
    teamId,
    expected,
  }: {
    teamId: string;
    expected: {
      name: string;
      ownerId: string;
    };
  }) {
    const teamResult = await this.teamRepository.findById(teamId);
    const team = teamResult.getValue();
    expect(team.id()).toEqual(teamId);
    expect(team.get('name')).toEqual(expected.name);
    expect(team.get('ownerId')).toEqual(expected.ownerId);
    expect(this.error).toBeNull();
  }
}
