import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { DeleteFeedbackSteps } from '@/src/server/specs/unit/DeleteFeedback/DeleteFeedbackSteps';

describe('Feature: Deleting a Feedback', () => {
  let _: DeleteFeedbackSteps;

  beforeEach(() => {
    _ = new DeleteFeedbackSteps();
    _.givenCurrentDateIs('2025-01-01');
  });

  describe('Scenario: The user can delete a feedback', () => {
    beforeEach(() => {
      _.givenCurrentUser(createFakeId('1'));
      _.givenFeedbackExists({ id: createFakeId('1'), userId: createFakeId('1') });
      _.givenRequest({ feedbackId: createFakeId('1') });
    });

    it('should delete the feedback', async () => {
      await _.whenTheUserDeletesTheFeedback();
      await _.thenFeedbackShouldBeDeleted();
    });
  });

  describe('Scenario: The user cannot delete the feedback', () => {
    describe('Scenario outline: The feedback does not exist', () => {
      beforeEach(() => {
        _.givenCurrentUser(createFakeId('1'));
        _.givenRequest({ feedbackId: createFakeId('1') });
      });

      it('should return a FeedbackNotFoundError', async () => {
        await _.whenTheUserDeletesTheFeedback();
        await _.thenFeedbackNotFoundErrorShouldBeReturned();
      });
    });

    describe('Scenario outline: The user is not the owner of the feedback', () => {
      beforeEach(() => {
        _.givenCurrentUser(createFakeId('2'));
        _.givenFeedbackExists({ id: createFakeId('1'), userId: createFakeId('1') });
        _.givenRequest({ feedbackId: createFakeId('1') });
      });

      it('should return an UnauthorizedFeedbackDeletionError', async () => {
        await _.whenTheUserDeletesTheFeedback();
        await _.thenUnauthorizedFeedbackDeletionErrorShouldBeReturned();
      });

      it('should not delete the feedback', async () => {
        await _.whenTheUserDeletesTheFeedback();
        await _.thenFeedbackShouldNotBeDeleted();
      });
    });
  });
});
