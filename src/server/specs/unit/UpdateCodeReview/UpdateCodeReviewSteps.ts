import { UpdateCodeReviewCommand } from '@/src/server/application/usecases/UpdateCodeReview/UpdateCodeReviewCommand';
import { UpdateCodeReviewCommandHandler } from '@/src/server/application/usecases/UpdateCodeReview/UpdateCodeReviewCommandHandler';
import { CodeReview } from '@/src/server/domain/CodeReview/CodeReview';
import { FakeIdentityProvider } from '@/src/server/infrastructure/providers/IdentityProvider/FakeIdentityProvider';
import { InMemoryCodeReviewRepository } from '@/src/server/infrastructure/repositories/CodeReviewRepository/InMemoryCodeReviewRepository';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { type DomainError, Result } from '@evyweb/simple-ddd-toolkit';
import { vi } from 'vitest';

export class UpdateCodeReviewSteps {
  private readonly codeReviewRepository: InMemoryCodeReviewRepository;
  private readonly commandHandler: UpdateCodeReviewCommandHandler;
  private readonly identityProvider: FakeIdentityProvider;

  private currentRequest: {
    codeReviewId: string;
    title: string;
  } | null = null;
  private currentUserId: string | null = null;
  private result: Result<void, Error> = Result.fail(new Error('NO RESULT FOUND IN SCENARIO'));
  private error: DomainError | null = null;

  constructor() {
    this.identityProvider = new FakeIdentityProvider();
    this.identityProvider.setNextIds(createFakeId('1'), createFakeId('2'), createFakeId('3'), createFakeId('4'));
    this.codeReviewRepository = new InMemoryCodeReviewRepository();
    this.commandHandler = new UpdateCodeReviewCommandHandler(this.codeReviewRepository);
  }

  givenCurrentDateIs(date: string) {
    vi.setSystemTime(new Date(date));
  }

  givenCurrentUser(userId: string) {
    this.currentUserId = userId;
  }

  givenRequest(request: {
    codeReviewId: string;
    title: string;
  }) {
    this.currentRequest = request;
  }

  async givenExistingCodeReview(codeReview: {
    id: string;
    title: string;
    authorId: string;
  }) {
    const review = CodeReview.organize(codeReview.id, codeReview.title, codeReview.authorId).getValue();
    await this.codeReviewRepository.save(review);
  }

  async whenTheUserEditsTheCodeReview() {
    try {
      const command = new UpdateCodeReviewCommand(
        this.currentRequest!.codeReviewId,
        this.currentRequest!.title,
        this.currentUserId!
      );
      this.result = await this.commandHandler.handle(command);
      this.error = null;
    } catch (error) {
      this.error = error as DomainError;
    }
  }

  async thenCodeReviewShouldBeUpdatedWith(expected: {
    id: string;
    title: string;
  }) {
    const codeReviewResult = await this.codeReviewRepository.findById(expected.id);
    const codeReview = codeReviewResult.getValue();

    expect(codeReview.get('id').get('value')).toEqual(expected.id);
    expect(codeReview.get('title').get('value')).toEqual(expected.title);
    expect(this.result.isOk()).toBeTrue();
  }

  thenShouldReceiveError(errorType: string) {
    expect(this.error).not.toBeNull();
    expect(this.error?.__TAG).toEqual(errorType);
  }
}
