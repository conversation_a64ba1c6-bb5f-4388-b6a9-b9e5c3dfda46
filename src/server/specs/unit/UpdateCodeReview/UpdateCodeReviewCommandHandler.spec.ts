import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { UpdateCodeReviewSteps } from '@/src/server/specs/unit/UpdateCodeReview/UpdateCodeReviewSteps';

describe('Feature: Editing a code review', () => {
  let _: UpdateCodeReviewSteps;

  beforeEach(() => {
    _ = new UpdateCodeReviewSteps();
    _.givenCurrentDateIs('2025-02-23');
    _.givenCurrentUser(createFakeId('1'));
  });

  describe('Scenario: The user can edit a code review they created', () => {
    beforeEach(async () => {
      await _.givenExistingCodeReview({
        id: createFakeId('1'),
        title: 'ORIGINAL_TITLE',
        authorId: createFakeId('1'),
      });

      _.givenRequest({
        codeReviewId: createFakeId('1'),
        title: 'UPDATED_TITLE',
      });
    });

    it('should update the code review title', async () => {
      await _.whenTheUserEditsTheCodeReview();
      await _.thenCodeReviewShouldBeUpdatedWith({
        id: createFakeId('1'),
        title: 'UPDATED_TITLE',
      });
    });
  });

  describe('Scenario: The user cannot edit a code review that does not exist', () => {
    beforeEach(() => {
      _.givenRequest({
        codeReviewId: createFakeId('999'),
        title: 'UPDATED_TITLE',
      });
    });

    it('should return a not found error', async () => {
      await _.whenTheUserEditsTheCodeReview();
      _.thenShouldReceiveError('CodeReviewNotFoundError');
    });
  });

  describe('Scenario: The user cannot edit a code review created by someone else', () => {
    beforeEach(async () => {
      await _.givenExistingCodeReview({
        id: createFakeId('2'),
        title: 'ORIGINAL_TITLE',
        authorId: createFakeId('2'),
      });

      _.givenRequest({
        codeReviewId: createFakeId('2'),
        title: 'UPDATED_TITLE',
      });
    });

    it('should return an unauthorized error', async () => {
      await _.whenTheUserEditsTheCodeReview();
      _.thenShouldReceiveError('UnauthorizedToEditCodeReviewError');
    });
  });
});
