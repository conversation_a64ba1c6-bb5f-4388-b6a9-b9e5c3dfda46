import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { RejectTeamInvitationSteps } from '@/src/server/specs/unit/RejectTeamInvitation/RejectTeamInvitationSteps';

describe('Feature: Rejecting a Team Invitation', () => {
  let _: RejectTeamInvitationSteps;

  beforeEach(() => {
    _ = new RejectTeamInvitationSteps();
  });

  describe('Scenario: User rejects a pending invitation', () => {
    it('should successfully reject the invitation', async () => {
      const invitationId = createFakeId('1');
      const teamId = createFakeId('2');
      const userId = createFakeId('3');
      const email = '<EMAIL>';

      await _.givenAPendingInvitationExists({
        id: invitationId,
        teamId,
        email,
        invitedBy: createFakeId('4'),
      });

      await _.whenUserRejectsInvitation(userId, invitationId);

      await _.thenInvitationShouldBeRejected(invitationId);
    });
  });

  describe('Scenario: User tries to reject a non-existent invitation', () => {
    it('should fail with invitation not found error', async () => {
      const nonExistentInvitationId = createFakeId('5');
      const userId = createFakeId('6');

      await _.whenUserRejectsInvitation(userId, nonExistentInvitationId);

      await _.thenRequestShouldFailWithInvitationNotFound();
    });
  });

  describe('Scenario: User tries to reject an already accepted invitation', () => {
    it('should fail as the invitation is already accepted', async () => {
      const invitationId = createFakeId('7');
      const teamId = createFakeId('8');
      const userId = createFakeId('9');
      const email = '<EMAIL>';

      await _.givenAnAcceptedInvitationExists({
        id: invitationId,
        teamId,
        email,
        invitedBy: createFakeId('10'),
      });

      await _.whenUserRejectsInvitation(userId, invitationId);

      await _.thenRequestShouldFailAsInvitationIsNotPending();
    });
  });
});
