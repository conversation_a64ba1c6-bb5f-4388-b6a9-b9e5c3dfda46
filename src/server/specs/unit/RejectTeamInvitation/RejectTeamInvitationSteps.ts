import { RejectTeamInvitationCommand } from '@/src/server/application/usecases/RejectTeamInvitation/RejectTeamInvitationCommand';
import { RejectTeamInvitationCommandHandler } from '@/src/server/application/usecases/RejectTeamInvitation/RejectTeamInvitationCommandHandler';
import { TeamInvitation } from '@/src/server/domain/TeamInvitation/TeamInvitation';
import { InvitationNotPendingError } from '@/src/server/domain/TeamInvitation/errors/InvitationNotPendingError';
import { TeamInvitationNotFoundError } from '@/src/server/domain/TeamInvitation/errors/TeamInvitationNotFoundError';
import { TeamInvitationId } from '@/src/server/domain/TeamInvitation/valueObjects/TeamInvitationId';
import { InMemoryTeamInvitationRepository } from '@/src/server/infrastructure/repositories/TeamInvitationRepository/InMemoryTeamInvitationRepository';
import type { Result } from '@evyweb/simple-ddd-toolkit';

export class RejectTeamInvitationSteps {
  private readonly teamInvitationRepository: InMemoryTeamInvitationRepository;
  private readonly handler: RejectTeamInvitationCommandHandler;
  private result: Result<void, TeamInvitationNotFoundError | InvitationNotPendingError> | null = null;

  constructor() {
    this.teamInvitationRepository = new InMemoryTeamInvitationRepository();
    this.handler = new RejectTeamInvitationCommandHandler(this.teamInvitationRepository);
  }

  async givenAPendingInvitationExists(properties: {
    id: string;
    teamId: string;
    email: string;
    invitedBy: string;
  }): Promise<void> {
    const invitation = TeamInvitation.createPendingInvitation({
      id: TeamInvitationId.createFrom(properties.id),
      teamId: properties.teamId,
      email: properties.email,
      invitedBy: properties.invitedBy,
    }).getValue();

    await this.teamInvitationRepository.save(invitation);
  }

  async givenAnAcceptedInvitationExists(properties: {
    id: string;
    teamId: string;
    email: string;
    invitedBy: string;
  }): Promise<void> {
    const invitation = TeamInvitation.createPendingInvitation({
      id: TeamInvitationId.createFrom(properties.id),
      teamId: properties.teamId,
      email: properties.email,
      invitedBy: properties.invitedBy,
    }).getValue();

    invitation.accept();
    await this.teamInvitationRepository.save(invitation);
  }

  async whenUserRejectsInvitation(userId: string, invitationId: string): Promise<void> {
    const command = new RejectTeamInvitationCommand(userId, invitationId);
    this.result = await this.handler.handle(command);
  }

  async thenInvitationShouldBeRejected(invitationId: string): Promise<void> {
    expect(this.result?.isOk()).toBe(true);

    const invitationResult = await this.teamInvitationRepository.findById(invitationId);
    expect(invitationResult.isOk()).toBe(true);

    const invitation = invitationResult.getValue();
    expect(invitation.isDeclined()).toBe(true);
  }

  async thenRequestShouldFailWithInvitationNotFound(): Promise<void> {
    expect(this.result?.isFail()).toBe(true);
    expect(this.result?.getError()).toBeInstanceOf(TeamInvitationNotFoundError);
  }

  async thenRequestShouldFailAsInvitationIsNotPending(): Promise<void> {
    expect(this.result?.isFail()).toBe(true);
    expect(this.result?.getError()).toBeInstanceOf(InvitationNotPendingError);
  }
}
