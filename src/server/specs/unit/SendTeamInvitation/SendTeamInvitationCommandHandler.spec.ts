import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { SendTeamInvitationSteps } from '@/src/server/specs/unit/SendTeamInvitation/SendTeamInvitationSteps';

describe('Feature: Sending a Team Invitation', () => {
  let _: SendTeamInvitationSteps;

  beforeEach(() => {
    _ = new SendTeamInvitationSteps();
  });

  describe('Scenario: Team owner sends an invitation', () => {
    it('should create a new invitation for the recipient', async () => {
      const teamId = createFakeId('1');
      const ownerId = createFakeId('2');
      const email = '<EMAIL>';
      await _.givenATeamExists(teamId, ownerId);

      await _.whenUserSendsInvitation(ownerId, teamId, email);

      await _.thenInvitationShouldBeSent();
    });
  });

  describe('Scenario: Non-owner tries to send an invitation', () => {
    it('should reject the request as only team owners can send invitations', async () => {
      const teamId = createFakeId('3');
      const ownerId = createFakeId('4');
      const nonOwnerId = createFakeId('5');
      const email = '<EMAIL>';
      await _.givenATeamExists(teamId, ownerId);

      await _.whenUserSendsInvitation(nonOwnerId, teamId, email);

      await _.thenRequestShouldBeRejectedAsNotTeamOwner();
    });
  });

  describe('Scenario: Team owner tries to send an invitation to an email that already has a pending invitation', () => {
    it('should reject the duplicate invitation as one is already pending', async () => {
      const teamId = createFakeId('6');
      const ownerId = createFakeId('7');
      const email = '<EMAIL>';
      await _.givenATeamExists(teamId, ownerId);
      await _.givenAPendingInvitationExists(teamId, email, ownerId);

      await _.whenUserSendsInvitation(ownerId, teamId, email);

      await _.thenRequestShouldBeRejectedAsDuplicateInvitation();
    });
  });

  describe('Scenario: User tries to send an invitation for a non-existent team', () => {
    it('should reject the request as the team does not exist', async () => {
      const nonExistentTeamId = createFakeId('8');
      const userId = createFakeId('9');
      const email = '<EMAIL>';

      await _.whenUserSendsInvitation(userId, nonExistentTeamId, email);

      await _.thenRequestShouldBeRejectedAsTeamNotFound();
    });
  });
});
