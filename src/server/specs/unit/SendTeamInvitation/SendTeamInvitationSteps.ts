import { SendTeamInvitationCommand } from '@/src/server/application/usecases/SendTeamInvitation/SendTeamInvitationCommand';
import { SendTeamInvitationCommandHandler } from '@/src/server/application/usecases/SendTeamInvitation/SendTeamInvitationCommandHandler';
import { Team } from '@/src/server/domain/Team/Team';
import { NotTeamOwnerError } from '@/src/server/domain/Team/errors/NotTeamOwnerError';
import { TeamNotFoundError } from '@/src/server/domain/Team/errors/TeamNotFoundError';
import { TeamId } from '@/src/server/domain/Team/valueObjects/TeamId';
import { TeamInvitation } from '@/src/server/domain/TeamInvitation/TeamInvitation';
import { TeamInvitationAlreadyExistsError } from '@/src/server/domain/TeamInvitation/errors/TeamInvitationAlreadyExistsError';
import { TeamInvitationId } from '@/src/server/domain/TeamInvitation/valueObjects/TeamInvitationId';
import { FakeIdentityProvider } from '@/src/server/infrastructure/providers/IdentityProvider/FakeIdentityProvider';
import { InMemoryTeamInvitationRepository } from '@/src/server/infrastructure/repositories/TeamInvitationRepository/InMemoryTeamInvitationRepository';
import { InMemoryTeamMemberRepository } from '@/src/server/infrastructure/repositories/TeamMemberRepository/InMemoryTeamMemberRepository';
import { InMemoryTeamRepository } from '@/src/server/infrastructure/repositories/TeamRepository/InMemoryTeamRepository';
import { TeamInvitationService } from '@/src/server/infrastructure/services/TeamInvitationService/TeamInvitationService';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import type { Result } from '@evyweb/simple-ddd-toolkit';

export class SendTeamInvitationSteps {
  private readonly teamRepository: InMemoryTeamRepository;
  private readonly teamInvitationRepository: InMemoryTeamInvitationRepository;
  private readonly teamMemberRepository: InMemoryTeamMemberRepository;
  private readonly teamInvitationService: TeamInvitationService;
  private readonly handler: SendTeamInvitationCommandHandler;
  private result: Result<string, NotTeamOwnerError | TeamInvitationAlreadyExistsError | TeamNotFoundError> | null =
    null;
  private readonly identityProvider: FakeIdentityProvider;

  constructor() {
    this.teamRepository = new InMemoryTeamRepository();
    this.teamInvitationRepository = new InMemoryTeamInvitationRepository();
    this.teamMemberRepository = new InMemoryTeamMemberRepository();
    this.identityProvider = new FakeIdentityProvider();
    this.identityProvider.setNextIds(createFakeId('1'));
    this.teamInvitationService = new TeamInvitationService(
      this.teamRepository,
      this.teamInvitationRepository,
      this.teamMemberRepository
    );
    this.handler = new SendTeamInvitationCommandHandler(
      this.teamInvitationService,
      this.teamInvitationRepository,
      this.identityProvider
    );
  }

  async givenATeamExists(teamId: string, ownerId: string): Promise<void> {
    const team = Team.create({
      id: TeamId.createFrom(teamId),
      name: 'Test Team',
      ownerId,
      createdAt: '2023-01-01T00:00:00.000Z',
    }).getValue();

    await this.teamRepository.save(team);
  }

  async givenAPendingInvitationExists(teamId: string, email: string, invitedBy: string): Promise<void> {
    const invitation = TeamInvitation.createPendingInvitation({
      id: TeamInvitationId.createFrom(createFakeId('2')),
      teamId,
      email,
      invitedBy,
    }).getValue();

    await this.teamInvitationRepository.save(invitation);
  }

  async whenUserSendsInvitation(currentUserId: string, teamId: string, email: string): Promise<void> {
    const command = new SendTeamInvitationCommand(currentUserId, teamId, email);
    this.result = await this.handler.handle(command);
  }

  async thenInvitationShouldBeSent(): Promise<void> {
    expect(this.result?.isOk()).toBe(true);

    const invitationId = this.result?.getValue();
    expect(invitationId).toBe(createFakeId('1'));
  }

  async thenRequestShouldBeRejectedAsNotTeamOwner(): Promise<void> {
    expect(this.result?.isFail()).toBe(true);
    expect(this.result?.getError()).toBeInstanceOf(NotTeamOwnerError);
  }

  async thenRequestShouldBeRejectedAsDuplicateInvitation(): Promise<void> {
    expect(this.result?.isFail()).toBe(true);
    expect(this.result?.getError()).toBeInstanceOf(TeamInvitationAlreadyExistsError);
  }

  async thenRequestShouldBeRejectedAsTeamNotFound(): Promise<void> {
    expect(this.result?.isFail()).toBe(true);
    expect(this.result?.getError()).toBeInstanceOf(TeamNotFoundError);
  }
}
