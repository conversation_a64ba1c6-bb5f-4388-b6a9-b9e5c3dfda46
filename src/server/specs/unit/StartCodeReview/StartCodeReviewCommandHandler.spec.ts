import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { StartCodeReviewSteps } from '@/src/server/specs/unit/StartCodeReview/StartCodeReviewSteps';

describe('Feature: Starting a code review', () => {
  let _: StartCodeReviewSteps;

  beforeEach(() => {
    _ = new StartCodeReviewSteps();
    _.givenCurrentDateIs('2025-02-23');
  });

  describe('Scenario: The user can start a code review', () => {
    beforeEach(() => {
      _.givenCurrentUser(createFakeId('1'));
      _.givenCodeReviewExists({
        id: createFakeId('1'),
        title: 'ANY_VALID_TITLE',
        authorId: createFakeId('1'),
      });
      _.givenRequest({ codeReviewId: createFakeId('1') });
    });

    it('should change the code review status to open', async () => {
      await _.whenTheUserStartsTheCodeReview();
      await _.thenCodeReviewShouldBeStarted();
    });
  });

  describe('Scenario: The user cannot start a code review', () => {
    describe('Scenario outline: The code review does not exist', () => {
      beforeEach(() => {
        _.givenCurrentUser(createFakeId('1'));
        _.givenRequest({ codeReviewId: createFakeId('1') });
      });

      it('should throw a CodeReviewNotFoundError', async () => {
        await _.whenTheUserStartsTheCodeReview();
        await _.thenCodeReviewNotFoundErrorShouldBeThrown();
      });
    });
  });
});
