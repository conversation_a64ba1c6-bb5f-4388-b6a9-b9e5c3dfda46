import { StartCodeReviewCommand } from '@/src/server/application/usecases/StartCodeReview/StartCodeReviewCommand';
import { StartCodeReviewCommandHandler } from '@/src/server/application/usecases/StartCodeReview/StartCodeReviewCommandHandler';
import { CodeReview } from '@/src/server/domain/CodeReview/CodeReview';
import { CodeReviewNotFoundError } from '@/src/server/domain/CodeReview/errors/CodeReviewNotFoundError';
import { InMemoryCodeReviewRepository } from '@/src/server/infrastructure/repositories/CodeReviewRepository/InMemoryCodeReviewRepository';
import { vi } from 'vitest';

export class StartCodeReviewSteps {
  private readonly codeReviewRepository: InMemoryCodeReviewRepository;
  private readonly commandHandler: StartCodeReviewCommandHandler;

  private currentRequest: {
    codeReviewId: string;
  } | null = null;
  private currentUserId: string | null = null;
  private error: Error | null = null;

  constructor() {
    this.codeReviewRepository = new InMemoryCodeReviewRepository();
    this.commandHandler = new StartCodeReviewCommandHandler(this.codeReviewRepository);
  }

  givenCurrentDateIs(date: string) {
    vi.setSystemTime(new Date(date));
  }

  givenCurrentUser(userId: string) {
    this.currentUserId = userId;
  }

  async givenCodeReviewExists(codeReview: { id: string; title: string; authorId: string }) {
    const codeReviewResult = CodeReview.organize(codeReview.id, codeReview.title, codeReview.authorId);
    await this.codeReviewRepository.save(codeReviewResult.getValue());
  }

  givenRequest(request: { codeReviewId: string }) {
    this.currentRequest = request;
  }

  async whenTheUserStartsTheCodeReview() {
    try {
      const command = new StartCodeReviewCommand(this.currentRequest!.codeReviewId, this.currentUserId!);
      await this.commandHandler.handle(command);
    } catch (e) {
      this.error = e as Error;
    }
  }

  async thenCodeReviewShouldBeStarted() {
    const codeReviewResult = await this.codeReviewRepository.findById(this.currentRequest!.codeReviewId);
    const codeReview = codeReviewResult.getValue();
    expect(codeReview.get('status')).toEqual('open');
  }

  async thenCodeReviewNotFoundErrorShouldBeThrown() {
    expect(this.error).toBeInstanceOf(CodeReviewNotFoundError);
  }
}
