import { DeleteCommentCommand } from '@/src/server/application/usecases/DeleteComment/DeleteCommentCommand';
import { DeleteCommentCommandHandler } from '@/src/server/application/usecases/DeleteComment/DeleteCommentCommandHandler';
import { Comment } from '@/src/server/domain/Comment/Comment';
import { CommentNotFoundError } from '@/src/server/domain/Comment/errors/CommentNotFoundError';
import { UnauthorizedCommentDeletionError } from '@/src/server/domain/Comment/errors/UnauthorizedCommentDeletionError';
import { CommentId } from '@/src/server/domain/Comment/valueObjects/CommentId';
import { InMemoryCommentRepository } from '@/src/server/infrastructure/repositories/CommentRepository/InMemoryCommentRepository';
import type { Result } from '@evyweb/simple-ddd-toolkit';

export class DeleteCommentSteps {
  private readonly repository: InMemoryCommentRepository;
  private readonly commandHandler: DeleteCommentCommandHandler;
  private currentUserId: string | null = null;
  private currentRequest: { commentId: string } | null = null;
  private result: Result<void, Error> | null = null;

  constructor() {
    this.repository = new InMemoryCommentRepository();
    this.commandHandler = new DeleteCommentCommandHandler(this.repository);
  }

  givenCurrentUser(userId: string) {
    this.currentUserId = userId;
  }

  async givenCommentExists(comment: { id: string; userId: string; feedbackId: string; content: string }) {
    const commentCreation = Comment.create({
      id: CommentId.createFrom(comment.id),
      feedbackId: comment.feedbackId,
      content: comment.content,
      userId: comment.userId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'published',
    });
    const createdComment = commentCreation.getValue();
    await this.repository.save(createdComment);
  }

  givenRequest(request: { commentId: string }) {
    this.currentRequest = request;
  }

  async whenTheUserDeletesTheComment() {
    const command = new DeleteCommentCommand(this.currentRequest!.commentId, this.currentUserId!);
    this.result = await this.commandHandler.handle(command);
  }

  async thenCommentShouldBeDeleted() {
    expect(this.result?.isOk()).toBe(true);

    const commentResult = await this.repository.findById(this.currentRequest!.commentId);
    expect(commentResult.isOk()).toBe(true);

    const comment = commentResult.getValue();
    expect(comment.get('status')).toBe('deleted');
  }

  thenCommentNotFoundErrorShouldBeReturned() {
    expect(this.result?.isFail()).toBe(true);
    expect(this.result?.getError()).toBeInstanceOf(CommentNotFoundError);
  }

  thenUnauthorizedDeletionErrorShouldBeReturned() {
    expect(this.result?.isFail()).toBe(true);
    expect(this.result?.getError()).toBeInstanceOf(UnauthorizedCommentDeletionError);
  }
}
