import { AddTeamMemberCommand } from '@/src/server/application/usecases/AddTeamMember/AddTeamMemberCommand';
import { AddTeamMemberCommandHandler } from '@/src/server/application/usecases/AddTeamMember/AddTeamMemberCommandHandler';
import { Team } from '@/src/server/domain/Team/Team';
import { NotTeamOwnerError } from '@/src/server/domain/Team/errors/NotTeamOwnerError';
import { TeamId } from '@/src/server/domain/Team/valueObjects/TeamId';
import { TeamMember } from '@/src/server/domain/TeamMember/TeamMember';
import { TeamMemberAlreadyExistsError } from '@/src/server/domain/TeamMember/errors/TeamMemberAlreadyExistsError';
import { TeamMemberId } from '@/src/server/domain/TeamMember/valueObjects/TeamMemberId';
import { FakeIdentityProvider } from '@/src/server/infrastructure/providers/IdentityProvider/FakeIdentityProvider';
import { InMemoryTeamMemberRepository } from '@/src/server/infrastructure/repositories/TeamMemberRepository/InMemoryTeamMemberRepository';
import { InMemoryTeamRepository } from '@/src/server/infrastructure/repositories/TeamRepository/InMemoryTeamRepository';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';

export class AddTeamMemberSteps {
  private currentUserId: string | null = null;
  private currentRequest: { teamId: string; userId: string } | null = null;
  private error: Error | null = null;
  private readonly teamMemberRepository: InMemoryTeamMemberRepository;
  private readonly teamRepository: InMemoryTeamRepository;
  private readonly commandHandler: AddTeamMemberCommandHandler;
  private readonly identityProvider: FakeIdentityProvider;

  constructor() {
    this.teamMemberRepository = new InMemoryTeamMemberRepository();
    this.teamRepository = new InMemoryTeamRepository();
    this.identityProvider = new FakeIdentityProvider();
    this.identityProvider.setNextIds(createFakeId('1'));
    this.commandHandler = new AddTeamMemberCommandHandler(
      this.teamMemberRepository,
      this.teamRepository,
      this.identityProvider
    );
  }

  givenCurrentDateIs(currentDate: string) {
    vi.setSystemTime(currentDate);
  }

  givenCurrentUser(currentUser: string) {
    this.currentUserId = currentUser;
  }

  givenTeamExists(team: { id: string; name: string; ownerId: string; createdAt: string }) {
    const teamEntity = Team.create({
      id: TeamId.createFrom(team.id),
      name: team.name,
      ownerId: team.ownerId,
      createdAt: team.createdAt,
    }).getValue();

    this.teamRepository.save(teamEntity);
  }

  givenTeamMemberExists(teamMember: {
    id: string;
    teamId: string;
    userId: string;
    addedBy: string;
    createdAt: string;
  }) {
    const teamMemberEntity = TeamMember.create({
      id: TeamMemberId.createFrom(teamMember.id),
      teamId: teamMember.teamId,
      userId: teamMember.userId,
      addedBy: teamMember.addedBy,
      createdAt: teamMember.createdAt,
    }).getValue();

    this.teamMemberRepository.save(teamMemberEntity);
  }

  givenRequest(request: { teamId: string; userId: string }) {
    this.currentRequest = request;
  }

  async whenTheUserAddsTheMemberToTheTeam() {
    try {
      const command = new AddTeamMemberCommand(
        this.currentUserId!,
        this.currentRequest!.teamId,
        this.currentRequest!.userId
      );
      await this.commandHandler.handle(command);
    } catch (e) {
      this.error = e as Error;
    }
  }

  async thenTeamMemberShouldBeAddedWith({
    teamMemberId,
    expected,
  }: {
    teamMemberId: string;
    expected: {
      teamId: string;
      userId: string;
      addedBy: string;
      createdAt: string;
    };
  }) {
    const teamMemberResult = await this.teamMemberRepository.findById(teamMemberId);
    const teamMember = teamMemberResult.getValue();
    expect(teamMember.id()).toEqual(teamMemberId);
    expect(teamMember.get('teamId')).toEqual(expected.teamId);
    expect(teamMember.get('userId')).toEqual(expected.userId);
    expect(teamMember.get('addedBy')).toEqual(expected.addedBy);
    expect(teamMember.get('createdAt')).toEqual(expected.createdAt);
    expect(this.error).toBeNull();
  }

  thenShouldThrowNotTeamOwnerError() {
    expect(this.error).toBeInstanceOf(NotTeamOwnerError);
  }

  thenShouldThrowTeamMemberAlreadyExistsError() {
    expect(this.error).toBeInstanceOf(TeamMemberAlreadyExistsError);
  }
}
