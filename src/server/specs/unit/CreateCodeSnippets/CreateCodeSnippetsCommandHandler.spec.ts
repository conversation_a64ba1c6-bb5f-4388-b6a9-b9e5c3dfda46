import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { CreateCodeSnippetsSteps } from '@/src/server/specs/unit/CreateCodeSnippets/CreateCodeSnippetsSteps';

describe('Feature: Creating code snippets', () => {
  let _: CreateCodeSnippetsSteps;

  beforeEach(() => {
    _ = new CreateCodeSnippetsSteps();
    _.givenCurrentDateIs('2025-02-23');
  });

  describe('Scenario: The user can create code snippets for a code review', () => {
    beforeEach(() => {
      _.givenRequest({
        codeReviewId: createFakeId('1'),
        snippets: [
          {
            content: 'console.log("snippet 1");',
            filename: 'snippet-1',
            language: 'javascript',
          },
          {
            content: 'console.log("snippet 2");',
            filename: 'snippet-2',
            language: 'typescript',
          },
        ],
      });
    });

    it('should save the code snippets', async () => {
      await _.whenTheUserCreatesTheCodeSnippets();
      await _.thenCodeSnippetsShouldBeSavedWith([
        {
          id: createFakeId('1'),
          content: 'console.log("snippet 1");',
          filename: 'snippet-1',
          language: 'javascript',
        },
        {
          id: createFakeId('2'),
          content: 'console.log("snippet 2");',
          filename: 'snippet-2',
          language: 'typescript',
        },
      ]);
    });
  });

  describe('Scenario: The user provides invalid code snippet content', () => {
    beforeEach(() => {
      _.givenRequest({
        codeReviewId: createFakeId('1'),
        snippets: [
          {
            content: '',
            filename: 'snippet-1',
            language: 'javascript',
          },
        ],
      });
    });

    it('should fail with invalid content error', async () => {
      await _.thenShouldFailWithInvalidContentError();
    });
  });
});
