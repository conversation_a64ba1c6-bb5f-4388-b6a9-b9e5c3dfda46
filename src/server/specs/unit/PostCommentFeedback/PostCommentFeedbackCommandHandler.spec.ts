import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import {
  END_LINE_LESS_THAN_ZERO_COMMENT_REQUEST,
  START_LINE_GREATER_THAN_END_LINE_COMMENT_REQUEST,
  START_LINE_LESS_THAN_ZERO_COMMENT_REQUEST,
  TOO_LONG_CONTENT_COMMENT_REQUEST,
} from '@/src/server/specs/helpers/factories/fakeInvalidFeedbackRequests';
import {
  VALID_COMMENT_EXPECTATION,
  VALID_COMMENT_REQUEST,
} from '@/src/server/specs/helpers/factories/fakeValidFeedbackRequests';
import { PostCommentFeedbackSteps } from '@/src/server/specs/unit/PostCommentFeedback/PostCommentFeedbackSteps';

describe('Feature: Posting a Comment Feedback', () => {
  let _: PostCommentFeedbackSteps;

  beforeEach(() => {
    _ = new PostCommentFeedbackSteps();
    _.givenCurrentDateIs('2025-01-01');
  });

  describe.each([
    {
      request: { ...VALID_COMMENT_REQUEST, userId: createFakeId('1') },
      description: 'valid content',
    },
  ])('Scenario: The user can post a comment feedback with a $description', ({ request }) => {
    beforeEach(() => {
      _.givenRequest(request);
    });

    it('should save the feedback', async () => {
      await _.whenTheUserPostsTheCommentFeedback();
      await _.thenCommentFeedbackShouldBeSavedWith(VALID_COMMENT_EXPECTATION);
    });

    it('should return the feedback id', async () => {
      await _.whenTheUserPostsTheCommentFeedback();
      await _.thenFeedbackIdShouldBeReturned(createFakeId('1'));
    });
  });

  describe('Scenario: The user cannot post the comment feedback', () => {
    describe.each([
      {
        request: { ...TOO_LONG_CONTENT_COMMENT_REQUEST, userId: createFakeId('1') },
        description: 'too long content',
      },
    ])('Scenario outline: The content is invalid ($description)', ({ request }) => {
      beforeEach(() => {
        _.givenRequest(request);
      });

      it('should return an InvalidFeedbackContentError', async () => {
        await _.whenTheUserPostsTheCommentFeedback();
        await _.thenInvalidFeedbackContentErrorShouldBeReturned();
      });
    });

    describe.each([
      {
        request: { ...START_LINE_GREATER_THAN_END_LINE_COMMENT_REQUEST, userId: createFakeId('1') },
        description: 'start line greater than end line',
      },
      {
        request: { ...START_LINE_LESS_THAN_ZERO_COMMENT_REQUEST, userId: createFakeId('1') },
        description: 'start line less than zero',
      },
      {
        request: { ...END_LINE_LESS_THAN_ZERO_COMMENT_REQUEST, userId: createFakeId('1') },
        description: 'end line less than zero',
      },
    ])('Scenario outline: The line range is invalid ($description)', ({ request }) => {
      beforeEach(() => {
        _.givenRequest(request);
      });

      it('should return an InvalidFeedbackLineRangeError', async () => {
        await _.whenTheUserPostsTheCommentFeedback();
        await _.thenInvalidFeedbackLineRangeErrorShouldBeReturned();
      });
    });
  });
});
