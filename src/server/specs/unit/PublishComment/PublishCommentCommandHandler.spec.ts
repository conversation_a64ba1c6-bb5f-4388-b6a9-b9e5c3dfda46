import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { PublishCommentSteps } from '@/src/server/specs/unit/PublishComment/PublishCommentSteps';

describe('Feature: Publishing a Comment', () => {
  let _: PublishCommentSteps;

  beforeEach(() => {
    _ = new PublishCommentSteps();
  });

  describe('Scenario: the user successfully publishes a comment on a feedback', () => {
    const COMMENT_ID = createFakeId('1');
    const FEEDBACK_ID = createFakeId('1');
    const USER_ID = createFakeId('1');
    const CREATION_DATE = new Date(Date.UTC(2025, 0, 1, 0, 0, 0)).toISOString();
    const ISO_CREATION_DATE = '2025-01-01T00:00:00.000Z';

    it('should successfully publish the comment', async () => {
      _.givenCurrentDateIs(CREATION_DATE);
      _.givenCurrentUser(USER_ID);
      _.givenRequest({ feedbackId: FEEDBACK_ID, content: 'ANY_VALID_CONTENT' });
      await _.whenTheUserPublishesTheComment();
      await _.thenCommentShouldBePublishedWith({
        commentId: COMMENT_ID,
        expected: {
          feedbackId: FEEDBACK_ID,
          content: 'ANY_VALID_CONTENT',
          status: 'published',
          userId: USER_ID,
          createdAt: ISO_CREATION_DATE,
          updatedAt: ISO_CREATION_DATE,
        },
      });
    });
  });

  describe('Scenario: the user successfully replies to a comment', () => {
    const COMMENT_ID = createFakeId('1');
    const FEEDBACK_ID = createFakeId('2');
    const PARENT_ID = createFakeId('3');
    const USER_ID = createFakeId('2');
    const CREATION_DATE = new Date(Date.UTC(2026, 0, 3, 18, 36, 59)).toISOString();
    const ISO_CREATION_DATE = '2026-01-03T18:36:59.000Z';

    it('should successfully publish the comment with a reference to the parent comment', async () => {
      _.givenCurrentDateIs(CREATION_DATE);
      _.givenCurrentUser(USER_ID);
      _.givenRequest({ feedbackId: FEEDBACK_ID, content: 'ANY_OTHER_VALID_CONTENT', parentId: PARENT_ID });
      await _.whenTheUserPublishesTheComment();
      await _.thenCommentShouldBePublishedWith({
        commentId: COMMENT_ID,
        expected: {
          feedbackId: FEEDBACK_ID,
          content: 'ANY_OTHER_VALID_CONTENT',
          status: 'published',
          userId: USER_ID,
          createdAt: ISO_CREATION_DATE,
          updatedAt: ISO_CREATION_DATE,
          parentId: PARENT_ID,
        },
      });
    });
  });
});
