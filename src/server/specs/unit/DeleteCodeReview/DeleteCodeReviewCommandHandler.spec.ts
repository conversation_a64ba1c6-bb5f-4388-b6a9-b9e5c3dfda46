import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { DeleteCodeReviewSteps } from '@/src/server/specs/unit/DeleteCodeReview/DeleteCodeReviewSteps';

describe('Feature: Deleting a code review', () => {
  let _: DeleteCodeReviewSteps;

  beforeEach(() => {
    _ = new DeleteCodeReviewSteps();
  });

  describe('Scenario: The user can delete a code review', () => {
    beforeEach(() => {
      _.givenCurrentUser(createFakeId('1'));
      _.givenCodeReviewExists({
        id: createFakeId('1'),
        title: 'ANY_VALID_TITLE',
        authorId: createFakeId('1'),
      });
      _.givenRequest({
        codeReviewId: createFakeId('1'),
      });
    });

    it('should change the code review status to deleted', async () => {
      await _.whenTheUserDeletesTheCodeReview();
      await _.thenCodeReviewShouldBeDeleted();
    });
  });

  describe('Scenario: The user can delete an open code review', () => {
    beforeEach(() => {
      _.givenCurrentUser(createFakeId('1'));
      _.givenCodeReviewExists({
        id: createFakeId('1'),
        title: 'ANY_VALID_TITLE',
        authorId: createFakeId('1'),
      });
      _.givenRequest({
        codeReviewId: createFakeId('1'),
      });
    });

    it('should change the code review status to deleted', async () => {
      await _.whenTheUserDeletesTheCodeReview();
      await _.thenCodeReviewShouldBeDeleted();
    });
  });

  describe('Scenario: The user cannot delete a code review', () => {
    describe('Scenario outline: The code review does not exist', () => {
      beforeEach(() => {
        _.givenCurrentUser(createFakeId('1'));
        _.givenRequest({
          codeReviewId: createFakeId('1'),
        });
      });

      it('should throw a CodeReviewNotFoundError', async () => {
        await _.whenTheUserDeletesTheCodeReview();
        await _.thenCodeReviewNotFoundErrorShouldBeThrown();
      });
    });

    describe('Scenario outline: The user is not the author of the code review', () => {
      beforeEach(() => {
        _.givenCurrentUser(createFakeId('2'));
        _.givenCodeReviewExists({
          id: createFakeId('1'),
          title: 'ANY_VALID_TITLE',
          authorId: createFakeId('1'),
        });
        _.givenRequest({
          codeReviewId: createFakeId('1'),
        });
      });

      it('should throw an UnauthorizedDeleteCodeReviewError', async () => {
        await _.whenTheUserDeletesTheCodeReview();
        await _.thenUnauthorizedDeleteErrorShouldBeThrown();
      });
    });
  });
});
