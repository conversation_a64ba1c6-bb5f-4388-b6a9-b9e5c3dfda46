import { DeleteCodeReviewCommand } from '@/src/server/application/usecases/DeleteCodeReview/DeleteCodeReviewCommand';
import { DeleteCodeReviewCommandHandler } from '@/src/server/application/usecases/DeleteCodeReview/DeleteCodeReviewCommandHandler';
import { CodeReview } from '@/src/server/domain/CodeReview/CodeReview';
import { CodeReviewNotFoundError } from '@/src/server/domain/CodeReview/errors/CodeReviewNotFoundError';
import { UnauthorizedDeleteCodeReviewError } from '@/src/server/domain/CodeReview/errors/UnauthorizedDeleteCodeReviewError';
import { InMemoryCodeReviewRepository } from '@/src/server/infrastructure/repositories/CodeReviewRepository/InMemoryCodeReviewRepository';

export class DeleteCodeReviewSteps {
  private readonly codeReviewRepository: InMemoryCodeReviewRepository;
  private readonly commandHandler: DeleteCodeReviewCommandHandler;
  private currentRequest: DeleteCodeReviewCommand | null = null;
  private error: Error | null = null;
  private currentUserId: string | null = null;

  constructor() {
    this.codeReviewRepository = new InMemoryCodeReviewRepository();
    this.commandHandler = new DeleteCodeReviewCommandHandler(this.codeReviewRepository);
  }

  givenCurrentUser(userId: string) {
    this.currentUserId = userId;
  }

  async givenCodeReviewExists(codeReview: { id: string; title: string; authorId: string; initialStatus?: string }) {
    const codeReviewResult = CodeReview.organize(codeReview.id, codeReview.title, codeReview.authorId);
    const createdCodeReview = codeReviewResult.getValue();

    if (codeReview.initialStatus) {
      createdCodeReview.set('status', codeReview.initialStatus);
    }

    await this.codeReviewRepository.save(createdCodeReview);
  }

  givenRequest(request: { codeReviewId: string; userId?: string }) {
    const userId = request.userId || this.currentUserId || '';
    this.currentRequest = new DeleteCodeReviewCommand(request.codeReviewId, userId);
  }

  async whenTheUserDeletesTheCodeReview() {
    try {
      await this.commandHandler.handle(this.currentRequest!);
    } catch (e) {
      this.error = e as Error;
    }
  }

  async thenCodeReviewShouldBeDeleted() {
    const codeReviewResult = await this.codeReviewRepository.findById(this.currentRequest!.codeReviewId);
    const codeReview = codeReviewResult.getValue();
    expect(codeReview.get('status')).toEqual('deleted');
  }

  async thenCodeReviewNotFoundErrorShouldBeThrown() {
    expect(this.error).toBeInstanceOf(CodeReviewNotFoundError);
  }

  async thenUnauthorizedDeleteErrorShouldBeThrown() {
    expect(this.error).toBeInstanceOf(UnauthorizedDeleteCodeReviewError);
  }
}
