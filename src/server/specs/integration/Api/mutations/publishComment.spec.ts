import { api } from '@/convex/_generated/api';
import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { JOHN } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { createCraftUsers } from '@/src/server/specs/integration/helpers/createCraftUsers';
import { setTodayIs } from '@/src/server/specs/integration/helpers/setTodayIs';
import { getOneFrom } from 'convex-helpers/server/relationships';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('Publish Comment', () => {
  let asJohn: TestConvexForDataModel<DataModel>;

  beforeEach(async () => {
    const testDate = new Date(Date.UTC(2023, 10, 4, 13, 30, 0));
    setTodayIs(testDate);
    asJohn = convexTest(schema).withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });
    await createCraftUsers(asJohn, [JOHN]);
  });

  describe('When the user publishes a comment', () => {
    it('should publish the comment successfully', async () => {
      // Arrange
      const request = {
        feedbackId: '00000000-0000-0000-0000-000000000001',
        content: 'Hello world',
      };

      // Act
      const commentId = await publishComment(asJohn, request);

      // Assert
      const commentDocument = await asJohn.run((ctx) => getOneFrom(ctx.db, 'comments', 'by_commentId', commentId));

      const comment = commentDocument!;
      expect(comment.commentId).toEqual(commentId);
      expect(comment.feedbackId).toEqual('00000000-0000-0000-0000-000000000001');
      expect(comment.content).toEqual('Hello world');
      expect(comment.status).toEqual('published');
      expect(comment.userId).toEqual('00000000-0000-0000-0000-000000000002');
      expect(comment.createdAt).toEqual('2023-11-04T13:30:00.000Z');
      expect(comment.updatedAt).toEqual('2023-11-04T13:30:00.000Z');
      expect(comment.parentId).toBeUndefined();
    });
  });

  function publishComment(
    currentUser: TestConvexForDataModel<DataModel>,
    request: {
      feedbackId: string;
      content: string;
      parentId?: string;
    }
  ) {
    return currentUser.mutation(api.mutations.user.publishComment.endpoint, request);
  }
});
