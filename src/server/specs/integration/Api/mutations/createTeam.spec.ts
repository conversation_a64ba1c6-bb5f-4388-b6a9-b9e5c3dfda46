import { api } from '@/convex/_generated/api';
import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { JOHN } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { createCraftUsers } from '@/src/server/specs/integration/helpers/createCraftUsers';
import { setTodayIs } from '@/src/server/specs/integration/helpers/setTodayIs';
import { getOneFrom } from 'convex-helpers/server/relationships';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('Create Team', () => {
  let asJohn: TestConvexForDataModel<DataModel>;
  const TODAY = '2025-01-01T00:00:00.000Z';

  beforeEach(async () => {
    setTodayIs(new Date(Date.UTC(2025, 0, 1)));
    asJohn = convexTest(schema).withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });
    await createCraftUsers(asJohn, [JOHN]);
  });

  describe('When the user creates a team', () => {
    it('should create the team successfully', async () => {
      // Arrange
      const request = {
        name: 'My Awesome Team',
      };

      // Act
      const teamId = await createTeam(asJohn, request);

      // Assert
      const teamDocument = await asJohn.run((ctx) => getOneFrom(ctx.db, 'teams', 'by_teamId', teamId));

      expect(teamDocument).toBeDefined();
      expect(teamDocument?.name).toBe(request.name);
      expect(teamDocument?.ownerId).toBe(JOHN.craftUserId);
      expect(teamDocument?.createdAt).toBe(TODAY);
    });
  });
});

function createTeam(
  currentUser: TestConvexForDataModel<DataModel>,
  request: {
    name: string;
  }
) {
  return currentUser.mutation(api.mutations.user.createTeam.endpoint, request);
}
