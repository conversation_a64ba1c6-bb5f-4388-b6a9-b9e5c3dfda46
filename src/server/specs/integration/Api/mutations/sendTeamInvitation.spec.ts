import { api } from '@/convex/_generated/api';
import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { JOHN, SOPHIE } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { createCraftUsers } from '@/src/server/specs/integration/helpers/createCraftUsers';
import { setTodayIs } from '@/src/server/specs/integration/helpers/setTodayIs';
import { getOneFrom } from 'convex-helpers/server/relationships';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('Send Team Invitation', () => {
  let asJohn: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;
  const TODAY = '2025-01-01T00:00:00.000Z';
  const JOHNS_TEAM_ID = '00000000-0000-0000-0000-000000000001';
  const TEST_EMAIL = '<EMAIL>';

  beforeEach(async () => {
    setTodayIs(new Date(Date.UTC(2025, 0, 1)));
    asJohn = convexTest(schema).withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });
    asSophie = convexTest(schema).withIdentity({ subject: SOPHIE.convexUserId, name: SOPHIE.name });
    await createCraftUsers(asJohn, [JOHN, SOPHIE]);
    await setupJohnTeam(asJohn);
  });

  describe('When the team owner sends an invitation', () => {
    it('should create a new invitation for the recipient', async () => {
      // Arrange
      const request = {
        teamId: JOHNS_TEAM_ID,
        email: TEST_EMAIL,
      };

      // Act
      const invitationId = await sendTeamInvitation(asJohn, request);

      // Assert
      const invitationDocument = await asJohn.run((ctx) =>
        getOneFrom(ctx.db, 'teamInvitations', 'by_teamInvitationId', invitationId)
      );

      expect(invitationDocument).toBeDefined();
      expect(invitationDocument?.teamId).toBe(JOHNS_TEAM_ID);
      expect(invitationDocument?.email).toBe(TEST_EMAIL);
      expect(invitationDocument?.status).toBe('pending');
      expect(invitationDocument?.invitedBy).toBe(JOHN.craftUserId);
      expect(invitationDocument?.createdAt).toBeDefined();
    });
  });

  describe('When a non-owner tries to send an invitation', () => {
    it('should reject the request as only team owners can send invitations', async () => {
      // Arrange
      const request = {
        teamId: JOHNS_TEAM_ID,
        email: TEST_EMAIL,
      };

      // Act & Assert
      await expect(sendTeamInvitation(asSophie, request)).rejects.toThrow('team.errors.notOwner');
    });
  });

  describe('When trying to send an invitation to an email that already has a pending invitation', () => {
    it('should reject the duplicate invitation as one is already pending', async () => {
      // Arrange
      await setupExistingInvitation(asJohn);
      const request = {
        teamId: JOHNS_TEAM_ID,
        email: TEST_EMAIL,
      };

      // Act & Assert
      await expect(sendTeamInvitation(asJohn, request)).rejects.toThrow('teamInvitation.errors.alreadyExists');
    });
  });

  async function sendTeamInvitation(
    user: TestConvexForDataModel<DataModel>,
    request: {
      teamId: string;
      email: string;
    }
  ) {
    return await user.mutation(api.mutations.user.sendTeamInvitation.endpoint, request);
  }

  async function setupJohnTeam(ctx: TestConvexForDataModel<DataModel>) {
    await ctx.run(async (dbCtx) => {
      await dbCtx.db.insert('teams', {
        teamId: JOHNS_TEAM_ID,
        name: "John's Team",
        ownerId: JOHN.craftUserId,
        createdAt: TODAY,
      });
    });
  }

  async function setupExistingInvitation(ctx: TestConvexForDataModel<DataModel>) {
    await ctx.run(async (dbCtx) => {
      await dbCtx.db.insert('teamInvitations', {
        teamInvitationId: '00000000-0000-0000-0000-000000000010',
        teamId: JOHNS_TEAM_ID,
        email: TEST_EMAIL,
        status: 'pending',
        invitedBy: JOHN.craftUserId,
        createdAt: TODAY,
      });
    });
  }
});
