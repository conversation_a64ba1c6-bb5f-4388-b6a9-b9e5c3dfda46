import { api } from '@/convex/_generated/api';
import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { JOH<PERSON> } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { createCraftUsers } from '@/src/server/specs/integration/helpers/createCraftUsers';
import { setTodayIs } from '@/src/server/specs/integration/helpers/setTodayIs';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('GetCodeSnippetsByCodeReviewId', () => {
  let asJohn: TestConvexForDataModel<DataModel>;

  const CODE_REVIEW_ID = createFakeId('1');
  const CODE_SNIPPET_1_ID = createFakeId('101');
  const CODE_SNIPPET_2_ID = createFakeId('102');

  beforeEach(async () => {
    setTodayIs(new Date(2023, 10, 4, 13, 30, 0));
    const t = convexTest(schema);
    asJohn = t.withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });
    await createCraftUsers(asJohn, [JOHN]);
  });

  describe('When code review does not exist', () => {
    it('should return an empty array', async () => {
      // Act
      const result = await getCodeSnippetsByCodeReviewId(asJohn, 'non-existent-id');

      // Assert
      expect(result.codeSnippets).toEqual([]);
    });
  });

  describe('When code review exists but has no code snippets', () => {
    beforeEach(async () => {
      await asJohn.run(async (ctx) => {
        await ctx.db.insert('codeReviews', {
          codeReviewId: CODE_REVIEW_ID,
          title: 'Test Code Review',
          authorId: JOHN.craftUserId,
          status: 'open',
          visibility: 'public',
          participantIds: [JOHN.craftUserId],
        });
      });
    });

    it('should return an empty array', async () => {
      // Act
      const result = await getCodeSnippetsByCodeReviewId(asJohn, CODE_REVIEW_ID);

      // Assert
      expect(result.codeSnippets).toEqual([]);
    });
  });

  describe('When code review exists with code snippets', () => {
    beforeEach(async () => {
      await asJohn.run(async (ctx) => {
        await ctx.db.insert('codeReviews', {
          codeReviewId: CODE_REVIEW_ID,
          title: 'Test Code Review',
          authorId: JOHN.craftUserId,
          status: 'open',
          visibility: 'public',
          participantIds: [JOHN.craftUserId],
        });

        await ctx.db.insert('codeSnippets', {
          codeSnippetId: CODE_SNIPPET_1_ID,
          filename: 'src/example.ts',
          language: 'typescript',
          content: 'function add(a: number, b: number): number {\n  return a + b;\n}\n\nconsole.log(add(1, 2));',
          codeReviewId: CODE_REVIEW_ID,
        });

        await ctx.db.insert('codeSnippets', {
          codeSnippetId: CODE_SNIPPET_2_ID,
          filename: 'src/utils.js',
          language: 'javascript',
          content: 'function multiply(a, b) {\n  return a * b;\n}\n\nmodule.exports = { multiply };',
          codeReviewId: CODE_REVIEW_ID,
        });
      });
    });

    it('should return an array of code snippet IDs', async () => {
      // Act
      const result = await getCodeSnippetsByCodeReviewId(asJohn, CODE_REVIEW_ID);

      // Assert
      expect(result.codeSnippets).toHaveLength(2);
      expect(result.codeSnippets).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: CODE_SNIPPET_1_ID,
            filename: 'src/example.ts',
          }),
          expect.objectContaining({
            id: CODE_SNIPPET_2_ID,
            filename: 'src/utils.js',
          }),
        ])
      );
    });
  });
});

function getCodeSnippetsByCodeReviewId(
  currentUser: TestConvexForDataModel<DataModel>,
  codeReviewId: string
) {
  return currentUser.query(api.queries.bot.getCodeSnippetsByCodeReviewId.endpoint, {
    codeReviewId,
    apiKey: '99999999-9999-9999-9999-999999999999',
  });
}
