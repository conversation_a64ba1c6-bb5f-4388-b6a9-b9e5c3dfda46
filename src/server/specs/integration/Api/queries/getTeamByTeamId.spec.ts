import { api } from '@/convex/_generated/api';
import type { DataModel } from '@/convex/_generated/dataModel';
import type { MutationCtx } from '@/convex/_generated/server';
import { JOHN, SOPHIE } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { authenticateUserAs } from '@/src/server/specs/integration/helpers/authenticateUserAs';
import { createCraftUsers } from '@/src/server/specs/integration/helpers/createCraftUsers';
import type { TestConvexForDataModel } from 'convex-test';

describe('getTeamByTeamId', () => {
  let asJohn: TestConvexForDataModel<DataModel>;
  let asSophie: TestConvexForDataModel<DataModel>;

  const TODAY = '2025-01-01T00:00:00.000Z';

  const JOHNS_TEAM_ID = '00000000-0000-0000-0000-000000000101';
  const NON_EXISTENT_TEAM_ID = '00000000-0000-0000-0000-000000000999';

  const SOPHIE_MEMBER_ID = '00000000-0000-0000-0000-000000000201';

  const DELETED_USER_ID = '00000000-0000-0000-0000-000000000301';
  const DELETED_USER_MEMBER_ID = '00000000-0000-0000-0000-000000000401';

  beforeEach(async () => {
    asJohn = authenticateUserAs(JOHN);
    asSophie = authenticateUserAs(SOPHIE);
    await createCraftUsers(asJohn, [JOHN, SOPHIE]);
  });

  describe('When team does not exist', () => {
    it('should return null', async () => {
      // Act
      const result = await getTeamByTeamId(asJohn, NON_EXISTENT_TEAM_ID);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('When team exists', () => {
    beforeEach(async () => {
      await asJohn.run(async (ctx) => {
        await setupJohnTeam(ctx);
        await addSophieToJohnTeam(ctx);
        await addDeletedUserToJohnTeam(ctx);
      });
    });

    it('should contain current team details', async () => {
      // Act
      const result = await getTeamByTeamId(asJohn, JOHNS_TEAM_ID);

      // Assert
      expect(result?.teamId).toEqual(JOHNS_TEAM_ID);
      expect(result?.teamName).toEqual("John's Team");
    });

    describe('When user is the owner', () => {
      it('should have the user as owner', async () => {
        // Act
        const result = await getTeamByTeamId(asJohn, JOHNS_TEAM_ID);

        // Assert
        expect(result?.isOwner).toBeTrue();
      });
    });

    describe('When user is not the owner', () => {
      it('should not have the user as owner', async () => {
        // Act
        const result = await getTeamByTeamId(asSophie, JOHNS_TEAM_ID);

        // Assert
        expect(result?.isOwner).toBeFalse();
      });
    });

    it('should have only existing members in the list', async () => {
      // Act
      const result = await getTeamByTeamId(asSophie, JOHNS_TEAM_ID);

      // Assert
      expect(result?.members).toEqual([
        {
          userId: JOHN.craftUserId,
          name: JOHN.name,
          avatar: JOHN.avatar,
          avatarFallback: 'JO',
          isOwner: true,
          role: 'Owner',
        },
        {
          userId: SOPHIE.craftUserId,
          name: SOPHIE.name,
          avatar: SOPHIE.avatar,
          avatarFallback: 'SO',
          isOwner: false,
          role: 'Member',
        },
      ]);
    });
  });

  async function getTeamByTeamId(user: TestConvexForDataModel<DataModel>, teamId: string) {
    return await user.query(api.queries.lobby.getTeamByTeamId.endpoint, { teamId });
  }

  async function setupJohnTeam(ctx: MutationCtx) {
    await ctx.db.insert('teams', {
      teamId: JOHNS_TEAM_ID,
      name: "John's Team",
      ownerId: JOHN.craftUserId,
      createdAt: TODAY,
    });
  }

  async function addSophieToJohnTeam(ctx: MutationCtx) {
    await ctx.db.insert('teamMembers', {
      teamMemberId: SOPHIE_MEMBER_ID,
      teamId: JOHNS_TEAM_ID,
      userId: SOPHIE.craftUserId,
      addedBy: JOHN.craftUserId,
      createdAt: TODAY,
    });
  }

  async function addDeletedUserToJohnTeam(ctx: MutationCtx) {
    await ctx.db.insert('teamMembers', {
      teamMemberId: DELETED_USER_MEMBER_ID,
      teamId: JOHNS_TEAM_ID,
      userId: DELETED_USER_ID,
      addedBy: JOHN.craftUserId,
      createdAt: TODAY,
    });
  }
});
