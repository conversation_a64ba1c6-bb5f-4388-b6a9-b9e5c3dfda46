import { api } from '@/convex/_generated/api';
import type { DataModel } from '@/convex/_generated/dataModel';
import type { MutationCtx } from '@/convex/_generated/server';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { JOHN, SOPHIE } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { authenticateUserAs } from '@/src/server/specs/integration/helpers/authenticateUserAs';
import { createCraftUsers } from '@/src/server/specs/integration/helpers/createCraftUsers';
import { setTodayIs } from '@/src/server/specs/integration/helpers/setTodayIs';
import type { TestConvexForDataModel } from 'convex-test';

describe('GetUserTeams', () => {
  let asJohn: TestConvexForDataModel<DataModel>;
  const TODAY = '2025-01-01T00:00:00.000Z';

  const JOHNS_TEAM_ID = createFakeId('1');
  const SOPHIES_TEAM_ID = createFakeId('2');
  const JOHN_IN_SOPHIES_TEAM_ID = createFakeId('3');

  beforeEach(async () => {
    setTodayIs(new Date(2025, 0, 1));
    asJohn = authenticateUserAs(JOHN);
    await createCraftUsers(asJohn, [JOHN, SOPHIE]);
  });

  describe('When a user has teams', () => {
    beforeEach(async () => {
      await asJohn.run(async (ctx) => {
        await setupJohnTeam(ctx);

        await setupSophiesTeam(ctx);
        await addJohnToSophiesTeam(ctx);
      });
    });

    it('should return the teams the user owns', async () => {
      // Act
      const userTeams = await getUserTeams(asJohn);

      // Assert
      expect(userTeams.ownedTeams).toEqual([
        {
          teamId: JOHNS_TEAM_ID,
          teamName: "John's Team",
          members: [
            {
              userId: JOHN.craftUserId,
              name: JOHN.name,
              avatar: JOHN.avatar,
              avatarFallback: 'JO',
              isOwner: true,
            },
          ],
        },
      ]);
    });

    it('should return the teams the user is a member of', async () => {
      // Act
      const userTeams = await getUserTeams(asJohn);

      // Assert
      expect(userTeams.memberOfTeams).toEqual([
        {
          teamId: SOPHIES_TEAM_ID,
          teamName: "Sophie's Team",
          members: [
            {
              userId: SOPHIE.craftUserId,
              name: SOPHIE.name,
              avatar: SOPHIE.avatar,
              avatarFallback: 'SO',
              isOwner: true,
            },
            {
              userId: JOHN.craftUserId,
              name: JOHN.name,
              avatar: JOHN.avatar,
              avatarFallback: 'JO',
              isOwner: false,
            },
          ],
        },
      ]);
    });
  });

  describe('When a user has no teams', () => {
    it('should return an empty list of teams the user owns', async () => {
      // Act
      const userTeams = await getUserTeams(asJohn);

      // Assert
      expect(userTeams.ownedTeams).toEqual([]);
    });

    it('should return an empty list of teams the user is a member of', async () => {
      // Act
      const userTeams = await getUserTeams(asJohn);

      // Assert
      expect(userTeams.memberOfTeams).toEqual([]);
    });
  });

  async function getUserTeams(user: TestConvexForDataModel<DataModel>) {
    return await user.query(api.queries.lobby.getUserTeams.endpoint);
  }

  async function setupJohnTeam(ctx: MutationCtx) {
    await ctx.db.insert('teams', {
      teamId: JOHNS_TEAM_ID,
      name: "John's Team",
      ownerId: JOHN.craftUserId,
      createdAt: TODAY,
    });
  }

  async function setupSophiesTeam(ctx: MutationCtx) {
    await ctx.db.insert('teams', {
      teamId: SOPHIES_TEAM_ID,
      name: "Sophie's Team",
      ownerId: SOPHIE.craftUserId,
      createdAt: TODAY,
    });
  }

  async function addJohnToSophiesTeam(ctx: MutationCtx) {
    await ctx.db.insert('teamMembers', {
      teamMemberId: JOHN_IN_SOPHIES_TEAM_ID,
      teamId: SOPHIES_TEAM_ID,
      userId: JOHN.craftUserId,
      addedBy: SOPHIE.craftUserId,
      createdAt: TODAY,
    });
  }
});
