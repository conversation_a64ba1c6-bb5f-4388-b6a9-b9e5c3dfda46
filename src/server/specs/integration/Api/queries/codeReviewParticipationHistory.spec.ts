import { api } from '@/convex/_generated/api';
import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { JOHN, SOPHIE } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { createCraftUsers } from '@/src/server/specs/integration/helpers/createCraftUsers';
import { loadShardedCounters } from '@/src/server/specs/integration/helpers/loadShardedCounters';
import { setTodayIs } from '@/src/server/specs/integration/helpers/setTodayIs';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('CodeReviewParticipationHistory', () => {
  let asSophie: TestConvexForDataModel<DataModel>;

  beforeEach(async () => {
    setTodayIs(new Date(2023, 10, 4, 13, 30, 0));
    const t = convexTest(schema);
    await loadShardedCounters(t);
    asSophie = t.withIdentity({ subject: SOPHIE.convexUserId, name: SOPHIE.name });
    await createCraftUsers(asSophie, [SOPHIE, JOHN]);
  });

  describe('When the user has participated to some code reviews', () => {
    it('should return his code review history sorted by date', async () => {
      await asSophie.run(async (ctx) => {
        await ctx.db.insert('codeReviewParticipation', {
          codeReviewParticipationId: '00000000-0000-0000-0000-000000000001',
          codeReviewId: '00000000-0000-0000-0000-000000000001',
          userId: SOPHIE.craftUserId,
          date: '2023-11-04',
        });

        await ctx.db.insert('codeReviewParticipation', {
          codeReviewParticipationId: '00000000-0000-0000-0000-000000000002',
          codeReviewId: '00000000-0000-0000-0000-000000000002',
          userId: SOPHIE.craftUserId,
          date: '2023-11-04',
        });

        await ctx.db.insert('codeReviewParticipation', {
          codeReviewParticipationId: '00000000-0000-0000-0000-000000000003',
          codeReviewId: '00000000-0000-0000-0000-000000000003',
          userId: SOPHIE.craftUserId,
          date: '2024-08-16',
        });

        await ctx.db.insert('codeReviews', {
          codeReviewId: '00000000-0000-0000-0000-000000000001',
          title: 'Hello world',
          authorId: SOPHIE.craftUserId,
          status: 'open',
          visibility: 'public',
          participantIds: [SOPHIE.craftUserId, JOHN.craftUserId],
        });

        await ctx.db.insert('codeReviews', {
          codeReviewId: '00000000-0000-0000-0000-000000000002',
          title: 'Hello world 2',
          authorId: JOHN.craftUserId,
          status: 'open',
          visibility: 'public',
          participantIds: [SOPHIE.craftUserId, JOHN.craftUserId],
        });

        await ctx.db.insert('codeReviews', {
          codeReviewId: '00000000-0000-0000-0000-000000000003',
          title: 'Hello world 3',
          authorId: SOPHIE.craftUserId,
          status: 'open',
          visibility: 'public',
          participantIds: [SOPHIE.craftUserId, JOHN.craftUserId],
        });

        await ctx.db.insert('codeSnippets', {
          codeSnippetId: '00000000-0000-0000-0000-000000000001',
          content: "console.log('Hello world');",
          filename: 'Hello world',
          language: 'javascript',
          codeReviewId: '00000000-0000-0000-0000-000000000001',
        });

        await ctx.db.insert('codeSnippets', {
          codeSnippetId: '00000000-0000-0000-0000-000000000002',
          content: "console.log('Hello world 2');",
          filename: 'Hello world 2',
          language: 'python',
          codeReviewId: '00000000-0000-0000-0000-000000000002',
        });

        await ctx.db.insert('codeSnippets', {
          codeSnippetId: '00000000-0000-0000-0000-000000000003',
          content: "console.log('Hello world 3');",
          filename: 'Hello world 3',
          language: 'typescript',
          codeReviewId: '00000000-0000-0000-0000-000000000003',
        });
      });

      const codeReviewParticipationHistory = await asSophie.query(api.queries.codeReviewParticipationHistory.endpoint);

      expect(codeReviewParticipationHistory).toEqual([
        {
          date: '2024-08-16',
          history: [
            {
              id: '00000000-0000-0000-0000-000000000003',
              name: 'Hello world 3',
              image:
                'https://api.dicebear.com/9.x/identicon/svg?rowColor=85E89D,9ECBFF,B392F0,F97583,FFEA7F,F99B15,C9D1D9,0D1117,161B22&seed=00000000-0000-0000-0000-000000000003',
              links: {
                participation: 'code-reviews/00000000-0000-0000-0000-000000000003',
                edition: 'lobby/code-reviews/00000000-0000-0000-0000-000000000003',
              },
              author: {
                name: 'Sophie Martin',
                avatar: 'https://i.pravatar.cc/300?img=5',
              },
              participants: 2,
              status: 'open',
              visibility: 'public',
              isOwner: true,
              editable: false,
              creationDate: '2024-08-16',
              languages: ['typescript'],
              totalCodeSnippets: 1,
              totalFeedbacks: 0,
            },
          ],
        },
        {
          date: '2023-11-04',
          history: [
            {
              id: '00000000-0000-0000-0000-000000000001',
              name: 'Hello world',
              image:
                'https://api.dicebear.com/9.x/identicon/svg?rowColor=85E89D,9ECBFF,B392F0,F97583,FFEA7F,F99B15,C9D1D9,0D1117,161B22&seed=00000000-0000-0000-0000-000000000001',
              links: {
                participation: 'code-reviews/00000000-0000-0000-0000-000000000001',
                edition: 'lobby/code-reviews/00000000-0000-0000-0000-000000000001',
              },
              author: {
                name: 'Sophie Martin',
                avatar: 'https://i.pravatar.cc/300?img=5',
              },
              participants: 2,
              status: 'open',
              visibility: 'public',
              isOwner: true,
              editable: false,
              creationDate: '2023-11-04',
              languages: ['javascript'],
              totalCodeSnippets: 1,
              totalFeedbacks: 0,
            },
            {
              id: '00000000-0000-0000-0000-000000000002',
              name: 'Hello world 2',
              image:
                'https://api.dicebear.com/9.x/identicon/svg?rowColor=85E89D,9ECBFF,B392F0,F97583,FFEA7F,F99B15,C9D1D9,0D1117,161B22&seed=00000000-0000-0000-0000-000000000002',
              links: {
                participation: 'code-reviews/00000000-0000-0000-0000-000000000002',
                edition: 'lobby/code-reviews/00000000-0000-0000-0000-000000000002',
              },
              author: {
                name: 'John Doe',
                avatar: 'https://i.pravatar.cc/300?img=1',
              },
              participants: 2,
              status: 'open',
              visibility: 'public',
              isOwner: false,
              editable: false,
              creationDate: '2023-11-04',
              languages: ['python'],
              totalCodeSnippets: 1,
              totalFeedbacks: 0,
            },
          ],
        },
      ]);
    });
  });

  describe('When the user has not participated to any code reviews', () => {
    it('should return an empty history', async () => {
      const codeReviewParticipationHistory = await asSophie.query(api.queries.codeReviewParticipationHistory.endpoint);
      expect(codeReviewParticipationHistory).toEqual([]);
    });
  });

  describe('When the user has participated to deleted code reviews', () => {
    it('should not include deleted code reviews in the history', async () => {
      await asSophie.run(async (ctx) => {
        await ctx.db.insert('codeReviewParticipation', {
          codeReviewParticipationId: '00000000-0000-0000-0000-000000000001',
          codeReviewId: '00000000-0000-0000-0000-000000000001',
          userId: SOPHIE.craftUserId,
          date: '2023-11-04',
        });
      });

      const codeReviewParticipationHistory = await asSophie.query(api.queries.codeReviewParticipationHistory.endpoint);
      expect(codeReviewParticipationHistory).toEqual([]);
    });
  });

  describe('When the user has participated to code reviews with deleted code snippets', () => {
    it('should throw an error about missing snippet data', async () => {
      await asSophie.run(async (ctx) => {
        await ctx.db.insert('codeReviewParticipation', {
          codeReviewParticipationId: '00000000-0000-0000-0000-000000000001',
          codeReviewId: '00000000-0000-0000-0000-000000000001',
          userId: SOPHIE.craftUserId,
          date: '2023-11-04',
        });

        await ctx.db.insert('codeReviews', {
          codeReviewId: '00000000-0000-0000-0000-000000000001',
          title: 'Hello world',
          authorId: SOPHIE.craftUserId,
          status: 'open',
          visibility: 'public',
          participantIds: [SOPHIE.craftUserId, JOHN.craftUserId],
        });
      });

      await expect(asSophie.query(api.queries.codeReviewParticipationHistory.endpoint)).rejects.toThrow(
        'Snippet data not found: 00000000-0000-0000-0000-000000000001'
      );
    });
  });
});
