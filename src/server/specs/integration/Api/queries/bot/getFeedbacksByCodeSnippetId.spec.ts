import { api } from '@/convex/_generated/api';
import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { JOH<PERSON>, SOPHIE } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { createCraftUsers } from '@/src/server/specs/integration/helpers/createCraftUsers';
import { setTodayIs } from '@/src/server/specs/integration/helpers/setTodayIs';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('GetFeedbacksByCodeSnippetId', () => {
  let asJohn: TestConvexForDataModel<DataModel>;

  const CODE_REVIEW_ID = createFakeId('1');
  const CODE_SNIPPET_ID = createFakeId('101');
  const FEEDBACK_1_ID = createFakeId('201');
  const FEEDBACK_2_ID = createFakeId('202');
  const DELETED_FEEDBACK_ID = createFakeId('203');
  const NON_EXISTENT_CODE_SNIPPET_ID = createFakeId('999');

  beforeEach(async () => {
    setTodayIs(new Date(2023, 10, 4, 13, 30, 0));
    const t = convexTest(schema);
    asJohn = t.withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });
    await createCraftUsers(asJohn, [JOHN, SOPHIE]);
  });

  describe('When code snippet does not exist', () => {
    it('should return an empty array', async () => {
      // Act
      const result = await getFeedbacksByCodeSnippetId(asJohn, NON_EXISTENT_CODE_SNIPPET_ID);

      // Assert
      expect(result.feedbacks).toEqual([]);
    });
  });

  describe('When code snippet exists but has no feedbacks', () => {
    beforeEach(async () => {
      await asJohn.run(async (ctx) => {
        await ctx.db.insert('codeReviews', {
          codeReviewId: CODE_REVIEW_ID,
          title: 'Test Code Review',
          authorId: JOHN.craftUserId,
          status: 'open',
          visibility: 'public',
          participantIds: [JOHN.craftUserId],
        });

        await ctx.db.insert('codeSnippets', {
          codeSnippetId: CODE_SNIPPET_ID,
          filename: 'src/example.ts',
          language: 'typescript',
          content: 'function add(a: number, b: number): number {\n  return a + b;\n}\n\nconsole.log(add(1, 2));',
          codeReviewId: CODE_REVIEW_ID,
        });
      });
    });

    it('should return an empty array', async () => {
      // Act
      const result = await getFeedbacksByCodeSnippetId(asJohn, CODE_SNIPPET_ID);

      // Assert
      expect(result.feedbacks).toEqual([]);
    });
  });

  describe('When code snippet exists with feedbacks', () => {
    beforeEach(async () => {
      await asJohn.run(async (ctx) => {
        await ctx.db.insert('codeReviews', {
          codeReviewId: CODE_REVIEW_ID,
          title: 'Test Code Review',
          authorId: JOHN.craftUserId,
          status: 'open',
          visibility: 'public',
          participantIds: [JOHN.craftUserId, SOPHIE.craftUserId],
        });

        await ctx.db.insert('codeSnippets', {
          codeSnippetId: CODE_SNIPPET_ID,
          filename: 'src/example.ts',
          language: 'typescript',
          content: 'function add(a: number, b: number): number {\n  return a + b;\n}\n\nconsole.log(add(1, 2));',
          codeReviewId: CODE_REVIEW_ID,
        });

        await ctx.db.insert('feedbacks', {
          feedbackId: FEEDBACK_1_ID,
          codeSnippetId: CODE_SNIPPET_ID,
          title: 'Add type annotation',
          content: 'Consider adding a more descriptive return type annotation.',
          userId: JOHN.craftUserId,
          startLine: 1,
          endLine: 1,
          type: 'constructive',
          subType: 'highlightLines',
          deleted: false,
          createdAt: '2023-11-04T13:30:00.000Z',
          updatedAt: '2023-11-04T13:30:00.000Z',
        });

        await ctx.db.insert('feedbacks', {
          feedbackId: FEEDBACK_2_ID,
          codeSnippetId: CODE_SNIPPET_ID,
          title: 'Remove console.log',
          content: 'This console.log should be removed before production.',
          userId: SOPHIE.craftUserId,
          startLine: 5,
          endLine: 5,
          type: 'boyscout',
          subType: 'removeLines',
          deleted: false,
          createdAt: '2023-11-04T13:35:00.000Z',
          updatedAt: '2023-11-04T13:35:00.000Z',
        });

        await ctx.db.insert('feedbacks', {
          feedbackId: DELETED_FEEDBACK_ID,
          codeSnippetId: CODE_SNIPPET_ID,
          title: 'Deleted feedback',
          content: 'This feedback has been deleted.',
          userId: JOHN.craftUserId,
          startLine: 3,
          endLine: 3,
          type: 'question',
          subType: 'highlightLines',
          deleted: true,
          createdAt: '2023-11-04T13:40:00.000Z',
          updatedAt: '2023-11-04T13:40:00.000Z',
        });
      });
    });

    it('should return all non-deleted feedbacks for the code snippet', async () => {
      // Act
      const result = await getFeedbacksByCodeSnippetId(asJohn, CODE_SNIPPET_ID);

      // Assert
      expect(result.feedbacks).toHaveLength(2);
      expect(result.feedbacks).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: FEEDBACK_1_ID,
            title: 'Add type annotation',
            content: 'Consider adding a more descriptive return type annotation.',
            type: 'constructive',
            subType: 'highlightLines',
            startLine: 1,
            endLine: 1,
            author: {
              name: JOHN.name,
            },
          }),
          expect.objectContaining({
            id: FEEDBACK_2_ID,
            title: 'Remove console.log',
            content: 'This console.log should be removed before production.',
            type: 'boyscout',
            subType: 'removeLines',
            startLine: 5,
            endLine: 5,
            author: {
              name: SOPHIE.name,
            },
          }),
        ])
      );
      
      // Verify deleted feedback is not included
      const deletedFeedback = result.feedbacks.find(f => f.id === DELETED_FEEDBACK_ID);
      expect(deletedFeedback).toBeUndefined();
    });
  });
});

function getFeedbacksByCodeSnippetId(
  currentUser: TestConvexForDataModel<DataModel>,
  codeSnippetId: string
) {
  return currentUser.query(api.queries.bot.getFeedbacksByCodeSnippetId.endpoint, {
    codeSnippetId,
    apiKey: '99999999-9999-9999-9999-999999999999',
  });
}
