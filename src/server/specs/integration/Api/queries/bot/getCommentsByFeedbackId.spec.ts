import { api } from '@/convex/_generated/api';
import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { JOH<PERSON>, SOPHIE } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { createCraftUsers } from '@/src/server/specs/integration/helpers/createCraftUsers';
import { setTodayIs } from '@/src/server/specs/integration/helpers/setTodayIs';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('GetCommentsByFeedbackId', () => {
  let asJohn: TestConvexForDataModel<DataModel>;

  const CODE_REVIEW_ID = createFakeId('1');
  const CODE_SNIPPET_ID = createFakeId('101');
  const FEEDBACK_ID = createFakeId('201');
  const COMMENT_1_ID = createFakeId('301');
  const COMMENT_2_ID = createFakeId('302');
  const DELETED_COMMENT_ID = createFakeId('303');
  const REPLY_COMMENT_ID = createFakeId('304');
  const NON_EXISTENT_FEEDBACK_ID = createFakeId('999');

  beforeEach(async () => {
    setTodayIs(new Date(2023, 10, 4, 13, 30, 0));
    const t = convexTest(schema);
    asJohn = t.withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });
    await createCraftUsers(asJohn, [JOHN, SOPHIE]);
  });

  describe('When feedback does not exist', () => {
    it('should return an empty array', async () => {
      // Act
      const result = await getCommentsByFeedbackId(asJohn, NON_EXISTENT_FEEDBACK_ID);

      // Assert
      expect(result.comments).toEqual([]);
    });
  });

  describe('When feedback exists but has no comments', () => {
    beforeEach(async () => {
      await asJohn.run(async (ctx) => {
        await ctx.db.insert('codeReviews', {
          codeReviewId: CODE_REVIEW_ID,
          title: 'Test Code Review',
          authorId: JOHN.craftUserId,
          status: 'open',
          visibility: 'public',
          participantIds: [JOHN.craftUserId],
        });

        await ctx.db.insert('codeSnippets', {
          codeSnippetId: CODE_SNIPPET_ID,
          filename: 'src/example.ts',
          language: 'typescript',
          content: 'function add(a: number, b: number): number {\n  return a + b;\n}\n\nconsole.log(add(1, 2));',
          codeReviewId: CODE_REVIEW_ID,
        });

        await ctx.db.insert('feedbacks', {
          feedbackId: FEEDBACK_ID,
          codeSnippetId: CODE_SNIPPET_ID,
          title: 'Add type annotation',
          content: 'Consider adding a more descriptive return type annotation.',
          userId: JOHN.craftUserId,
          startLine: 1,
          endLine: 1,
          type: 'constructive',
          subType: 'highlightLines',
          deleted: false,
          createdAt: '2023-11-04T13:30:00.000Z',
          updatedAt: '2023-11-04T13:30:00.000Z',
        });
      });
    });

    it('should return an empty array', async () => {
      // Act
      const result = await getCommentsByFeedbackId(asJohn, FEEDBACK_ID);

      // Assert
      expect(result.comments).toEqual([]);
    });
  });

  describe('When feedback exists with comments', () => {
    beforeEach(async () => {
      await asJohn.run(async (ctx) => {
        await ctx.db.insert('codeReviews', {
          codeReviewId: CODE_REVIEW_ID,
          title: 'Test Code Review',
          authorId: JOHN.craftUserId,
          status: 'open',
          visibility: 'public',
          participantIds: [JOHN.craftUserId, SOPHIE.craftUserId],
        });

        await ctx.db.insert('codeSnippets', {
          codeSnippetId: CODE_SNIPPET_ID,
          filename: 'src/example.ts',
          language: 'typescript',
          content: 'function add(a: number, b: number): number {\n  return a + b;\n}\n\nconsole.log(add(1, 2));',
          codeReviewId: CODE_REVIEW_ID,
        });

        await ctx.db.insert('feedbacks', {
          feedbackId: FEEDBACK_ID,
          codeSnippetId: CODE_SNIPPET_ID,
          title: 'Add type annotation',
          content: 'Consider adding a more descriptive return type annotation.',
          userId: JOHN.craftUserId,
          startLine: 1,
          endLine: 1,
          type: 'constructive',
          subType: 'highlightLines',
          deleted: false,
          createdAt: '2023-11-04T13:30:00.000Z',
          updatedAt: '2023-11-04T13:30:00.000Z',
        });

        await ctx.db.insert('comments', {
          commentId: COMMENT_1_ID,
          feedbackId: FEEDBACK_ID,
          content: 'I agree with this feedback.',
          userId: JOHN.craftUserId,
          createdAt: '2023-11-04T14:00:00.000Z',
          updatedAt: '2023-11-04T14:00:00.000Z',
          status: 'published',
        });

        await ctx.db.insert('comments', {
          commentId: COMMENT_2_ID,
          feedbackId: FEEDBACK_ID,
          content: 'Thanks for the suggestion!',
          userId: SOPHIE.craftUserId,
          createdAt: '2023-11-04T14:05:00.000Z',
          updatedAt: '2023-11-04T14:05:00.000Z',
          status: 'published',
        });

        await ctx.db.insert('comments', {
          commentId: DELETED_COMMENT_ID,
          feedbackId: FEEDBACK_ID,
          content: 'This comment has been deleted.',
          userId: JOHN.craftUserId,
          createdAt: '2023-11-04T14:10:00.000Z',
          updatedAt: '2023-11-04T14:10:00.000Z',
          status: 'deleted',
        });

        await ctx.db.insert('comments', {
          commentId: REPLY_COMMENT_ID,
          feedbackId: FEEDBACK_ID,
          parentId: COMMENT_1_ID,
          content: 'This is a reply to the first comment.',
          userId: SOPHIE.craftUserId,
          createdAt: '2023-11-04T14:15:00.000Z',
          updatedAt: '2023-11-04T14:15:00.000Z',
          status: 'published',
        });
      });
    });

    it('should return all published comments for the feedback', async () => {
      // Act
      const result = await getCommentsByFeedbackId(asJohn, FEEDBACK_ID);

      // Assert
      expect(result.comments).toHaveLength(2);
      expect(result.comments).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: COMMENT_1_ID,
            content: 'I agree with this feedback.',
            author: {
              name: JOHN.name,
            },
            replies: [
              expect.objectContaining({
                id: REPLY_COMMENT_ID,
                content: 'This is a reply to the first comment.',
                author: {
                  name: SOPHIE.name,
                },
              }),
            ],
          }),
          expect.objectContaining({
            id: COMMENT_2_ID,
            content: 'Thanks for the suggestion!',
            author: {
              name: SOPHIE.name,
            },
            replies: [],
          }),
        ])
      );

      // Verify deleted comment is not included
      const deletedComment = result.comments.find(c => c.id === DELETED_COMMENT_ID);
      expect(deletedComment).toBeUndefined();
    });
  });
});

function getCommentsByFeedbackId(
  currentUser: TestConvexForDataModel<DataModel>,
  feedbackId: string
) {
  return currentUser.query(api.queries.bot.getCommentsByFeedbackId.endpoint, {
    feedbackId,
    apiKey: '99999999-9999-9999-9999-999999999999',
  });
}
