import { api } from '@/convex/_generated/api';
import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import { JOH<PERSON>, SOPHIE } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { createCraftUsers } from '@/src/server/specs/integration/helpers/createCraftUsers';
import { setTodayIs } from '@/src/server/specs/integration/helpers/setTodayIs';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('CodeReviewFeedbacksMarkdown', () => {
  let asJohn: TestConvexForDataModel<DataModel>;

  const CODE_REVIEW_ID = createFakeId('1');
  const CODE_SNIPPET_1_ID = createFakeId('101');
  const CODE_SNIPPET_2_ID = createFakeId('102');
  const FEEDBACK_1_ID = createFakeId('201');
  const FEEDBACK_2_ID = createFakeId('202');
  const FEEDBACK_3_ID = createFakeId('203');
  const COMMENT_1_ID = createFakeId('301');
  const COMMENT_2_ID = createFakeId('302');

  beforeEach(async () => {
    setTodayIs(new Date(2023, 10, 4, 13, 30, 0));
    const t = convexTest(schema);
    asJohn = t.withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });

    await createCraftUsers(asJohn, [JOHN, SOPHIE]);

    await asJohn.run(async (ctx) => {
      await ctx.db.insert('codeReviews', {
        codeReviewId: CODE_REVIEW_ID,
        title: 'Test Code Review',
        authorId: JOHN.craftUserId,
        status: 'open',
        visibility: 'public',
        participantIds: [JOHN.craftUserId, SOPHIE.craftUserId],
      });

      await ctx.db.insert('codeSnippets', {
        codeSnippetId: CODE_SNIPPET_1_ID,
        filename: 'src/example.ts',
        language: 'typescript',
        content: 'function add(a: number, b: number): number {\n  return a + b;\n}\n\nconsole.log(add(1, 2));',
        codeReviewId: CODE_REVIEW_ID,
      });

      await ctx.db.insert('codeSnippets', {
        codeSnippetId: CODE_SNIPPET_2_ID,
        filename: 'src/utils.js',
        language: 'javascript',
        content: 'function multiply(a, b) {\n  return a * b;\n}\n\nmodule.exports = { multiply };',
        codeReviewId: CODE_REVIEW_ID,
      });

      await ctx.db.insert('feedbacks', {
        feedbackId: FEEDBACK_1_ID,
        codeSnippetId: CODE_SNIPPET_1_ID,
        title: 'Add type annotation',
        content: 'Consider adding a more descriptive return type annotation.',
        userId: JOHN.craftUserId,
        startLine: 1,
        endLine: 1,
        type: 'constructive',
        subType: 'highlightLines',
        deleted: false,
        createdAt: '2023-11-04T13:30:00.000Z',
        updatedAt: '2023-11-04T13:30:00.000Z',
      });

      await ctx.db.insert('feedbacks', {
        feedbackId: FEEDBACK_2_ID,
        codeSnippetId: CODE_SNIPPET_1_ID,
        title: 'Remove console.log',
        content: 'This console.log should be removed before production.',
        userId: SOPHIE.craftUserId,
        startLine: 5,
        endLine: 5,
        type: 'boyscout',
        subType: 'removeLines',
        deleted: false,
        createdAt: '2023-11-04T13:35:00.000Z',
        updatedAt: '2023-11-04T13:35:00.000Z',
      });

      await ctx.db.insert('feedbacks', {
        feedbackId: FEEDBACK_3_ID,
        codeSnippetId: CODE_SNIPPET_2_ID,
        title: 'Add JSDoc comment',
        content: 'Consider adding a JSDoc comment to explain the function purpose.',
        userId: JOHN.craftUserId,
        startLine: 1,
        endLine: 3,
        type: 'constructive',
        subType: 'addLineBefore',
        deleted: false,
        createdAt: '2023-11-04T13:40:00.000Z',
        updatedAt: '2023-11-04T13:40:00.000Z',
      });

      await ctx.db.insert('comments', {
        commentId: COMMENT_1_ID,
        feedbackId: FEEDBACK_1_ID,
        content: "Good catch! I'll update this.",
        userId: SOPHIE.craftUserId,
        createdAt: '2023-11-04T14:00:00.000Z',
        updatedAt: '2023-11-04T14:00:00.000Z',
        status: 'published',
      });

      await ctx.db.insert('comments', {
        commentId: COMMENT_2_ID,
        feedbackId: FEEDBACK_3_ID,
        content: 'I agree, documentation is important.',
        userId: SOPHIE.craftUserId,
        createdAt: '2023-11-04T14:05:00.000Z',
        updatedAt: '2023-11-04T14:05:00.000Z',
        status: 'published',
      });
    });
  });

  it('should return a well-formatted markdown with all feedbacks organized by code snippet', async () => {
    const result = await asJohn.query(api.queries.codeReviewFeedbacksMarkdown.endpoint, {
      codeReviewId: CODE_REVIEW_ID,
    });

    expect(result).toBeDefined();
    expect(typeof result.markdown).toBe('string');

    expect(result.markdown).toContain('# Test Code Review');
    expect(result.markdown).toContain('## src/example.ts');
    expect(result.markdown).toContain('## src/utils.js');
    expect(result.markdown).toContain('### Add type annotation');
    expect(result.markdown).toContain('### Remove console.log');
    expect(result.markdown).toContain('### Add JSDoc comment');
    expect(result.markdown).toContain('Consider adding a more descriptive return type annotation.');
    expect(result.markdown).toContain('This console.log should be removed before production.');
    expect(result.markdown).toContain('Consider adding a JSDoc comment to explain the function purpose.');
    expect(result.markdown).toContain("Good catch! I'll update this.");
    expect(result.markdown).toContain('I agree, documentation is important.');
    expect(result.markdown).toContain('John Doe');
    expect(result.markdown).toContain('Sophie Martin');
    expect(result.markdown).toContain('Line 1');
    expect(result.markdown).toContain('Line 5');
    expect(result.markdown).toContain('Lines 1-3');
  });

  describe('when the code review does not exist', () => {
    it('should return an empty markdown', async () => {
      const result = await asJohn.query(api.queries.codeReviewFeedbacksMarkdown.endpoint, {
        codeReviewId: 'non-existent-id',
      });

      expect(result).toBeDefined();
      expect(result.markdown).toBe('');
    });
  });

  describe('when the code review has no feedbacks', () => {
    it('should return a markdown with no feedbacks', async () => {
      const emptyCodeReviewId = createFakeId('2');
      const emptyCodeSnippetId = createFakeId('103');

      await asJohn.run(async (ctx) => {
        await ctx.db.insert('codeReviews', {
          codeReviewId: emptyCodeReviewId,
          title: 'Empty Code Review',
          authorId: JOHN.craftUserId,
          status: 'open',
          visibility: 'public',
          participantIds: [JOHN.craftUserId],
        });

        await ctx.db.insert('codeSnippets', {
          codeSnippetId: emptyCodeSnippetId,
          filename: 'src/empty.ts',
          language: 'typescript',
          content: '// Empty file',
          codeReviewId: emptyCodeReviewId,
        });
      });

      const result = await asJohn.query(api.queries.codeReviewFeedbacksMarkdown.endpoint, {
        codeReviewId: emptyCodeReviewId,
      });

      expect(result).toBeDefined();
      expect(result.markdown).toContain('# Empty Code Review');
      expect(result.markdown).toContain('## src/empty.ts');
      expect(result.markdown).toContain('No feedbacks for this file.');
    });
  });
});
