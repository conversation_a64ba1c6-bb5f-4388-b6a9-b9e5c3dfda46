import type { DataModel } from '@/convex/_generated/dataModel';
import { createFakeId } from '@/src/server/specs/helpers/factories/fakeIds';
import {
  JOHN_PUBLISHED_COMMENT,
  JOHN_REPLY_TO_SOPHIE,
  SOPHIE_DELETED_COMMENT,
  SOPHIE_PUBLISHED_COMMENT,
} from '@/src/server/specs/integration/Api/dtos/fakeCommentDtos';
import { JOH<PERSON>, SOPHIE } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { authenticateUserAs } from '@/src/server/specs/integration/helpers/authenticateUserAs';
import { createComments } from '@/src/server/specs/integration/helpers/createComments';
import { createCraftUsers } from '@/src/server/specs/integration/helpers/createCraftUsers';
import { getCommentListByFeedbackId } from '@/src/server/specs/integration/helpers/getCommentListByFeedbackId';
import { setTodayIs } from '@/src/server/specs/integration/helpers/setTodayIs';
import type { TestConvexForDataModel } from 'convex-test';

describe('CommentList', () => {
  let asSophie: TestConvexForDataModel<DataModel>;

  beforeEach(async () => {
    setTodayIs(new Date(2023, 10, 4, 13, 30, 0));
    asSophie = authenticateUserAs(SOPHIE);
    await createCraftUsers(asSophie, [SOPHIE, JOHN]);
  });

  it('should have the current logged in user', async () => {
    // Arrange
    const feedbackId = createFakeId('1');

    // Act
    const commentListViewModel = await getCommentListByFeedbackId(asSophie, feedbackId);

    // Assert
    expect(commentListViewModel.user).toEqual({
      name: SOPHIE.name,
      avatar: SOPHIE.avatar,
      avatarFallback: 'SO',
    });
  });

  describe('When no comments are found', () => {
    it('should return an empty list', async () => {
      // Arrange
      const feedbackId = createFakeId('1');

      // Act
      const commentListViewModel = await getCommentListByFeedbackId(asSophie, feedbackId);

      // Assert
      expect(commentListViewModel.comments).toEqual([]);
    });
  });

  describe('When comments exist', () => {
    it('should return the list of comments of a given feedback', async () => {
      // Arrange
      await createComments(asSophie, [SOPHIE_PUBLISHED_COMMENT, SOPHIE_DELETED_COMMENT, JOHN_PUBLISHED_COMMENT]);
      const feedbackId = createFakeId('1');

      // Act
      const commentListViewModel = await getCommentListByFeedbackId(asSophie, feedbackId);

      // Assert
      expect(commentListViewModel.comments).toEqual([
        {
          id: '00000000-0000-0000-0000-000000000001',
          author: {
            name: 'Sophie Martin',
            avatar: 'https://i.pravatar.cc/300?img=5',
            avatarFallback: 'SO',
            isCurrentUser: true,
          },
          content: 'I found an error in this line of code. There is a missing semicolon at the end.',
          createdAt: '2023-11-02T09:34:00.000Z',
          replies: [],
        },
      ]);
    });

    it('should return the list of comments of a given feedback with replies', async () => {
      // Arrange
      await createComments(asSophie, [SOPHIE_PUBLISHED_COMMENT, JOHN_REPLY_TO_SOPHIE]);
      const feedbackId = createFakeId('1');

      // Act
      const commentListViewModel = await getCommentListByFeedbackId(asSophie, feedbackId);

      // Assert
      expect(commentListViewModel.comments[0].replies).toEqual([
        {
          id: '00000000-0000-0000-0000-000000000002',
          author: {
            name: 'John Doe',
            avatar: 'https://i.pravatar.cc/300?img=1',
            avatarFallback: 'JO',
            isCurrentUser: false,
          },
          content: "Thank you for pointing that out. I'll fix it right away.",
          createdAt: '2023-11-02T09:35:00.000Z',
        },
      ]);
    });
  });
});
