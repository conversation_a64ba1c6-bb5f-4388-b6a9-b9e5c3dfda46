export const SOPHIE_PUBLISHED_COMMENT = {
  commentId: '00000000-0000-0000-0000-000000000001',
  feedbackId: '00000000-0000-0000-0000-000000000001',
  parentId: undefined,
  content: 'I found an error in this line of code. There is a missing semicolon at the end.',
  userId: '00000000-0000-0000-0000-000000000001',
  createdAt: '2023-11-02T09:34:00.000Z',
  updatedAt: '2023-11-02T09:34:00.000Z',
  status: 'published',
};

export const SOPHIE_DELETED_COMMENT = {
  commentId: '00000000-0000-0000-0000-000000000003',
  feedbackId: '00000000-0000-0000-0000-000000000001',
  parentId: undefined,
  content: 'If you need more help, feel free to ask.',
  userId: '00000000-0000-0000-0000-000000000001',
  createdAt: '2023-11-02T09:34:00.000Z',
  updatedAt: '2023-11-02T09:34:00.000Z',
  status: 'deleted',
};

export const JOHN_REPLY_TO_SOPHIE = {
  commentId: '00000000-0000-0000-0000-000000000002',
  feedbackId: '00000000-0000-0000-0000-000000000001',
  parentId: '00000000-0000-0000-0000-000000000001',
  content: "Thank you for pointing that out. I'll fix it right away.",
  userId: '00000000-0000-0000-0000-000000000002',
  createdAt: '2023-11-02T09:35:00.000Z',
  updatedAt: '2023-11-02T09:35:00.000Z',
  status: 'published',
};

export const JOHN_PUBLISHED_COMMENT = {
  commentId: '00000000-0000-0000-0000-000000000002',
  feedbackId: '00000000-0000-0000-0000-000000000002',
  parentId: undefined,
  content: "Sorry, I don't understand what you mean.",
  userId: '00000000-0000-0000-0000-000000000001',
  createdAt: '2023-11-02T09:34:00.000Z',
  updatedAt: '2023-11-02T09:34:00.000Z',
  status: 'published',
};
