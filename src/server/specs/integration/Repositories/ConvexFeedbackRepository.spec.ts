import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { Feedback } from '@/src/server/domain/Feedback/Feedback';
import { FeedbackNotFoundError } from '@/src/server/domain/Feedback/errors/FeedbackNotFoundError';
import { ConvexFeedbackRepository } from '@/src/server/infrastructure/repositories/FeedbackRepository/ConvexFeedbackRepository';
import { JOHN } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { loadShardedCounters } from '@/src/server/specs/integration/helpers/loadShardedCounters';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('ConvexFeedbackRepository', () => {
  let asJohn: TestConvexForDataModel<DataModel>;

  beforeEach(async () => {
    vi.setSystemTime(new Date(Date.UTC(2023, 11, 4, 13, 30, 0)).toISOString());
    const t = convexTest(schema);
    await loadShardedCounters(t);
    asJohn = t.withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });
  });

  describe('findByFeedbackId', () => {
    describe('When the feedback exists', () => {
      it('should successfully retrieve the feedback from the database', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const feedbackRepository = new ConvexFeedbackRepository(ctx);
          await ctx.db.insert('feedbacks', {
            feedbackId: '00000000-0000-0000-0000-000000000001',
            codeSnippetId: '00000000-0000-0000-0000-000000000001',
            title: 'Hello world',
            content: 'Hello world',
            userId: JOHN.craftUserId,
            startLine: 1,
            endLine: 1,
            type: 'constructive',
            subType: 'highlightLines',
            deleted: false,
            createdAt: '2023-11-04T13:30:00.000Z',
            updatedAt: '2023-11-04T13:30:00.000Z',
          });

          // Act
          const savedFeedback = await feedbackRepository.findByFeedbackId('00000000-0000-0000-0000-000000000001');

          // Assert
          expect(savedFeedback.getValue()).toEqual(
            Feedback.fromRaw({
              id: '00000000-0000-0000-0000-000000000001',
              codeSnippetId: '00000000-0000-0000-0000-000000000001',
              title: 'Hello world',
              content: 'Hello world',
              userId: JOHN.craftUserId,
              startLine: 1,
              endLine: 1,
              type: 'constructive',
              subType: 'highlightLines',
              deleted: false,
            })
          );
        });
      });
    });

    describe('When the feedback does not exist', () => {
      it('should return an error', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const feedbackRepository = new ConvexFeedbackRepository(ctx);

          // Act
          const result = await feedbackRepository.findByFeedbackId('00000000-0000-0000-0000-000000000001');

          // Assert
          expect(result.getError()).toEqual(new FeedbackNotFoundError());
        });
      });
    });
  });

  describe('save', () => {
    describe('When the feedback does not exist', () => {
      describe('When feedback line range are not provided', () => {
        it('should save the feedback with startLine and endLine set to 0', async () => {
          // Arrange
          await asJohn.run(async (ctx) => {
            const feedbackRepository = new ConvexFeedbackRepository(ctx);
            const feedback = Feedback.fromRaw({
              id: '00000000-0000-0000-0000-000000000001',
              codeSnippetId: '00000000-0000-0000-0000-000000000001',
              title: 'Hello world',
              content: 'Hello world',
              userId: JOHN.craftUserId,
              startLine: null,
              endLine: null,
              type: 'boyscout',
              subType: 'addLineBefore',
              deleted: false,
            });

            // Act
            await feedbackRepository.save(feedback);

            // Assert
            const savedFeedback = await ctx.db
              .query('feedbacks')
              .filter((q) => q.eq(q.field('feedbackId'), '00000000-0000-0000-0000-000000000001'))
              .first();

            expect(savedFeedback?.startLine).toEqual(0);
            expect(savedFeedback?.endLine).toEqual(0);
          });
        });
      });

      describe('When feedback line range are provided', () => {
        it('should save the feedback', async () => {
          // Arrange
          await asJohn.run(async (ctx) => {
            const feedbackRepository = new ConvexFeedbackRepository(ctx);
            const feedback = Feedback.fromRaw({
              id: '00000000-0000-0000-0000-000000000001',
              codeSnippetId: '00000000-0000-0000-0000-000000000001',
              title: 'Hello world',
              content: 'Hello world',
              userId: JOHN.craftUserId,
              type: 'boyscout',
              subType: 'addLineBefore',
              deleted: false,
            });

            // Act
            await feedbackRepository.save(feedback);

            // Assert
            const savedFeedback = await ctx.db
              .query('feedbacks')
              .filter((q) => q.eq(q.field('feedbackId'), '00000000-0000-0000-0000-000000000001'))
              .first();

            expect(savedFeedback?.feedbackId).toEqual('00000000-0000-0000-0000-000000000001');
            expect(savedFeedback?.codeSnippetId).toEqual('00000000-0000-0000-0000-000000000001');
            expect(savedFeedback?.title).toEqual('Hello world');
            expect(savedFeedback?.content).toEqual('Hello world');
            expect(savedFeedback?.userId).toEqual(JOHN.craftUserId);
            expect(savedFeedback?.startLine).toEqual(0);
            expect(savedFeedback?.endLine).toEqual(0);
            expect(savedFeedback?.type).toEqual('boyscout');
            expect(savedFeedback?.subType).toEqual('addLineBefore');
            expect(savedFeedback?.deleted).toEqual(false);
            expect(savedFeedback?.createdAt).toEqual('2023-12-04T13:30:00.000Z');
            expect(savedFeedback?.updatedAt).toEqual('2023-12-04T13:30:00.000Z');
          });
        });
      });
    });

    describe('When the feedback already exists', () => {
      it('should update existing feedback', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const feedbackRepository = new ConvexFeedbackRepository(ctx);
          await ctx.db.insert('feedbacks', {
            feedbackId: '00000000-0000-0000-0000-000000000001',
            codeSnippetId: '00000000-0000-0000-0000-000000000001',
            title: 'Hello world',
            content: 'Hello world',
            userId: JOHN.craftUserId,
            startLine: 1,
            endLine: 1,
            type: 'constructive',
            subType: 'highlightLines',
            deleted: false,
            createdAt: '2023-11-04T13:30:00.000Z',
            updatedAt: '2023-11-04T13:30:00.000Z',
          });

          const feedback = Feedback.fromRaw({
            id: '00000000-0000-0000-0000-000000000001',
            codeSnippetId: '00000000-0000-0000-0000-000000000001',
            title: 'New hello world',
            content: 'New hello world',
            userId: JOHN.craftUserId,
            startLine: 1,
            endLine: 1,
            type: 'missing',
            subType: 'removeLines',
            deleted: true,
          });

          // Act
          await feedbackRepository.save(feedback);

          // Assert
          const savedFeedback = await ctx.db
            .query('feedbacks')
            .filter((q) => q.eq(q.field('feedbackId'), '00000000-0000-0000-0000-000000000001'))
            .first();

          expect(savedFeedback?.feedbackId).toEqual('00000000-0000-0000-0000-000000000001');
          expect(savedFeedback?.codeSnippetId).toEqual('00000000-0000-0000-0000-000000000001');
          expect(savedFeedback?.title).toEqual('New hello world');
          expect(savedFeedback?.content).toEqual('New hello world');
          expect(savedFeedback?.userId).toEqual(JOHN.craftUserId);
          expect(savedFeedback?.startLine).toEqual(1);
          expect(savedFeedback?.endLine).toEqual(1);
          expect(savedFeedback?.type).toEqual('missing');
          expect(savedFeedback?.subType).toEqual('removeLines');
          expect(savedFeedback?.deleted).toEqual(true);
          expect(savedFeedback?.createdAt).toEqual('2023-11-04T13:30:00.000Z');
          expect(savedFeedback?.updatedAt).toEqual('2023-12-04T13:30:00.000Z');
        });
      });
    });

    describe('When the feedback has no startLine and endLine', () => {
      it('should update existing feedback', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const feedbackRepository = new ConvexFeedbackRepository(ctx);
          await ctx.db.insert('feedbacks', {
            feedbackId: '00000000-0000-0000-0000-000000000001',
            codeSnippetId: '00000000-0000-0000-0000-000000000001',
            title: 'Hello world',
            content: 'Hello world',
            userId: JOHN.craftUserId,
            startLine: 1,
            endLine: 1,
            type: 'constructive',
            subType: 'highlightLines',
            deleted: false,
            createdAt: '2023-11-04T13:30:00.000Z',
            updatedAt: '2023-11-04T13:30:00.000Z',
          });

          const feedback = Feedback.fromRaw({
            id: '00000000-0000-0000-0000-000000000001',
            codeSnippetId: '00000000-0000-0000-0000-000000000001',
            title: 'New hello world',
            content: 'New hello world',
            userId: JOHN.craftUserId,
            startLine: null,
            endLine: null,
            type: 'constructive',
            subType: 'highlightLines',
            deleted: true,
          });

          // Act
          await feedbackRepository.save(feedback);

          // Assert
          const savedFeedback = await ctx.db
            .query('feedbacks')
            .filter((q) => q.eq(q.field('feedbackId'), '00000000-0000-0000-0000-000000000001'))
            .first();

          expect(savedFeedback?.startLine).toEqual(0);
          expect(savedFeedback?.endLine).toEqual(0);
        });
      });
    });
  });

  describe('delete', () => {
    describe('When the feedback does not exist', () => {
      it('should throw an error', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const feedbackRepository = new ConvexFeedbackRepository(ctx);

          // Act
          await expect(feedbackRepository.delete('00000000-0000-0000-0000-000000000001')).rejects.toThrow();
        });
      });
    });

    describe('When the feedback exists', () => {
      it('should delete the feedback', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const feedbackRepository = new ConvexFeedbackRepository(ctx);
          await ctx.db.insert('feedbacks', {
            feedbackId: '00000000-0000-0000-0000-000000000001',
            codeSnippetId: '00000000-0000-0000-0000-000000000001',
            title: 'Hello world',
            content: 'Hello world',
            userId: JOHN.craftUserId,
            startLine: 1,
            endLine: 1,
            type: 'constructive',
            subType: 'highlightLines',
            deleted: false,
            createdAt: '2023-11-04T13:30:00.000Z',
            updatedAt: '2023-11-04T13:30:00.000Z',
          });

          // Act
          await feedbackRepository.delete('00000000-0000-0000-0000-000000000001');

          // Assert
          const savedFeedback = await ctx.db
            .query('feedbacks')
            .filter((q) => q.eq(q.field('feedbackId'), '00000000-0000-0000-0000-000000000001'))
            .first();

          expect(savedFeedback?.deleted).toEqual(true);
        });
      });
    });
  });

  describe('getAll', () => {
    it('should throw an error', async () => {
      // Arrange
      await asJohn.run(async (ctx) => {
        const feedbackRepository = new ConvexFeedbackRepository(ctx);

        // Act
        await expect(feedbackRepository.getAll()).rejects.toThrow();
      });
    });
  });
});
