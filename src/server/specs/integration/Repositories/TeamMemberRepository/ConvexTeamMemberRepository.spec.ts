import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { TeamMember } from '@/src/server/domain/TeamMember/TeamMember';
import { TeamMemberNotFoundError } from '@/src/server/domain/TeamMember/errors/TeamMemberNotFoundError';
import { TeamMemberId } from '@/src/server/domain/TeamMember/valueObjects/TeamMemberId';
import { ConvexTeamMemberRepository } from '@/src/server/infrastructure/repositories/TeamMemberRepository/ConvexTeamMemberRepository';
import { JOHN, SOPHIE } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('ConvexTeamMemberRepository', () => {
  let asJohn: TestConvexForDataModel<DataModel>;
  const TEAM_MEMBER_ID = TeamMemberId.createFrom('00000000-0000-0000-0000-000000000001');
  const TEAM_ID = '00000000-0000-0000-0000-000000000002';
  const TODAY = new Date(Date.UTC(2025, 0, 1, 0, 0, 0)).toISOString();

  beforeEach(async () => {
    vi.setSystemTime(TODAY);
    asJohn = convexTest(schema).withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });
  });

  describe('save', () => {
    it('should save a team member to the database', async () => {
      // Arrange
      await asJohn.run(async (ctx) => {
        const teamMemberRepository = new ConvexTeamMemberRepository(ctx.db);
        const teamMember = TeamMember.create({
          id: TEAM_MEMBER_ID,
          teamId: TEAM_ID,
          userId: SOPHIE.craftUserId,
          addedBy: JOHN.craftUserId,
          createdAt: TODAY,
        }).getValue();

        // Act
        await teamMemberRepository.save(teamMember);

        // Assert
        const savedTeamMember = await ctx.db
          .query('teamMembers')
          .filter((q) => q.eq(q.field('teamMemberId'), TEAM_MEMBER_ID.get('value')))
          .first();

        expect(savedTeamMember?.teamMemberId).toEqual(TEAM_MEMBER_ID.get('value'));
        expect(savedTeamMember?.teamId).toEqual(TEAM_ID);
        expect(savedTeamMember?.userId).toEqual(SOPHIE.craftUserId);
        expect(savedTeamMember?.addedBy).toEqual(JOHN.craftUserId);
        expect(savedTeamMember?.createdAt).toEqual(TODAY);
      });
    });
  });

  describe('findById', () => {
    describe('When the team member exists', () => {
      it('should successfully retrieve the team member from the database', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const teamMemberRepository = new ConvexTeamMemberRepository(ctx.db);
          await ctx.db.insert('teamMembers', {
            teamMemberId: TEAM_MEMBER_ID.get('value'),
            teamId: TEAM_ID,
            userId: SOPHIE.craftUserId,
            addedBy: JOHN.craftUserId,
            createdAt: TODAY,
          });

          // Act
          const savedTeamMember = await teamMemberRepository.findById(TEAM_MEMBER_ID.get('value'));

          // Assert
          expect(savedTeamMember.isOk()).toBeTrue();
          expect(savedTeamMember.getValue()).toEqual(
            TeamMember.create({
              id: TeamMemberId.createFrom(TEAM_MEMBER_ID.get('value')),
              teamId: TEAM_ID,
              userId: SOPHIE.craftUserId,
              addedBy: JOHN.craftUserId,
              createdAt: TODAY,
            }).getValue()
          );
        });
      });
    });

    describe('When the team member does not exist', () => {
      it('should return an error', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const teamMemberRepository = new ConvexTeamMemberRepository(ctx.db);

          // Act
          const result = await teamMemberRepository.findById(TEAM_MEMBER_ID.get('value'));

          // Assert
          expect(result.isFail()).toBeTrue();
          expect(result.getError()).toEqual(new TeamMemberNotFoundError());
        });
      });
    });
  });

  describe('findByTeamIdAndUserId', () => {
    describe('When the team member exists', () => {
      it('should successfully retrieve the team member from the database', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const teamMemberRepository = new ConvexTeamMemberRepository(ctx.db);
          await ctx.db.insert('teamMembers', {
            teamMemberId: TEAM_MEMBER_ID.get('value'),
            teamId: TEAM_ID,
            userId: SOPHIE.craftUserId,
            addedBy: JOHN.craftUserId,
            createdAt: TODAY,
          });

          // Act
          const savedTeamMember = await teamMemberRepository.findByTeamIdAndUserId(TEAM_ID, SOPHIE.craftUserId);

          // Assert
          expect(savedTeamMember.isOk()).toBeTrue();
          expect(savedTeamMember.getValue()).toEqual(
            TeamMember.create({
              id: TeamMemberId.createFrom(TEAM_MEMBER_ID.get('value')),
              teamId: TEAM_ID,
              userId: SOPHIE.craftUserId,
              addedBy: JOHN.craftUserId,
              createdAt: TODAY,
            }).getValue()
          );
        });
      });
    });

    describe('When the team member does not exist', () => {
      it('should return an error', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const teamMemberRepository = new ConvexTeamMemberRepository(ctx.db);

          // Act
          const result = await teamMemberRepository.findByTeamIdAndUserId(TEAM_ID, SOPHIE.craftUserId);

          // Assert
          expect(result.isFail()).toBeTrue();
          expect(result.getError()).toEqual(new TeamMemberNotFoundError());
        });
      });
    });
  });
});
