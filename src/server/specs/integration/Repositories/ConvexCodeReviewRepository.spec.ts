import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { CodeReview } from '@/src/server/domain/CodeReview/CodeReview';
import { CodeReviewNotFoundError } from '@/src/server/domain/CodeReview/errors/CodeReviewNotFoundError';
import { ConvexCodeReviewRepository } from '@/src/server/infrastructure/repositories/CodeReviewRepository/ConvexCodeReviewRepository';
import { JOHN, SOPHIE } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('ConvexCodeReviewRepository', () => {
  let asJohn: TestConvexForDataModel<DataModel>;

  beforeEach(async () => {
    vi.setSystemTime(new Date(Date.UTC(2023, 11, 4, 13, 30, 0)).toISOString());
    asJohn = convexTest(schema).withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });
  });

  describe('findById', () => {
    describe('When the code review exists', () => {
      it('should successfully retrieve the code review', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const codeReviewRepository = new ConvexCodeReviewRepository(ctx.db);
          await ctx.db.insert('codeReviews', {
            codeReviewId: '00000000-0000-0000-0000-000000000001',
            title: 'Hello world',
            authorId: JOHN.craftUserId,
            status: 'new',
            visibility: 'public',
            participantIds: ['00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002'],
          });

          // Act
          const savedCodeReview = await codeReviewRepository.findById('00000000-0000-0000-0000-000000000001');

          // Assert
          expect(savedCodeReview.getValue()).toEqual(
            CodeReview.fromRaw({
              id: '00000000-0000-0000-0000-000000000001',
              title: 'Hello world',
              authorId: JOHN.craftUserId,
              status: 'new',
              visibility: 'public',
              participantIds: ['00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002'],
            })
          );
        });
      });
    });

    describe('When the code review does not exist', () => {
      it('should return an error', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const codeReviewRepository = new ConvexCodeReviewRepository(ctx.db);

          // Act
          const result = await codeReviewRepository.findById('00000000-0000-0000-0000-000000000001');

          // Assert
          expect(result.getError()).toEqual(new CodeReviewNotFoundError());
        });
      });
    });
  });

  describe('save', () => {
    describe('When the code review does not exist', () => {
      it('should save the code review', async () => {
        // Arrange
        const CODE_REVIEW_ID = '00000000-0000-0000-0000-000000000001';
        await asJohn.run(async (ctx) => {
          const codeReviewRepository = new ConvexCodeReviewRepository(ctx.db);
          const codeReview = CodeReview.fromRaw({
            id: CODE_REVIEW_ID,
            title: 'Hello world',
            authorId: JOHN.craftUserId,
            status: 'new',
            visibility: 'public',
            participantIds: [JOHN.craftUserId, SOPHIE.craftUserId],
          });

          // Act
          await codeReviewRepository.save(codeReview);

          // Assert
          const savedCodeReview = await ctx.db
            .query('codeReviews')
            .filter((q) => q.eq(q.field('codeReviewId'), CODE_REVIEW_ID))
            .first();

          expect(savedCodeReview?.codeReviewId).toEqual(CODE_REVIEW_ID);
          expect(savedCodeReview?.title).toEqual('Hello world');
          expect(savedCodeReview?.authorId).toEqual(JOHN.craftUserId);
          expect(savedCodeReview?.status).toEqual('new');
          expect(savedCodeReview?.visibility).toEqual('public');
          expect(savedCodeReview?.participantIds).toEqual([JOHN.craftUserId, SOPHIE.craftUserId]);
        });
      });
    });

    describe('When the code review already exists', () => {
      it('should update existing code review', async () => {
        // Arrange
        const CODE_REVIEW_ID = '00000000-0000-0000-0000-000000000001';
        await asJohn.run(async (ctx) => {
          const codeReviewRepository = new ConvexCodeReviewRepository(ctx.db);
          await ctx.db.insert('codeReviews', {
            codeReviewId: CODE_REVIEW_ID,
            title: 'Hello world',
            authorId: JOHN.craftUserId,
            status: 'new',
            visibility: 'public',
            participantIds: [JOHN.craftUserId, SOPHIE.craftUserId],
          });

          const codeReview = CodeReview.fromRaw({
            id: CODE_REVIEW_ID,
            title: 'New hello world',
            authorId: JOHN.craftUserId,
            status: 'close',
            visibility: 'private',
            participantIds: [JOHN.craftUserId],
          });

          // Act
          await codeReviewRepository.save(codeReview);

          // Assert
          const savedCodeReview = await ctx.db
            .query('codeReviews')
            .filter((q) => q.eq(q.field('codeReviewId'), CODE_REVIEW_ID))
            .first();

          expect(savedCodeReview?.codeReviewId).toEqual(CODE_REVIEW_ID);
          expect(savedCodeReview?.title).toEqual('New hello world');
          expect(savedCodeReview?.authorId).toEqual(JOHN.craftUserId);
          expect(savedCodeReview?.status).toEqual('close');
          expect(savedCodeReview?.visibility).toEqual('private');
          expect(savedCodeReview?.participantIds).toEqual([JOHN.craftUserId]);
        });
      });
    });
  });

  describe('getAll', () => {
    it('should throw an error', async () => {
      // Arrange
      await asJohn.run(async (ctx) => {
        const codeReviewRepository = new ConvexCodeReviewRepository(ctx.db);

        // Act
        await expect(codeReviewRepository.getAll()).rejects.toThrow();
      });
    });
  });
});
