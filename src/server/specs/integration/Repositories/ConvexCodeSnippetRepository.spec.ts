import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { CodeSnippet } from '@/src/server/domain/CodeSnippet/CodeSnippet';
import { ConvexCodeSnippetRepository } from '@/src/server/infrastructure/repositories/CodeSnippetRepository/ConvexCodeSnippetRepository';
import { JOHN } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('ConvexCodeSnippetRepository', () => {
  let asJohn: TestConvexForDataModel<DataModel>;

  beforeEach(async () => {
    vi.setSystemTime(new Date(Date.UTC(2023, 11, 4, 13, 30, 0)).toISOString());
    asJohn = convexTest(schema).withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });
  });

  describe('save', () => {
    describe('When the code snippet does not exist', () => {
      it('should create the code snippet', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const codeSnippetRepository = new ConvexCodeSnippetRepository(ctx.db);
          const codeSnippet = CodeSnippet.submitForReview(
            '00000000-0000-0000-0000-000000000001',
            'Hello world',
            'console.log("Hello world");',
            'javascript',
            '00000000-0000-0000-0000-000000000001'
          ).getValue();

          // Act
          await codeSnippetRepository.save(codeSnippet);

          // Assert
          const savedCodeSnippet = await ctx.db
            .query('codeSnippets')
            .filter((q) => q.eq(q.field('codeSnippetId'), '00000000-0000-0000-0000-000000000001'))
            .first();

          expect(savedCodeSnippet?.codeSnippetId).toEqual('00000000-0000-0000-0000-000000000001');
          expect(savedCodeSnippet?.content).toEqual('console.log("Hello world");');
          expect(savedCodeSnippet?.filename).toEqual('Hello world');
          expect(savedCodeSnippet?.language).toEqual('javascript');
          expect(savedCodeSnippet?.codeReviewId).toEqual('00000000-0000-0000-0000-000000000001');
        });
      });
    });

    describe('When the code snippet already exists', () => {
      it('should update the code snippet', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const codeSnippetRepository = new ConvexCodeSnippetRepository(ctx.db);
          await ctx.db.insert('codeSnippets', {
            codeSnippetId: '00000000-0000-0000-0000-000000000001',
            content: 'console.log("Hello world");',
            filename: 'Hello world',
            language: 'javascript',
            codeReviewId: '00000000-0000-0000-0000-000000000001',
          });

          const codeSnippet = CodeSnippet.fromRaw({
            id: '00000000-0000-0000-0000-000000000001',
            content: 'console.log("New hello world");',
            filename: 'New hello world',
            language: 'typescript',
            codeReviewId: '00000000-0000-0000-0000-000000000001',
          });

          // Act
          await codeSnippetRepository.save(codeSnippet);

          // Assert
          const savedCodeSnippet = await ctx.db
            .query('codeSnippets')
            .filter((q) => q.eq(q.field('codeSnippetId'), '00000000-0000-0000-0000-000000000001'))
            .first();

          expect(savedCodeSnippet?.codeSnippetId).toEqual('00000000-0000-0000-0000-000000000001');
          expect(savedCodeSnippet?.content).toEqual('console.log("New hello world");');
          expect(savedCodeSnippet?.filename).toEqual('New hello world');
          expect(savedCodeSnippet?.language).toEqual('typescript');
          expect(savedCodeSnippet?.codeReviewId).toEqual('00000000-0000-0000-0000-000000000001');
        });
      });
    });
  });

  describe('findByCodeSnippetId', () => {
    describe('When the code snippet exists', () => {
      it('should successfully retrieve the code snippet', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const codeSnippetRepository = new ConvexCodeSnippetRepository(ctx.db);
          await ctx.db.insert('codeSnippets', {
            codeSnippetId: '00000000-0000-0000-0000-000000000001',
            content: 'console.log("Hello world");',
            filename: 'Hello world',
            language: 'javascript',
            codeReviewId: '00000000-0000-0000-0000-000000000001',
          });

          // Act
          const savedCodeSnippet = await codeSnippetRepository.findByCodeSnippetId(
            '00000000-0000-0000-0000-000000000001'
          );

          // Assert
          expect(savedCodeSnippet).toEqual(
            CodeSnippet.fromRaw({
              id: '00000000-0000-0000-0000-000000000001',
              content: 'console.log("Hello world");',
              language: 'javascript',
              filename: 'Hello world',
              codeReviewId: '00000000-0000-0000-0000-000000000001',
            })
          );
        });
      });
    });

    describe('When the code snippet does not exist', () => {
      it('should return null', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const codeSnippetRepository = new ConvexCodeSnippetRepository(ctx.db);

          // Act
          const savedCodeSnippet = await codeSnippetRepository.findByCodeSnippetId(
            '00000000-0000-0000-0000-000000000001'
          );

          // Assert
          expect(savedCodeSnippet).toBeNull();
        });
      });
    });
  });

  describe('getAll', () => {
    it('should throw an error', async () => {
      // Arrange
      await asJohn.run(async (ctx) => {
        const codeSnippetRepository = new ConvexCodeSnippetRepository(ctx.db);

        // Act
        await expect(codeSnippetRepository.getAll()).rejects.toThrow();
      });
    });
  });

  describe('saveAll', () => {
    it('should save all code snippets', async () => {
      // Arrange
      await asJohn.run(async (ctx) => {
        const codeSnippetRepository = new ConvexCodeSnippetRepository(ctx.db);
        const codeSnippets = [
          CodeSnippet.submitForReview(
            '00000000-0000-0000-0000-000000000001',
            'Hello world',
            'console.log("Hello world");',
            'javascript',
            '00000000-0000-0000-0000-000000000001'
          ).getValue(),
          CodeSnippet.submitForReview(
            '00000000-0000-0000-0000-000000000002',
            'Hello world',
            'console.log("Hello world");',
            'javascript',
            '00000000-0000-0000-0000-000000000001'
          ).getValue(),
        ];

        // Act
        await codeSnippetRepository.saveAll(codeSnippets);

        // Assert
        const savedCodeSnippets = await ctx.db
          .query('codeSnippets')
          .filter((q) => q.eq(q.field('codeReviewId'), '00000000-0000-0000-0000-000000000001'))
          .collect();

        expect(savedCodeSnippets.length).toEqual(2);
      });
    });
  });
});
