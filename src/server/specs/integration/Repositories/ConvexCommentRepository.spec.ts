import type { DataModel } from '@/convex/_generated/dataModel';
import schema from '@/convex/schema';
import { Comment } from '@/src/server/domain/Comment/Comment';
import { CommentNotFoundError } from '@/src/server/domain/Comment/errors/CommentNotFoundError';
import { CommentId } from '@/src/server/domain/Comment/valueObjects/CommentId';
import { ConvexCommentRepository } from '@/src/server/infrastructure/repositories/CommentRepository/ConvexCommentRepository';
import { JOHN } from '@/src/server/specs/integration/Api/dtos/fakeUserDtos';
import { type TestConvexForDataModel, convexTest } from 'convex-test';

describe('ConvexCommentRepository', () => {
  let asJohn: TestConvexForDataModel<DataModel>;

  beforeEach(async () => {
    vi.setSystemTime(new Date(Date.UTC(2023, 11, 4, 13, 30, 0)).toISOString());
    asJohn = convexTest(schema).withIdentity({ subject: JOHN.convexUserId, name: JOHN.name });
  });

  describe('save', () => {
    it('should save a comment to the database', async () => {
      // Arrange
      await asJohn.run(async (ctx) => {
        const commentRepository = new ConvexCommentRepository(ctx.db);
        const comment = Comment.create({
          id: CommentId.create('00000000-0000-0000-0000-000000000001'),
          feedbackId: '00000000-0000-0000-0000-000000000002',
          content: 'Hello world',
          userId: JOHN.craftUserId,
          createdAt: '2023-11-04T13:30:00.000Z',
          updatedAt: '2023-11-04T13:30:00.000Z',
          status: 'published',
          parentId: '00000000-0000-0000-0000-000000000004',
        }).getValue();

        // Act
        await commentRepository.save(comment);

        // Assert
        const savedComment = await ctx.db
          .query('comments')
          .filter((q) => q.eq(q.field('commentId'), '00000000-0000-0000-0000-000000000001'))
          .first();

        expect(savedComment?.commentId).toEqual('00000000-0000-0000-0000-000000000001');
        expect(savedComment?.feedbackId).toEqual('00000000-0000-0000-0000-000000000002');
        expect(savedComment?.content).toEqual('Hello world');
        expect(savedComment?.status).toEqual('published');
        expect(savedComment?.userId).toEqual(JOHN.craftUserId);
        expect(savedComment?.createdAt).toEqual('2023-11-04T13:30:00.000Z');
        expect(savedComment?.updatedAt).toEqual('2023-11-04T13:30:00.000Z');
        expect(savedComment?.parentId).toEqual('00000000-0000-0000-0000-000000000004');
      });
    });
  });

  describe('findById', () => {
    describe('When the comment exists', () => {
      it('should successfully retrieve the comment from the database', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const commentRepository = new ConvexCommentRepository(ctx.db);
          await ctx.db.insert('comments', {
            commentId: '00000000-0000-0000-0000-000000000001',
            feedbackId: '00000000-0000-0000-0000-000000000002',
            content: 'Hello world',
            userId: JOHN.craftUserId,
            createdAt: '2023-11-04T13:30:00.000Z',
            updatedAt: '2023-11-04T13:30:00.000Z',
            status: 'published',
            parentId: '00000000-0000-0000-0000-000000000004',
          });

          // Act
          const savedComment = await commentRepository.findById('00000000-0000-0000-0000-000000000001');

          // Assert
          expect(savedComment.getValue()).toEqual(
            Comment.fromRaw({
              id: CommentId.create('00000000-0000-0000-0000-000000000001'),
              feedbackId: '00000000-0000-0000-0000-000000000002',
              content: 'Hello world',
              userId: JOHN.craftUserId,
              createdAt: '2023-11-04T13:30:00.000Z',
              updatedAt: '2023-11-04T13:30:00.000Z',
              status: 'published',
              parentId: '00000000-0000-0000-0000-000000000004',
            })
          );
        });
      });
    });

    describe('When the comment does not exist', () => {
      it('should return an error', async () => {
        // Arrange
        await asJohn.run(async (ctx) => {
          const commentRepository = new ConvexCommentRepository(ctx.db);

          // Act
          const result = await commentRepository.findById('00000000-0000-0000-0000-000000000001');

          // Assert
          expect(result.isFail()).toBeTrue();
          expect(result.getError()).toEqual(new CommentNotFoundError());
        });
      });
    });
  });
});
