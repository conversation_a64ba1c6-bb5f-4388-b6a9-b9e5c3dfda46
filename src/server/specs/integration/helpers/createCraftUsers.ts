import type { DataModel } from '@/convex/_generated/dataModel';
import type { UserDTO } from '@/src/server/specs/integration/Api/dtos/userDTO';
import type { TestConvexForDataModel } from 'convex-test';

export async function createCraftUsers(currentUser: TestConvexForDataModel<DataModel>, users: UserDTO[]) {
  await currentUser.run(async (ctx) => {
    for (const user of users) {
      await ctx.db.insert('craftUsers', user);
    }
  });
}
