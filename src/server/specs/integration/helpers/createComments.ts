import type { DataModel } from '@/convex/_generated/dataModel';
import type { CommentDTO } from '@/src/server/specs/integration/Api/dtos/commentDTO';
import type { TestConvexForDataModel } from 'convex-test';

export async function createComments(currentUser: TestConvexForDataModel<DataModel>, comments: CommentDTO[]) {
  await currentUser.run(async (ctx) => {
    for (const comment of comments) {
      await ctx.db.insert('comments', comment);
    }
  });
}
