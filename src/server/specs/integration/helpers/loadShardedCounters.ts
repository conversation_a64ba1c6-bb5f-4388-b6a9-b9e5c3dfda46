/// <reference types="vite/client" />

import type schema from '@/convex/schema';
import type { TestConvex } from 'convex-test';

export async function loadShardedCounters(t: TestConvex<typeof schema>) {
  const componentSchema = await import('../../../../../node_modules/@convex-dev/sharded-counter/src/component/schema');
  const componentModules = import.meta.glob(
    '../../../../../node_modules/@convex-dev/sharded-counter/src/component/**/*.ts'
  );
  t.registerComponent('shardedCounter', componentSchema.default, componentModules);
}
