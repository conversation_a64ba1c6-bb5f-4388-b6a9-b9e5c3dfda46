import { NextIntlTranslationService } from '@/src/server/infrastructure/services/TranslationService/NextIntlTranslationService';

vi.mock('next-intl/server', () => ({
  getTranslations: (namespace: string) => (key: string) => `${namespace}.${key}: Cleaner Code`,
}));

describe('NextIntlTranslationService', () => {
  it('should return the response with translated values', async () => {
    // Arrange
    const translationService = new NextIntlTranslationService();
    const translate = await translationService.getTranslations('common');

    // Act
    const response = translate('applicationName');

    // Assert
    expect(response).toEqual('common.applicationName: Cleaner Code');
  });
});
