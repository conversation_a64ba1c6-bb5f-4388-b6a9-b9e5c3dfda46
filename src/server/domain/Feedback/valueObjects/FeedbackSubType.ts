import { InvalidFeedbackSubTypeError } from '@/src/server/domain/Feedback/errors/InvalidFeedbackSubTypeError';
import { Result, ValueObject } from '@evyweb/simple-ddd-toolkit';

export type FeedbackSubTypeValue =
  | 'addLineBefore'
  | 'addLineAfter'
  | 'addLineBeforeAndAfter'
  | 'removeLines'
  | 'extractLines'
  | 'highlightLines';
const allowedFeedbackValues: FeedbackSubTypeValue[] = [
  'addLineBefore',
  'addLineAfter',
  'addLineBeforeAndAfter',
  'removeLines',
  'extractLines',
  'highlightLines',
];

type FeedbackSubTypeProperties = {
  value: FeedbackSubTypeValue;
};

export class FeedbackSubType extends ValueObject<FeedbackSubTypeProperties> {
  static createFrom(subType: string): Result<FeedbackSubType, InvalidFeedbackSubTypeError> {
    if (!FeedbackSubType.hasValid(subType)) {
      return Result.fail(new InvalidFeedbackSubTypeError());
    }
    return Result.ok(new FeedbackSubType({ value: subType as FeedbackSubTypeValue }));
  }

  private static hasValid(type: string): type is FeedbackSubTypeValue {
    return allowedFeedbackValues.includes(type as FeedbackSubTypeValue);
  }
}
