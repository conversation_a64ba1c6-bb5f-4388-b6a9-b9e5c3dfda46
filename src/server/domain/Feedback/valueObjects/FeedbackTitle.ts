import { InvalidFeedbackTitleError } from '@/src/server/domain/Feedback/errors/InvalidFeedbackTitleError';
import { Result, ValueObject } from '@evyweb/simple-ddd-toolkit';

type FeedbackTitleProperties = {
  value: string;
};

export class FeedbackTitle extends ValueObject<FeedbackTitleProperties> {
  static createFrom(content: string): Result<FeedbackTitle, InvalidFeedbackTitleError> {
    if (FeedbackTitle.hasInvalid(content)) {
      return Result.fail(new InvalidFeedbackTitleError());
    }

    return Result.ok(new FeedbackTitle({ value: content }));
  }

  private static hasInvalid(content: string): boolean {
    if (!content) {
      return true;
    }
    return content.trim().length === 0 || content.length > 150;
  }
}
