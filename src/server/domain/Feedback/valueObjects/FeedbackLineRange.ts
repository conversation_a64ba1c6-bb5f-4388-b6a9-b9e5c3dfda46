import { InvalidFeedbackLineRangeError } from '@/src/server/domain/Feedback/errors/InvalidFeedbackLineRangeError';
import { Result, ValueObject } from '@evyweb/simple-ddd-toolkit';

type FeedbackLineRangeProperties = {
  startLine: number | null;
  endLine: number | null;
};

export class FeedbackLineRange extends ValueObject<FeedbackLineRangeProperties> {
  static createFrom(
    startLine: number | null = null,
    endLine: number | null = null
  ): Result<FeedbackLineRange, InvalidFeedbackLineRangeError> {
    if (FeedbackLineRange.hasInvalid(startLine, endLine)) {
      return Result.fail(new InvalidFeedbackLineRangeError());
    }

    return Result.ok(new FeedbackLineRange({ startLine, endLine }));
  }

  static hasInvalid(startLine: number | null, endLine: number | null): boolean {
    if (FeedbackLineRange.isFileLevelFeedback(startLine, endLine)) {
      return false;
    }

    if (FeedbackLineRange.hasStartLineNullWithEndLineNotNull(startLine, endLine)) {
      return true;
    }

    const effectiveEndLine = FeedbackLineRange.computeEffectiveEndLine(startLine, endLine);

    if (FeedbackLineRange.hasInvalidLineNumbers(startLine, effectiveEndLine)) {
      return true;
    }

    return FeedbackLineRange.hasStartLineGreaterThanEndLine(startLine, effectiveEndLine);
  }

  private static isFileLevelFeedback(startLine: number | null, endLine: number | null): boolean {
    return startLine === null && endLine === null;
  }

  private static hasStartLineNullWithEndLineNotNull(startLine: number | null, endLine: number | null): boolean {
    return startLine === null && endLine !== null;
  }

  private static computeEffectiveEndLine(startLine: number | null, endLine: number | null): number | null {
    return endLine === null ? startLine : endLine;
  }

  private static hasInvalidLineNumbers(startLine: number | null, effectiveEndLine: number | null): boolean {
    if (startLine === null || effectiveEndLine === null) {
      return false;
    }
    return startLine < 0 || effectiveEndLine < 0;
  }

  private static hasStartLineGreaterThanEndLine(startLine: number | null, effectiveEndLine: number | null): boolean {
    if (startLine === null || effectiveEndLine === null) {
      return false;
    }
    return startLine > effectiveEndLine;
  }
}
