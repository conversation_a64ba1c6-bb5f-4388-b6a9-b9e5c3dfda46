import type { InvalidCodeSnippetError } from '@/src/server/domain/CodeSnippet/errors/InvalidCodeSnippetError';
import { CodeReviewId } from '@/src/server/domain/CodeSnippet/valueObjects/CodeReviewId';
import { CodeSnippetContent } from '@/src/server/domain/CodeSnippet/valueObjects/CodeSnippetContent';
import { CodeSnippetFilename } from '@/src/server/domain/CodeSnippet/valueObjects/CodeSnippetFilename';
import { CodeSnippetLanguage } from '@/src/server/domain/CodeSnippet/valueObjects/CodeSnippetLanguage';
import { Aggregate, Result } from '@evyweb/simple-ddd-toolkit';
import { CodeSnippetId } from './valueObjects/CodeSnippetId';

interface CodeSnippetProperties {
  id: CodeSnippetId;
  filename: CodeSnippetFilename;
  content: CodeSnippetContent;
  language: CodeSnippetLanguage;
  codeReviewId: CodeReviewId;
}

export class CodeSnippet extends Aggregate<CodeSnippetProperties> {
  static submitForReview(
    id: string,
    filename: string,
    content: string,
    language: string,
    codeReviewId: string
  ): Result<CodeSnippet, InvalidCodeSnippetError> {
    const codeSnippetId = CodeSnippetId.createFrom(id);
    const filenameCreation = CodeSnippetFilename.createFrom(filename);
    const contentCreation = CodeSnippetContent.createFrom(content);
    const languageCreation = CodeSnippetLanguage.createFrom(language);
    const codeReviewIdCreation = CodeReviewId.createFrom(codeReviewId);

    if (filenameCreation.isFail()) {
      return Result.fail(filenameCreation.getError());
    }

    if (contentCreation.isFail()) {
      return Result.fail(contentCreation.getError());
    }

    if (languageCreation.isFail()) {
      return Result.fail(languageCreation.getError());
    }

    return Result.ok(
      new CodeSnippet({
        id: codeSnippetId,
        filename: filenameCreation.getValue(),
        content: contentCreation.getValue(),
        language: languageCreation.getValue(),
        codeReviewId: codeReviewIdCreation,
      })
    );
  }

  static fromRaw(rawData: { id: string; content: string; filename: string; language: string; codeReviewId: string }) {
    return new CodeSnippet({
      id: CodeSnippetId.createFrom(rawData.id),
      content: CodeSnippetContent.createFrom(rawData.content).getValue(),
      filename: CodeSnippetFilename.createFrom(rawData.filename).getValue(),
      language: CodeSnippetLanguage.createFrom(rawData.language).getValue(),
      codeReviewId: CodeReviewId.createFrom(rawData.codeReviewId),
    });
  }
}
