import { InvalidCodeSnippetLanguageError } from '@/src/server/domain/CodeSnippet/errors/InvalidCodeSnippetLanguageError';
import { Result, ValueObject } from '@evyweb/simple-ddd-toolkit';
import { z } from 'zod';

const codeSnippetLanguageSchema = z.string().trim().min(1).max(1000000);

type CodeSnippetLanguageProperties = {
  value: string;
};

export class CodeSnippetLanguage extends ValueObject<CodeSnippetLanguageProperties> {
  static createFrom(value: string): Result<CodeSnippetLanguage, InvalidCodeSnippetLanguageError> {
    if (CodeSnippetLanguage.hasInvalid(value)) {
      return Result.fail(new InvalidCodeSnippetLanguageError());
    }

    return Result.ok(new CodeSnippetLanguage({ value }));
  }

  static hasInvalid(value: string): boolean {
    return !codeSnippetLanguageSchema.safeParse(value).success;
  }
}
