import { Result, ValueObject } from '@evyweb/simple-ddd-toolkit';
import { z } from 'zod';
import { InvalidCodeSnippetContentError } from '../errors/InvalidCodeSnippetContentError';

const codeSnippetContentSchema = z.string().trim().min(1).max(3000000);

type CodeSnippetContentProperties = {
  value: string;
};

export class CodeSnippetContent extends ValueObject<CodeSnippetContentProperties> {
  static createFrom(content: string): Result<CodeSnippetContent, InvalidCodeSnippetContentError> {
    if (CodeSnippetContent.hasInvalid(content)) {
      return Result.fail(new InvalidCodeSnippetContentError());
    }

    return Result.ok(new CodeSnippetContent({ value: content }));
  }

  static hasInvalid(value: string): boolean {
    return !codeSnippetContentSchema.safeParse(value).success;
  }
}
