import { InvalidCodeSnippetFilenameError } from '@/src/server/domain/CodeSnippet/errors/InvalidCodeSnippetFilenameError';
import { Result, ValueObject } from '@evyweb/simple-ddd-toolkit';
import { z } from 'zod';

const codeSnippetFilenameSchema = z.string().trim().min(1).max(120);

type CodeSnippetFilenameProperties = {
  value: string;
};

export class CodeSnippetFilename extends ValueObject<CodeSnippetFilenameProperties> {
  static createFrom(filename: string): Result<CodeSnippetFilename, InvalidCodeSnippetFilenameError> {
    if (CodeSnippetFilename.hasInvalid(filename)) {
      return Result.fail(new InvalidCodeSnippetFilenameError());
    }

    return Result.ok(new CodeSnippetFilename({ value: filename }));
  }

  static hasInvalid(value: string): boolean {
    return !codeSnippetFilenameSchema.safeParse(value).success;
  }
}
