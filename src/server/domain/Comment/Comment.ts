import type { CommentId } from '@/src/server/domain/Comment/valueObjects/CommentId';
import { Aggregate, Result } from '@evyweb/simple-ddd-toolkit';

export type CommentProperties = {
  id: CommentId;
  feedbackId: string;
  parentId?: string;
  content: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  status: string;
};

export class Comment extends Aggregate<CommentProperties> {
  private constructor(properties: CommentProperties) {
    super(properties);
  }

  static create(properties: CommentProperties): Result<Comment, Error> {
    // TODO: Validation here
    return Result.ok(new Comment(properties));
  }

  static fromRaw(rawData: CommentProperties): Comment {
    return new Comment(rawData);
  }

  delete(): void {
    this.set('status', 'deleted');
  }

  isNotWrittenBy(userId: string): boolean {
    return this.get('userId') !== userId;
  }
}
