import { InvalidTitleError } from '@/src/server/domain/CodeReview/errors/InvalidTitleError';
import { Result, ValueObject } from '@evyweb/simple-ddd-toolkit';
import { z } from 'zod';

const schema = z.string().trim().min(1).max(150);

type CodeReviewTitleProperties = {
  value: string;
};

export class CodeReviewTitle extends ValueObject<CodeReviewTitleProperties> {
  static createFrom(title: string): Result<CodeReviewTitle, InvalidTitleError> {
    if (CodeReviewTitle.hasInvalid(title)) {
      return Result.fail(new InvalidTitleError());
    }

    return Result.ok(new CodeReviewTitle({ value: title }));
  }

  static hasInvalid(value: string): boolean {
    return !schema.safeParse(value).success;
  }
}
