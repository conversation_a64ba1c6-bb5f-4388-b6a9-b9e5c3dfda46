import type { TeamInvitationId } from '@/src/server/domain/TeamInvitation/valueObjects/TeamInvitationId';
import { Aggregate, Result } from '@evyweb/simple-ddd-toolkit';

export type TeamInvitationStatus = 'pending' | 'accepted' | 'declined' | 'cancelled';

export type TeamInvitationProperties = {
  id: TeamInvitationId;
  teamId: string;
  email: string;
  status: TeamInvitationStatus;
  invitedBy: string;
};

export class TeamInvitation extends Aggregate<TeamInvitationProperties> {
  private constructor(properties: TeamInvitationProperties) {
    super(properties);
  }

  static create(properties: TeamInvitationProperties): Result<TeamInvitation, Error> {
    return Result.ok(new TeamInvitation(properties));
  }

  static createPendingInvitation(properties: {
    id: TeamInvitationId;
    teamId: string;
    email: string;
    invitedBy: string;
  }): Result<TeamInvitation, Error> {
    return Result.ok(
      new TeamInvitation({
        ...properties,
        status: 'pending',
      })
    );
  }

  static fromRaw(rawData: TeamInvitationProperties): TeamInvitation {
    return new TeamInvitation(rawData);
  }

  isPending(): boolean {
    return this.get('status') === 'pending';
  }

  isNotPending(): boolean {
    return !this.isPending();
  }

  isDeclined(): boolean {
    return this.get('status') === 'declined';
  }

  isFor(email: string): boolean {
    return this.get('email') === email;
  }

  isNotFor(email: string): boolean {
    return !this.isFor(email);
  }

  accept(): void {
    this.set('status', 'accepted');
  }

  decline(): void {
    this.set('status', 'declined');
  }
}
