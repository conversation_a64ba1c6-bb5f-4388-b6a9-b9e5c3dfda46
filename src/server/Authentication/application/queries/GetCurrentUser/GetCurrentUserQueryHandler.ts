import {GenericQueryCtx} from "convex/server";
import {DataModel} from "@/convex/_generated/dataModel";
import {
  GetCurrentUserQuery
} from "@/src/server/Authentication/application/queries/GetCurrentUser/GetCurrentUserQuery";

export class GetCurrentUserQueryHandler {
  constructor(private readonly ctx: GenericQueryCtx<DataModel>) {}

  async handle(_query: GetCurrentUserQuery) {
    const identity = await this.ctx.auth.getUserIdentity();
    if (!identity) return null;

    const convexId = identity.subject.split('|')[0];

    const user = await this.ctx.db
      .query('users')
      .filter((q) => q.eq(q.field('_id'), convexId))
      .first();

    if (!user) {
      return null;
    }

    return {
      userId: user._id,
      email: user.email,
    };
  }
}
