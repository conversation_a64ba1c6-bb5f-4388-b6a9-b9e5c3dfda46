import input from "@inquirer/input";
import {ScrapGitRepoUseCase} from "@/application/usecases/ScrapGitRepoUseCase";
import {GitAdapter} from "@/infrastructure/GitAdapter";
import {FileSystemAdapter} from "@/infrastructure/FileSystemAdapter";
import {InMemoryGitRepoRepository} from "@/infrastructure/InMemoryGitRepoRepository";
import {ScrapingProcessPresenter} from "@/presentation/ScrapingProcessPresenter";
import {ConsoleLogger} from "@/infrastructure/ConsoleLogger";
import path from "path";

export async function main(
  stdin: NodeJS.ReadableStream,
  customInput: typeof input,
  logger = new ConsoleLogger()
) {
  const repositoryUrl = await customInput(
    {message: "Enter the URL of the repository to clone:"},
    {input: stdin}
  );

  const destination = await customInput(
    {message: "Enter the destination directory:", default: "./output"},
    {input: stdin}
  );

  const splitSize = await customInput(
    {message: "Enter the maximum size for file chunks (in MB):", default: "24"},
    {input: stdin}
  );

  const gitAdapter = new GitAdapter();
  const fileSystemAdapter = new FileSystemAdapter(+splitSize * 1024 * 1024);
  const gitRepoRepository = new InMemoryGitRepoRepository();
  const presenter = new ScrapingProcessPresenter(logger);

  const useCase = new ScrapGitRepoUseCase(
    gitAdapter,
    fileSystemAdapter,
    presenter,
    gitRepoRepository
  );

  await useCase.execute({
    repositoryUrl,
    destination: path.resolve(destination),
    folders: []
  });
}
