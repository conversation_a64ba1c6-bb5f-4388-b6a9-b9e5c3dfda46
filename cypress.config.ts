import { defineConfig } from 'cypress'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'

export default defineConfig({
  e2e: {
    specPattern: 'src/client/**/specs/e2e/**/*.cy.ts*',
    supportFile: 'cypress/support/e2e.ts',
  },
  component: {
    specPattern: 'src/client/**/specs/components/**/*.cy.ts*',
    supportFile: 'cypress/support/component.ts',
    devServer: {
      framework: 'react',
      bundler: 'vite',
      viteConfig: {
        plugins: [react(), tsconfigPaths()],
      },
    },
  },
})
