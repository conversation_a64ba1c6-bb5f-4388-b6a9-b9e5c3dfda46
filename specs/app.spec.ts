import {main} from "@/app";
import {afterEach, beforeEach, describe, expect, it, vi} from "vitest";
import fs from "fs-extra";
import path from "path";
import {execSync} from "child_process";
import {randomUUID} from "crypto";
import {FakeLogger} from "./fakes/FakeLogger";

describe("App Integration Tests", () => {
    let testRepoPath: string;
    let outputDirPath: string;

    beforeEach(() => {
        const uniqueId = randomUUID();
        outputDirPath = path.resolve(`./tmp/output-${uniqueId}`);
        testRepoPath = path.resolve(`./tmp/test-repo-${uniqueId}`);

        if (fs.existsSync(testRepoPath)) fs.removeSync(testRepoPath);
        if (fs.existsSync(outputDirPath)) fs.removeSync(outputDirPath);

        execSync("git config --global user.email '<EMAIL>'");
        execSync("git config --global user.name 'Test User'");

        fs.mkdirpSync(testRepoPath);
        fs.writeFileSync(path.join(testRepoPath, "README.md"), "# Test Repo");
        fs.mkdirpSync(path.join(testRepoPath, "node_modules"));
        fs.writeFileSync(path.join(testRepoPath, "node_modules/ignored.txt"), "should not appear");
        fs.writeFileSync(path.join(testRepoPath, "image.png"), "fake binary");
        fs.mkdirpSync(path.join(testRepoPath, "src"));
        fs.writeFileSync(path.join(testRepoPath, "src/index.ts"), "console.log('Hello, world!');");

        fs.writeFileSync(path.join(testRepoPath, "large-file.txt"), "A".repeat(2 * 1024 * 1024)); // 2 Mo

        execSync("git init", {cwd: testRepoPath});
        execSync("git add .", {cwd: testRepoPath});
        execSync('git commit -m "Initial commit with large file"', {cwd: testRepoPath});
    });

    afterEach(() => {
        fs.removeSync(outputDirPath);
        fs.removeSync(testRepoPath);
    });

    it("should exclude specified files and folders when generating the repository tree", async () => {
        // Arrange
        const mockInput = vi.fn()
            .mockResolvedValueOnce(testRepoPath)
            .mockResolvedValueOnce(outputDirPath)
            .mockResolvedValueOnce("24");

        // Act
        await main(process.stdin, mockInput, new FakeLogger());

        // Assert
        const treeFilePath = path.join(outputDirPath, "clean-architecture-node.txt");
        expect(fs.existsSync(treeFilePath)).toBe(true);

        const treeContent = fs.readFileSync(treeFilePath, "utf8");
        expect(treeContent).toContain("==== File: README.md\n# Test Repo\n");
        expect(treeContent).toContain("==== File: src/index.ts\nconsole.log('Hello, world!');\n");
        expect(treeContent).not.toContain("==== File: node_modules/ignored.txt");
        expect(treeContent).not.toContain("==== File: image.png");
    });

    it("should split large repository content into multiple files", async () => {
        // Arrange
        const mockInput = vi.fn()
            .mockResolvedValueOnce(testRepoPath)
            .mockResolvedValueOnce(outputDirPath)
            .mockResolvedValueOnce(1);

        const logger = new FakeLogger();

        // Act
        await main(process.stdin, mockInput, logger);

        // Assert
        const file1 = path.join(outputDirPath, "clean-architecture-node.txt");
        const file2 = path.join(outputDirPath, "repository-tree_2.txt");

        expect(fs.existsSync(file1)).toBe(true);
        const file1Size = fs.statSync(file1).size;
        expect(file1Size).toBe(1024 * 1024);

        expect(fs.existsSync(file2)).toBe(true);
        const file2Size = fs.statSync(file2).size;
        expect(file2Size).toBe(1024 * 1024);
    });
});
