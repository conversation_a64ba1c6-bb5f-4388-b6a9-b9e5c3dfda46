import { ScrapGitRepoUseCase } from "@/application/usecases/ScrapGitRepoUseCase";
import { ScrapGitRepoRequest } from "@/application/usecases/ScrapGitRepoRequest";
import { FakeGitService } from "../../fakes/FakeGitService";
import { InMemoryFileSystemService } from "../../fakes/InMemoryFileSystemService";
import { InMemoryGitRepoRepository } from "@/infrastructure/InMemoryGitRepoRepository";
import { ScrapingProcessPresenter } from "@/presentation/ScrapingProcessPresenter";
import { FakeLogger } from "../../fakes/FakeLogger";

describe("ScrapGitRepoUseCase", () => {
    let gitService: FakeGitService;
    let fileSystemService: InMemoryFileSystemService;
    let gitRepoRepository: InMemoryGitRepoRepository;
    let logger: FakeLogger;
    let presenter: ScrapingProcessPresenter;
    let useCase: ScrapGitRepoUseCase;
    let request: ScrapGitRepoRequest;

    beforeEach(() => {
        gitService = new FakeGitService();
        fileSystemService = new InMemoryFileSystemService();
        gitRepoRepository = new InMemoryGitRepoRepository();
        logger = new FakeLogger();
        presenter = new ScrapingProcessPresenter(logger);
        useCase = new ScrapGitRepoUseCase(gitService, fileSystemService, presenter, gitRepoRepository);

        request = {
            repositoryUrl: "https://example.com/repo.git",
            destination: "./cloned-repo",
            folders: ["src", "lib"]
        };
    });

    it("should log the start and finish of cloning", async () => {
        // Act
        await useCase.execute(request);

        // Assert
        expect(logger.messages).toContain("Cloning project...");
        expect(logger.messages).toContain("Cloning completed.");
    });

    it("should create a GitRepo and save it in the repository", async () => {
        // Act
        await useCase.execute(request);

        // Assert
        const savedRepo = gitRepoRepository.findByUrl(request.repositoryUrl);
        expect(savedRepo).toBeDefined();
        expect(savedRepo?.url).toBe(request.repositoryUrl);
        expect(savedRepo?.path).toBe(request.destination);
    });

    it("should split and save repository tree content into multiple files", async () => {
        // Arrange
        const largeContent = "A".repeat(25 * 1024 * 1024); // 25 Mo
        gitService.setRepositoryTree([
            { name: "large-file.txt", content: largeContent }
        ]);

        // Act
        await useCase.execute(request);

        // Assert
        const file1 = fileSystemService.getFileContent("./cloned-repo/clean-architecture-node.txt");
        const file2 = fileSystemService.getFileContent("./cloned-repo/repository-tree_2.txt");

        expect(file1).toBeDefined();
        expect(file2).toBeDefined();
        expect(file1!.length).toBe(24 * 1024 * 1024);
        expect(file2!.length).toBeLessThan(2 * 1024 * 1024);
    });

    it("should present the saved tree path", async () => {
        // Act
        await useCase.execute(request);

        // Assert
        expect(presenter.treePath).toContain("repository-tree_*.txt");
    });

    it("should handle small content without splitting", async () => {
        // Arrange
        gitService.setRepositoryTree([
            { name: "small-file.txt", content: "Small content" }
        ]);

        // Act
        await useCase.execute(request);

        // Assert
        const file1 = fileSystemService.getFileContent("./cloned-repo/clean-architecture-node.txt");

        expect(file1).toBeDefined();
        expect(file1).toContain("Small content");
    });
});
