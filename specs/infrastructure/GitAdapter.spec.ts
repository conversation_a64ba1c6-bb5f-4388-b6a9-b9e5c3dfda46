import { GitAdapter } from "@/infrastructure/GitAdapter";
import fs from "fs-extra";
import { execSync } from "child_process";
import path from "path";
import { randomUUID } from "crypto";

describe("GitAdapter", () => {
    let testRepoPath: string;
    let clonedRepoPath: string;
    let gitAdapter: GitAdapter;

    beforeEach(() => {
        const uniqueId = randomUUID();
        testRepoPath = path.resolve(`./tmp/test-repo-${uniqueId}`);
        clonedRepoPath = path.resolve(`./tmp/cloned-repo-${uniqueId}`);

        gitAdapter = new GitAdapter();

        fs.removeSync(testRepoPath);
        fs.removeSync(clonedRepoPath);

        execSync("git config --global user.email '<EMAIL>'");
        execSync("git config --global user.name 'Test User'");

        fs.mkdirpSync(testRepoPath);
        fs.writeFileSync(path.join(testRepoPath, "README.md"), "# Test Repo");
        fs.mkdirpSync(path.join(testRepoPath, "src"));
        fs.writeFileSync(path.join(testRepoPath, "src/index.ts"), "console.log('Hello, world!');");

        fs.mkdirpSync(path.join(testRepoPath, "node_modules"));
        fs.writeFileSync(path.join(testRepoPath, "node_modules/ignored.txt"), "should not appear");
        fs.writeFileSync(path.join(testRepoPath, "image.png"), "fake binary");

        execSync("git init", { cwd: testRepoPath });
        execSync("git add .", { cwd: testRepoPath });
        execSync('git commit -m "Initial commit"', { cwd: testRepoPath });
    });

    afterEach(() => {
        fs.removeSync(testRepoPath);
        fs.removeSync(clonedRepoPath);
    });

    it("should clone the repository to the specified destination", async () => {
        // Act
        await gitAdapter.cloneRepository(testRepoPath, clonedRepoPath);

        // Assert
        expect(fs.existsSync(path.join(clonedRepoPath, ".git"))).toBe(true);
        expect(fs.existsSync(path.join(clonedRepoPath, "README.md"))).toBe(true);
        expect(fs.existsSync(path.join(clonedRepoPath, "src/index.ts"))).toBe(true);
    });

    it("should exclude specified patterns from the repository tree", async () => {
        // Arrange
        await gitAdapter.cloneRepository(testRepoPath, clonedRepoPath);

        // Act
        const tree = await gitAdapter.getRepositoryTree(clonedRepoPath);

        // Assert
        expect(tree).not.toEqual(expect.arrayContaining([
            { name: "node_modules/ignored.txt", content: "should not appear" },
            { name: "image.png", content: "fake binary" },
        ]));

        expect(tree).toEqual(expect.arrayContaining([
            { name: "README.md", content: "# Test Repo" },
            { name: "src/index.ts", content: "console.log('Hello, world!');" },
        ]));
    });
});
