import { FileSystemAdapter } from "@/infrastructure/FileSystemAdapter";
import fs from "fs-extra";
import path from "path";
import { randomUUID } from "crypto";

describe("FileSystemAdapter", () => {
    let fileSystem: FileSystemAdapter;
    let testFilePath: string;
    let testDirPath: string;

    beforeEach(() => {
        fileSystem = new FileSystemAdapter();
        const uniqueId = randomUUID();
        testDirPath = path.resolve(`./tmp/dir-${uniqueId}`);
        testFilePath = path.resolve(`./tmp/file-${uniqueId}.txt`);

        fs.removeSync(testDirPath);
        fs.mkdirpSync(testDirPath);
    });

    afterEach(() => {
        fs.removeSync(testDirPath);
    });

    it("should write a file with the specified content", async () => {
        // Arrange
        const content = "Hello, World!";

        // Act
        await fileSystem.writeFile(testFilePath, content);

        // Assert
        expect(fs.readFileSync(testFilePath, "utf8")).toBe(content);
    });

    it("should read a file and return its content", async () => {
        // Arrange
        const content = "Test Content";
        fs.writeFileSync(testFilePath, content);

        // Act
        const result = await fileSystem.readFile(testFilePath);

        // Assert
        expect(result).toBe(content);
    });

    it("should split content into chunks of 24MB by default", () => {
        // Arrange
        const largeContent = "A".repeat(25 * 1024 * 1024);

        // Act
        const chunks = fileSystem.splitContent(largeContent);

        // Assert
        expect(chunks.length).toBe(2);
        expect(chunks[0].length).toBe(24 * 1024 * 1024);
        expect(chunks[1].length).toBe(1 * 1024 * 1024);
    });

    it("should split content dynamically with a custom size", () => {
        // Arrange
        const customContent = "B".repeat(2 * 1024 * 1024);
        const customSplitSize = 1 * 1024 * 1024;

        // Act
        const chunks = fileSystem.splitContent(customContent, customSplitSize);

        // Assert
        expect(chunks.length).toBe(2);
        expect(chunks[0].length).toBe(1 * 1024 * 1024);
        expect(chunks[1].length).toBe(1 * 1024 * 1024);
    });

    it("should not split content if it is smaller than the max size", () => {
        // Arrange
        const smallContent = "C".repeat(2 * 1024 * 1024);

        // Act
        const chunks = fileSystem.splitContent(smallContent);

        // Assert
        expect(chunks.length).toBe(1);
        expect(chunks[0].length).toBe(2 * 1024 * 1024);
    });
});
