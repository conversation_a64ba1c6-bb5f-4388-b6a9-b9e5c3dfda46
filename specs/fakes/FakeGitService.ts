import { IGitService } from "@/application/ports/IGitService";

interface FakeFile {
    name: string;
    content: string;
}

export class FakeGitService implements IGitService {
    public clonedRepositories: string[] = [];
    private repositoryTree: FakeFile[] = [];

    setRepositoryTree(tree: FakeFile[]): void {
        this.repositoryTree = tree;
    }

    async cloneRepository(_url: string, destination: string): Promise<void> {
        this.clonedRepositories.push(destination);
    }

    async getRepositoryTree(_path: string): Promise<FakeFile[]> {
        return this.repositoryTree;
    }
}
