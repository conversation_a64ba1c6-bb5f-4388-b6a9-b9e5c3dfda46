name: CI
on:
  push:
    branches:
      - '**'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.2.1
      - uses: actions/setup-node@v4.0.4
        with:
          node-version: 22.x
          cache: "npm"

      - name: Configure Git User
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Test User"

      - run: npm ci
      - run: npm run lint
      - run: npm test
      - run: npm run build
