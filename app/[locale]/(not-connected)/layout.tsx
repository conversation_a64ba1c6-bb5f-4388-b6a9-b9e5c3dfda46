import { FeedbackProvider } from '@/src/client/presentation/components/app/Feedbacks/FeedbackEditor/useFeedbackContext';
import { Flex } from '@radix-ui/themes';
import type { FC, PropsWithChildren } from 'react';

const NotConnectedLayout: FC<PropsWithChildren> = ({ children }) => (
  <Flex direction="column" style={{ height: '100svh', minHeight: '100svh' }}>
    <FeedbackProvider>
      <Flex direction="column" align="center" justify="center" flexGrow="1" className="min-h-0">
        {children}
      </Flex>
    </FeedbackProvider>
  </Flex>
);

export default NotConnectedLayout;
