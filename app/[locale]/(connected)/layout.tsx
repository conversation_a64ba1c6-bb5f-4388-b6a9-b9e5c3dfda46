import { CodeProvider } from '@/src/client/presentation/components/app/Code/CodeBlock/CodeContext';
import { FeedbackProvider } from '@/src/client/presentation/components/app/Feedbacks/FeedbackEditor/useFeedbackContext';
import { SidebarProvider } from '@/src/client/presentation/components/ui/Sidebar/sidebar';
import type { FC, ReactNode } from 'react';

type Props = Readonly<{
  children: ReactNode;
}>;

const ConnectedLayout: FC<Props> = async ({ children }) => (
  <SidebarProvider>
    <FeedbackProvider>
      <CodeProvider>{children}</CodeProvider>
    </FeedbackProvider>
  </SidebarProvider>
);

export default ConnectedLayout;
