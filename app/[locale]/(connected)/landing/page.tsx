'use client';

import { Sparkles } from '@/src/client/presentation/components/ui/Sparkles/Sparkles';
import {
  ChatBubbleIcon,
  CodeIcon,
  GroupIcon,
  LightningBoltIcon,
  LockClosedIcon,
  PersonIcon,
  ReaderIcon,
  RocketIcon,
  Share1Icon,
  StarIcon,
  TargetIcon,
} from '@radix-ui/react-icons';
import { Box, Button, Card, Container, Flex, Grid, Heading, Section, Text } from '@radix-ui/themes';
import Image from 'next/image';
import type { FC } from 'react';

const HomePage: FC = () => (
  <Flex direction="column" width="100%">
    <Box
      p="4"
      style={{
        borderBottom: '1px solid var(--gray-a5)',
        backgroundColor: '#00000080',
        position: 'fixed',
        top: 0,
        width: '100%',
        zIndex: 1000,
      }}
    >
      <Container size="4">
        <Flex justify="between" align="center">
          <Flex align="center" gap="2">
            <Image src="/logo-small.png" alt="CRAFT Logo Small" width="40" height="40" />
            <Heading size="5" weight="bold">
              CRAFT
            </Heading>
          </Flex>
          <Flex gap="4" align="center">
            <Button variant="ghost" highContrast>
              Log In
            </Button>
            <Button>Get Started</Button>
          </Flex>
        </Flex>
      </Container>
    </Box>

    <Section
      size={{ initial: '2', md: '3' }}
      style={{
        backgroundImage:
          'linear-gradient(rgba(150, 0, 150, 0.2), rgba(150, 0, 50 , 0.2)), url(/backgrounds/background.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        height: '100svh',
      }}
      py="0"
    >
      <Container size="3" style={{ height: '100svh' }} className="relative">
        <Flex
          direction="column"
          align="center"
          justify="center"
          gap="4"
          style={{
            minHeight: '100svh',
          }}
        >
          <Image src="/logo.png" alt="CRAFT Logo" width="400" height="400" className="rounded-lg" />
          <Heading as="h1" size={{ initial: '8', md: '9' }} align="center" weight="bold">
            Code Reviews & Forgotten Tales
          </Heading>
          <Text size={{ initial: '4', md: '6' }} align="center" color="gray">
            The AI era left code quality in ruins. Join CRAFT, rediscover the &#34;Forgotten Tales&#34; of development
            through collaborative code reviews. Forge your legend, and battle for dominance in a world rebuilding its
            digital soul.
          </Text>
          <Flex gap="4" mt="5">
            <Button color="indigo" size={{ initial: '3', md: '4' }}>
              <RocketIcon width="16" height="16" /> Join the Resistance
            </Button>
            <Button size={{ initial: '3', md: '4' }} variant="soft" color="gray" highContrast>
              <ReaderIcon width="16" height="16" /> Learn the Lore
            </Button>
          </Flex>
        </Flex>
        <Sparkles
          minSize={1}
          maxSize={2}
          particleDensity={10}
          className="top-0 left-0 bottom-0 right-0 absolute z-0 pointer-events-none !opacity-80"
          particleColor="#FFFFFF"
          speed={0.1}
        />
      </Container>
    </Section>

    <Section size={{ initial: '2', md: '3' }}>
      <Container size="4">
        <Flex direction="column" align="center" gap="3" pb="9">
          <Heading as="h2" size="7" align="center" weight="medium" mb="7">
            Change the way you review code
          </Heading>
          <Text size="4" align="center" color="gray">
            CRAFT is more than just a code review platform.
          </Text>
          <Text size="4" align="center" color="gray" mb="7">
            It&#39;s a digital realm where you can rediscover the forgotten tales of development. Share your code,
            collaborate with peers, and battle to restore the digital frontier to its former glory.
          </Text>
          <Grid columns={{ initial: '1', sm: '2' }} gap="6" width="100%">
            <Box className="relative overflow-hidden rounded-lg shadow-md cursor-pointer transition duration-300 ease-in-out hover:scale-105 hover:shadow-xl hover:z-10">
              <Box style={{ position: 'relative', width: '100%', paddingTop: '56.25%' }}>
                <Box style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}>
                  <Image
                    src="/screenshots/1new.png"
                    alt="Code Review Interface"
                    fill
                    className="object-cover block"
                    sizes="(max-width: 640px) 100vw, 50vw"
                  />
                </Box>
              </Box>
            </Box>

            <Box className="relative overflow-hidden rounded-lg shadow-md cursor-pointer transition duration-300 ease-in-out hover:scale-105 hover:shadow-xl hover:z-10">
              <Box style={{ position: 'relative', width: '100%', paddingTop: '56.25%' }}>
                <Box style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}>
                  <Image
                    src="/screenshots/3new.png"
                    alt="Code Review Interface"
                    fill
                    className="object-cover block"
                    sizes="(max-width: 640px) 100vw, 50vw"
                  />
                </Box>
              </Box>
            </Box>

            <Box className="relative overflow-hidden rounded-lg shadow-md cursor-pointer transition duration-300 ease-in-out hover:scale-105 hover:shadow-xl hover:z-10">
              <Box style={{ position: 'relative', width: '100%', paddingTop: '56.25%' }}>
                <Box style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}>
                  <Image
                    src="/screenshots/5.png"
                    alt="Code Review Interface"
                    fill
                    className="object-cover block"
                    sizes="(max-width: 640px) 100vw, 50vw"
                  />
                </Box>
              </Box>
            </Box>

            <Box className="relative overflow-hidden rounded-lg shadow-md cursor-pointer transition duration-300 ease-in-out hover:scale-105 hover:shadow-xl hover:z-10">
              <Box style={{ position: 'relative', width: '100%', paddingTop: '56.25%' }}>
                <Box style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}>
                  <Image
                    src="/screenshots/2new.png"
                    alt="Code Review Interface"
                    fill
                    className="object-cover block"
                    sizes="(max-width: 640px) 100vw, 50vw"
                  />
                </Box>
              </Box>
            </Box>

            <Box className="relative overflow-hidden rounded-lg shadow-md cursor-pointer transition duration-300 ease-in-out hover:scale-105 hover:shadow-xl hover:z-10">
              <Box style={{ position: 'relative', width: '100%', paddingTop: '56.25%' }}>
                <Box style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}>
                  <Image
                    src="/screenshots/4new.png"
                    alt="Code Review Interface"
                    fill
                    className="object-cover block"
                    sizes="(max-width: 640px) 100vw, 50vw"
                  />
                </Box>
              </Box>
            </Box>
          </Grid>
        </Flex>
      </Container>
    </Section>

    <Section size={{ initial: '2', md: '3' }} style={{ backgroundColor: '#11111140' }}>
      <Container size="4">
        <Flex direction="column" align="center" gap="3" pb="9">
          <Heading as="h2" size="7" align="center" weight="medium" mb="7">
            Forge Your Path: Code, Collaborate, Conquer
          </Heading>
          <Grid columns={{ initial: '1', sm: '2', md: '3' }} gap="6" width="100%">
            <Card variant="surface">
              <Flex direction="column" p="5" gap="3">
                <PersonIcon width="24" height="24" className="text-[var(--violet-11)]" />
                <Heading as="h3" size="4">
                  Create Your Crafter
                </Heading>
                <Text size="2" color="gray">
                  Design your unique developer avatar. Customize their look and skills as you progress.
                </Text>
              </Flex>
            </Card>
            <Card variant="surface">
              <Flex direction="column" p="5" gap="3">
                <StarIcon width="24" height="24" className="text-[var(--violet-11)]" />
                <Heading as="h3" size="4">
                  Gain XP & Level Up
                </Heading>
                <Text size="2" color="gray">
                  Earn experience through meaningful code reviews and challenges. Unlock abilities and prestige.
                </Text>
              </Flex>
            </Card>
            <Card variant="surface">
              <Flex direction="column" p="5" gap="3">
                <GroupIcon width="24" height="24" className="text-[var(--violet-11)]" />
                <Heading as="h3" size="4">
                  Form Clans & Alliances
                </Heading>
                <Text size="2" color="gray">
                  Team up with friends and colleagues. Create private clan rooms for focused reviews or plot world
                  domination.
                </Text>
              </Flex>
            </Card>
            <Card variant="surface">
              <Flex direction="column" p="5" gap="3">
                <TargetIcon width="24" height="24" className="text-[var(--violet-11)]" />
                <Heading as="h3" size="4">
                  Explore & Battle
                </Heading>
                <Text size="2" color="gray">
                  Venture beyond reviews. Battle AI remnants or challenge other players to test your mettle and earn
                  rewards.
                </Text>
              </Flex>
            </Card>

            <Card variant="surface">
              <Flex direction="column" p="5" gap="3">
                <Share1Icon width="24" height="24" className="text-[var(--violet-11)]" />
                <Heading as="h3" size="4">
                  Share Forgotten Tales (Public Reviews)
                </Heading>
                <Text size="2" color="gray">
                  Share code snippets publicly. Help rebuild collective knowledge and learn from diverse Crafters.
                </Text>
              </Flex>
            </Card>
            <Card variant="surface">
              <Flex direction="column" p="5" gap="3">
                <LockClosedIcon width="24" height="24" className="text-[var(--violet-11)]" />
                <Heading as="h3" size="4">
                  Secure Clan Archives (Private Rooms)
                </Heading>
                <Text size="2" color="gray">
                  Create invite-only spaces for confidential team reviews, strategy sessions, and focused skill-honing.
                </Text>
              </Flex>
            </Card>
            <Card variant="surface">
              <Flex direction="column" p="5" gap="3">
                <ChatBubbleIcon width="24" height="24" className="text-[var(--violet-11)]" />
                <Heading as="h3" size="4">
                  Masterful Feedback
                </Heading>
                <Text size="2" color="gray">
                  Utilize structured comments (Critical, Constructive, Query) for clear, actionable review insights.
                </Text>
              </Flex>
            </Card>
            <Card variant="surface">
              <Flex direction="column" p="5" gap="3">
                <CodeIcon width="24" height="24" className="text-[var(--violet-11)]" />
                <Heading as="h3" size="4">
                  Pinpoint Precision (Inline Context)
                </Heading>
                <Text size="2" color="gray">
                  Comment directly on specific code lines within the integrated viewer for maximum clarity.
                </Text>
              </Flex>
            </Card>
            <Card variant="surface">
              <Flex direction="column" p="5" gap="3">
                <LightningBoltIcon width="24" height="24" className="text-[var(--violet-11)]" />
                <Heading as="h3" size="4">
                  Chronicle Your Journey (Activity Log)
                </Heading>
                <Text size="2" color="gray">
                  Track your review contributions, battle history, XP gains, and overall progress in your personal log.
                </Text>
              </Flex>
            </Card>
          </Grid>
        </Flex>
      </Container>
    </Section>

    <Section size={{ initial: '2', md: '3' }}>
      <Container size="3">
        <Flex direction="column" align="center" gap="4" py="6">
          <Heading as="h2" size="7" align="center" weight="medium">
            Balance the Craft and the Combat
          </Heading>
          <Text size="4" align="center" color="gray">
            CRAFT isn&#39;t just about code reviews; it&#39;s an adventure. Hone your skills, collaborate with allies,
            gain experience, and engage in thrilling challenges. Find your perfect balance between improving code and
            exploring the world.
          </Text>
          <Button size="4" mt="4" variant="surface" highContrast>
            Start Your Quest
          </Button>
        </Flex>
      </Container>
    </Section>

    <Section size={{ initial: '2', md: '3' }} style={{ backgroundColor: '#11111140' }}>
      <Container size="3">
        <Flex direction="column" align="center" gap="4" py="6">
          <Image src="/logo-small.png" alt="CRAFT Logo Small" width="300" height="300" />
          <Heading as="h2" size={{ initial: '6', md: '7' }} align="center">
            Answer the Call, Crafter!
          </Heading>
          <Text size="4" align="center" color="violet">
            The digital frontier needs rebuilding. Sign up free and forge your legend in code and combat today.
          </Text>
          <Button size={{ initial: '3', md: '4' }} mt="4">
            Start your journey
          </Button>
        </Flex>
      </Container>
    </Section>

    <Box pt="6" pb="6" style={{ backgroundColor: '#000000' }}>
      <Container size="4">
        <Flex justify="between" align="center" direction={{ initial: 'column', sm: 'row' }} gap="4">
          <Text size="2" color="gray">
            © {new Date().getFullYear()} CRAFT - Reclaiming Code&#39;s Soul.
          </Text>
          <Flex gap="4">
            <Button variant="ghost" size="2" color="gray">
              Privacy Policy
            </Button>
            <Button variant="ghost" size="2" color="gray">
              Terms of Service
            </Button>
          </Flex>
        </Flex>
      </Container>
    </Box>
  </Flex>
);

export default HomePage;
