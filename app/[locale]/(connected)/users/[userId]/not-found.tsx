import LobbyPage from '@/src/client/presentation/components/app/Layout/LobbyPage/LobbyPage';
import { Flex, Heading, Text } from '@radix-ui/themes';
import { UserX } from 'lucide-react';
import React from 'react';

export default function NotFound() {
  return (
    <LobbyPage>
      <LobbyPage.Content>
        <Flex direction="column" align="center" justify="center" gap="4" py="9">
          <UserX size={80} className="text-gray-500" />
          <Heading size="8">User Not Found</Heading>
          <Text size="4">The user you are looking for does not exist or has been deleted.</Text>
        </Flex>
      </LobbyPage.Content>
    </LobbyPage>
  );
}
