import LobbySidebar from '@/src/client/presentation/components/app/Layout/LeftSidebar/LobbySidebar';
import AppScrollArea from '@/src/client/presentation/components/ui/ScrollAreas/AppScrollArea';
import { Flex } from '@radix-ui/themes';
import React, { type FC, type PropsWithChildren } from 'react';

const LobbyLayout: FC<PropsWithChildren> = async ({ children }) => (
  <>
    <LobbySidebar />

    <Flex width="100%" flexGrow="1" className="min-h-0 overflow-hidden h-svh w-full pt-1 pb-3">
      <AppScrollArea>{children}</AppScrollArea>
    </Flex>
  </>
);

export default LobbyLayout;
