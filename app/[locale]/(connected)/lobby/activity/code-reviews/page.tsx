'use client';

import CodeReviewCard from '@/src/client/presentation/components/app/CodeReview/CodeReviewCard/CodeReviewCard';
import SkeletonCodeReviewCard from '@/src/client/presentation/components/app/CodeReview/CodeReviewCard/SkeletonCodeReviewCard';
import LobbyPage from '@/src/client/presentation/components/app/Layout/LobbyPage/LobbyPage';
import CardGrid from '@/src/client/presentation/components/ui/CardGrid/CardGrid';
import DateBlock from '@/src/client/presentation/components/ui/DateBlock/DateBlock';
import SkeletonHelper from '@/src/client/presentation/components/ui/SkeletonHelper/SkeletonHelper';
import { useLoadMyCodeReviewParticipationHistoryFeature } from '@/src/client/presentation/hooks/features/queries/useLoadMyCodeReviewParticipationHistoryFeature';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { Box, Flex, Skeleton, Text } from '@radix-ui/themes';
import React, { type FC } from 'react';

type Props = {
  params: Promise<{
    locale: string;
  }>;
};

const MyCodeReviewHistoryPage: FC<Props> = () => {
  const locale = useLocale();
  const { viewModel } = useLoadMyCodeReviewParticipationHistoryFeature();

  return (
    <LobbyPage>
      <LobbyPage.Header>
        <LobbyPage.Title>My Code Review History</LobbyPage.Title>
        <LobbyPage.Subtitle>
          <Flex align="center" gap="2">
            <Text size="3" className="italic">
              Recent participations.
            </Text>
          </Flex>
        </LobbyPage.Subtitle>
      </LobbyPage.Header>

      <LobbyPage.Content>
        {viewModel.isLoading && (
          <Box>
            <Skeleton>
              <Box mb="3">
                <Text size="5">Loading...</Text>
              </Box>
            </Skeleton>
            <CardGrid>
              <SkeletonHelper amount={12} component={SkeletonCodeReviewCard} />
            </CardGrid>
          </Box>
        )}

        {viewModel.participationHistory.map((historyDate) => (
          <Box key={historyDate.date}>
            <Box mb="3">
              <DateBlock dateToDisplay={historyDate.date} />
            </Box>

            <CardGrid>
              {historyDate.history.map((codeReview) => (
                <CodeReviewCard key={codeReview.id} codeReview={codeReview} locale={locale} />
              ))}
            </CardGrid>
          </Box>
        ))}

        {viewModel.hasNoParticipation && <Text size="2">No code review found...</Text>}
      </LobbyPage.Content>
    </LobbyPage>
  );
};

export default MyCodeReviewHistoryPage;
