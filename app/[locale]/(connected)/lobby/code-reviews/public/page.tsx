'use client';

import CodeReviewCard from '@/src/client/presentation/components/app/CodeReview/CodeReviewCard/CodeReviewCard';
import SkeletonCodeReviewCard from '@/src/client/presentation/components/app/CodeReview/CodeReviewCard/SkeletonCodeReviewCard';
import CreateCodeReviewButton from '@/src/client/presentation/components/app/CodeReview/CreateCodeReviewButton/CreateCodeReviewButton';
import LoadMoreCodeReviewsButton from '@/src/client/presentation/components/app/CodeReview/LoadMoreCodeReviewsButton/LoadMoreCodeReviewsButton';
import LobbyPage from '@/src/client/presentation/components/app/Layout/LobbyPage/LobbyPage';
import CardGrid from '@/src/client/presentation/components/ui/CardGrid/CardGrid';
import SkeletonHelper from '@/src/client/presentation/components/ui/SkeletonHelper/SkeletonHelper';
import { usePublicCodeReviewsFeature } from '@/src/client/presentation/hooks/features/queries/usePublicCodeReviewsFeature';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { Flex, Text } from '@radix-ui/themes';
import React, { type FC } from 'react';

const TOTAL_CARDS = 18;

const PublicCodeReviewsPage: FC = () => {
  const locale = useLocale();

  const {
    publicCodeReviews,
    noPublicCodeReviewsFound,
    shouldDisplayLoadMoreButton,
    shouldDisableLoadMoreButton,
    isLoadingFirstPage,
    loadMorePublicCodeReviews,
    isLoading,
  } = usePublicCodeReviewsFeature(TOTAL_CARDS);

  return (
    <LobbyPage>
      <LobbyPage.Header action={<CreateCodeReviewButton size={{ initial: '3', md: '2' }} />}>
        <LobbyPage.Title>Public Code reviews</LobbyPage.Title>
        <LobbyPage.Subtitle>
          <Flex align="center" gap="2">
            <Text size="3" className="italic">
              Find a code review to join!
            </Text>
          </Flex>
        </LobbyPage.Subtitle>
      </LobbyPage.Header>

      <LobbyPage.Content>
        {noPublicCodeReviewsFound && <Text size="2">No public code review found...</Text>}

        <CardGrid>
          {publicCodeReviews.map((codeReview) => (
            <CodeReviewCard key={codeReview.id} codeReview={codeReview} locale={locale} />
          ))}

          {isLoadingFirstPage && <SkeletonHelper amount={TOTAL_CARDS} component={SkeletonCodeReviewCard} />}
        </CardGrid>

        {shouldDisplayLoadMoreButton && (
          <Flex justify="center" mb="6">
            <LoadMoreCodeReviewsButton
              isLoading={isLoading}
              isDisabled={shouldDisableLoadMoreButton}
              onClick={() => loadMorePublicCodeReviews(TOTAL_CARDS)}
            />
          </Flex>
        )}
      </LobbyPage.Content>
    </LobbyPage>
  );
};

export default PublicCodeReviewsPage;
