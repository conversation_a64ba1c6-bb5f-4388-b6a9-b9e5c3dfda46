'use client';

import CodeReviewEditableCard from '@/src/client/presentation/components/app/CodeReview/CodeReviewEditableCard/CodeReviewEditableCard';
import SkeletonCodeReviewEditableCard from '@/src/client/presentation/components/app/CodeReview/CodeReviewEditableCard/SkeletonCodeReviewEditableCard';
import CreateCodeReviewButton from '@/src/client/presentation/components/app/CodeReview/CreateCodeReviewButton/CreateCodeReviewButton';
import LobbyPage from '@/src/client/presentation/components/app/Layout/LobbyPage/LobbyPage';
import CardGrid from '@/src/client/presentation/components/ui/CardGrid/CardGrid';
import SkeletonHelper from '@/src/client/presentation/components/ui/SkeletonHelper/SkeletonHelper';
import { useUserReadyCodeReviewsFeature } from '@/src/client/presentation/hooks/features/queries/useUserReadyCodeReviewsFeature';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { Flex, Text } from '@radix-ui/themes';
import React, { type FC } from 'react';

const RecentCodeReviewsPage: FC = () => {
  const locale = useLocale();

  const { codeReviews, isLoading, noCodeReviewsFound, codeReviewsExist, totalCodeReviews } =
    useUserReadyCodeReviewsFeature();

  return (
    <LobbyPage>
      <LobbyPage.Header color="border-blue-500" action={<CreateCodeReviewButton size={{ initial: '3', md: '2' }} />}>
        <LobbyPage.Title>
          Ready to review{' '}
          <Text as="span" size="3">
            ({totalCodeReviews})
          </Text>
        </LobbyPage.Title>
        <LobbyPage.Subtitle>
          <Flex align="center" gap="2">
            <Text size="3" className="italic">
              Click on the start button when you are ready to get some feedback.
            </Text>
          </Flex>
        </LobbyPage.Subtitle>
      </LobbyPage.Header>

      <LobbyPage.Content>
        {isLoading && (
          <CardGrid>
            <SkeletonHelper amount={12} component={SkeletonCodeReviewEditableCard} />
          </CardGrid>
        )}

        {codeReviewsExist && (
          <CardGrid>
            {codeReviews.map((codeReview) => (
              <CodeReviewEditableCard key={codeReview.id} codeReview={codeReview} locale={locale} />
            ))}
          </CardGrid>
        )}

        {noCodeReviewsFound && <Text size="2">No recently created code review found...</Text>}
      </LobbyPage.Content>
    </LobbyPage>
  );
};

export default RecentCodeReviewsPage;
