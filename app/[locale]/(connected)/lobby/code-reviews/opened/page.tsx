'use client';

import CodeReviewEditableCard from '@/src/client/presentation/components/app/CodeReview/CodeReviewEditableCard/CodeReviewEditableCard';
import SkeletonCodeReviewEditableCard from '@/src/client/presentation/components/app/CodeReview/CodeReviewEditableCard/SkeletonCodeReviewEditableCard';
import CreateCodeReviewButton from '@/src/client/presentation/components/app/CodeReview/CreateCodeReviewButton/CreateCodeReviewButton';
import LobbyPage from '@/src/client/presentation/components/app/Layout/LobbyPage/LobbyPage';
import CardGrid from '@/src/client/presentation/components/ui/CardGrid/CardGrid';
import SkeletonHelper from '@/src/client/presentation/components/ui/SkeletonHelper/SkeletonHelper';
import { useUserOpenedCodeReviewsFeature } from '@/src/client/presentation/hooks/features/queries/useUserOpenedCodeReviewsFeature';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { Flex, Text } from '@radix-ui/themes';
import React, { type FC } from 'react';

const MyCodeReviewsPage: FC = () => {
  const locale = useLocale();

  const { codeReviews, isLoading, codeReviewsExist, totalCodeReviews, noCodeReviewsFound } =
    useUserOpenedCodeReviewsFeature();

  return (
    <LobbyPage>
      <LobbyPage.Header color="border-green-500" action={<CreateCodeReviewButton size={{ initial: '3', md: '2' }} />}>
        <LobbyPage.Title>
          Opened reviews{' '}
          <Text as="span" size="3">
            ({totalCodeReviews})
          </Text>
        </LobbyPage.Title>
        <LobbyPage.Subtitle>
          <Flex align="center" gap="2">
            <Text size="3" className="italic">
              Collected enough feedback? Just stop the code review!
            </Text>
          </Flex>
        </LobbyPage.Subtitle>
      </LobbyPage.Header>

      <LobbyPage.Content>
        {isLoading && (
          <CardGrid>
            <SkeletonHelper amount={12} component={SkeletonCodeReviewEditableCard} />
          </CardGrid>
        )}

        {codeReviewsExist && (
          <CardGrid>
            {codeReviews.map((codeReview) => (
              <CodeReviewEditableCard key={codeReview.id} codeReview={codeReview} locale={locale} />
            ))}
          </CardGrid>
        )}

        {noCodeReviewsFound && <Text size="2">No code review found...</Text>}
      </LobbyPage.Content>
    </LobbyPage>
  );
};

export default MyCodeReviewsPage;
