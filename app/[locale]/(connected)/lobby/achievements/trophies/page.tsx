import LobbyPage from '@/src/client/presentation/components/app/Layout/LobbyPage/LobbyPage';
import { Flex, Text } from '@radix-ui/themes';
import React, { type FC } from 'react';

const Page: FC = () => {
  return (
    <LobbyPage>
      <LobbyPage.Header>
        <LobbyPage.Title>My Trophies</LobbyPage.Title>
        <LobbyPage.Subtitle>
          <Flex align="center" gap="2">
            <Text size="3" className="italic">
              Achievements you have unlocked.
            </Text>
          </Flex>
        </LobbyPage.Subtitle>
      </LobbyPage.Header>

      <LobbyPage.Content>
        <Text>Not implemented yet...</Text>
      </LobbyPage.Content>
    </LobbyPage>
  );
};

export default Page;
