'use client';

import Loading from '@/app/[locale]/(connected)/code-reviews/[codeReviewId]/loading';
import LobbyPage from '@/src/client/presentation/components/app/Layout/LobbyPage/LobbyPage';
import AddMemberDialog from '@/src/client/presentation/components/app/Teams/AddMemberDialog/AddMemberDialog';
import DeleteTeamDialog from '@/src/client/presentation/components/app/Teams/DeleteTeamDialog/DeleteTeamDialog';
import LeaveTeamDialog from '@/src/client/presentation/components/app/Teams/LeaveTeamDialog/LeaveTeamDialog';
import TeamNotFound from '@/src/client/presentation/components/app/Teams/NotFound/NotFound';
import TeamRenameDialog from '@/src/client/presentation/components/app/Teams/TeamRenameDialog/TeamRenameDialog';
import ShiningButton from '@/src/client/presentation/components/ui/ShiningButton/ShiningButton';
import { useTeamDetailsFeature } from '@/src/client/presentation/hooks/features/queries/useTeamDetailsFeature';
import { useLocale } from '@/src/client/presentation/hooks/utils/useLocale/useLocale';
import { useTeamId } from '@/src/client/presentation/hooks/utils/useTeamId/useTeamId';
import { Avatar, Badge, Box, Button, Flex, Heading, Table, Text } from '@radix-ui/themes';
import Link from 'next/link';
import React, { type FC } from 'react';

const TeamDetailsPage: FC = () => {
  const MAX_TOTAL_AVATARS = 5;

  const locale = useLocale();
  const teamId = useTeamId();

  const handleRemoveMember = (userId: string) => {
    console.log(`Removing member with ID: ${userId} from team: ${teamId}`);
  };

  const handlePromoteToOwner = (userId: string) => {
    console.log(`Promoting member with ID: ${userId} to owner of team: ${teamId}`);
  };

  const { isNotFound, isLoading, isOwner, team } = useTeamDetailsFeature(teamId);

  if (isNotFound) {
    return <TeamNotFound locale={locale} />;
  }

  if (isLoading) {
    return <Loading />;
  }

  return (
    <LobbyPage>
      <LobbyPage.Header
        action={
          isOwner && (
            <Flex gap="2">
              <AddMemberDialog teamId={teamId}>
                <ShiningButton>Add Member</ShiningButton>
              </AddMemberDialog>
            </Flex>
          )
        }
      >
        <LobbyPage.Title>Team Details</LobbyPage.Title>
        <LobbyPage.Subtitle>
          <Flex align="center" gap="2">
            <Text size="3" className="italic">
              Team management and details
            </Text>
          </Flex>
        </LobbyPage.Subtitle>
      </LobbyPage.Header>

      <LobbyPage.Content>
        <Flex direction="column" gap="3">
          <Box className="p-6 rounded-md bg-[#11111140]">
            <Flex direction="column" gap="2" align="center">
              <Flex align="center" gap="2">
                <Heading>{team.teamName}</Heading>
              </Flex>

              <Flex direction="column" align="center" gap="2">
                <div className="flex -space-x-4 overflow-hidden p-4">
                  {team.members.slice(0, MAX_TOTAL_AVATARS).map((member, index) => (
                    <Avatar
                      key={member.userId}
                      size="4"
                      src={member.avatar}
                      fallback={member.avatarFallback}
                      radius="full"
                      className="inline-block"
                      style={{ zIndex: 10 + index }}
                    />
                  ))}

                  {team.members.length > MAX_TOTAL_AVATARS && (
                    <Avatar
                      variant="solid"
                      color="iris"
                      size="4"
                      radius="full"
                      fallback={`+${team.members.length - MAX_TOTAL_AVATARS}`}
                      className="inline-block"
                      style={{ zIndex: 10 * MAX_TOTAL_AVATARS }}
                    />
                  )}
                </div>
                <Text size="3" weight="medium">
                  {team.members.length} {team.members.length === 1 ? 'member' : 'members'}
                </Text>
              </Flex>
            </Flex>
          </Box>

          {isOwner && (
            <Flex justify="end" className="p-4">
              <TeamRenameDialog teamId={teamId} currentName={team.teamName}>
                <Button variant="soft">Rename this team</Button>
              </TeamRenameDialog>
            </Flex>
          )}

          <Flex direction="column" gap="3" px="3">
            <Table.Root>
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeaderCell>User</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell align="center">Role</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell align="right">Actions</Table.ColumnHeaderCell>
                </Table.Row>
              </Table.Header>

              <Table.Body>
                {team.members.map((member) => (
                  <Table.Row key={member.userId}>
                    <Table.RowHeaderCell>
                      <Flex align="center" gap="2">
                        <Avatar size="2" src={member.avatar} fallback={member.avatarFallback} radius="full" />
                        <Text weight="medium">{member.name}</Text>
                      </Flex>
                    </Table.RowHeaderCell>
                    <Table.Cell align="center" justify="center" py="4">
                      <Badge color={member.isOwner ? 'plum' : 'gray'}>{member.role}</Badge>
                    </Table.Cell>
                    <Table.Cell align="right">
                      <Flex gap="2" align="center" justify="end">
                        <Button variant="soft" color="indigo" asChild>
                          <Link href={`/${locale}/users/${member.userId}`}>View Profile</Link>
                        </Button>
                        {isOwner && !member.isOwner && (
                          <>
                            <Button variant="soft" color="indigo" onClick={() => handlePromoteToOwner(member.userId)}>
                              Promote to Owner
                            </Button>
                            <Button variant="soft" color="red" onClick={() => handleRemoveMember(member.userId)}>
                              Remove
                            </Button>
                          </>
                        )}
                      </Flex>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table.Root>

            {isOwner && (
              <Flex direction="column" gap="4" className="mt-6">
                <Box className="mt-8 rounded-lg">
                  <Flex direction="column" gap="4">
                    <Flex
                      justify="between"
                      align="center"
                      className="p-4 border border-red-800/50 rounded-md bg-red-950/30"
                    >
                      <Flex direction="column" gap="1">
                        <Heading size="5" color="red">
                          Danger Zone
                        </Heading>
                        <Text size="2" color="gray">
                          Permanently delete this team and remove all members.
                        </Text>
                      </Flex>
                      <DeleteTeamDialog teamId={teamId} teamName={team.teamName}>
                        <Button color="red">Delete Team</Button>
                      </DeleteTeamDialog>
                    </Flex>
                  </Flex>
                </Box>
              </Flex>
            )}

            {!isOwner && (
              <Flex direction="column" gap="4" className="mt-6">
                <Box className="mt-8 rounded-lg">
                  <Flex direction="column" gap="4">
                    <Flex
                      justify="between"
                      align="center"
                      className="p-4 border border-amber-800/50 rounded-md bg-amber-950/30"
                    >
                      <Flex direction="column" gap="1">
                        <Heading size="5" color="amber">
                          Danger Zone
                        </Heading>
                        <Text size="2" color="gray">
                          Permanently leave this team.
                        </Text>
                      </Flex>
                      <LeaveTeamDialog teamId={teamId} teamName={team.teamName}>
                        <Button color="amber">Leave Team</Button>
                      </LeaveTeamDialog>
                    </Flex>
                  </Flex>
                </Box>
              </Flex>
            )}
          </Flex>
        </Flex>
      </LobbyPage.Content>
    </LobbyPage>
  );
};

export default TeamDetailsPage;
