import LobbyPage from '@/src/client/presentation/components/app/Layout/LobbyPage/LobbyPage';
import CreateTeamButton from '@/src/client/presentation/components/app/Teams/CreateTeamButton/CreateTeamButton';
import TeamsList from '@/src/client/presentation/components/app/Teams/TeamsList/TeamsList';
import { Flex, Text } from '@radix-ui/themes';
import React, { type FC } from 'react';

const TeamsPage: FC = () => (
  <LobbyPage>
    <LobbyPage.Header action={<CreateTeamButton />}>
      <LobbyPage.Title>My Teams</LobbyPage.Title>
      <LobbyPage.Subtitle>
        <Flex align="center" gap="2">
          <Text size="3" className="italic">
            List of your teams and their members
          </Text>
        </Flex>
      </LobbyPage.Subtitle>
    </LobbyPage.Header>

    <LobbyPage.Content>
      <TeamsList />
    </LobbyPage.Content>
  </LobbyPage>
);

export default TeamsPage;
