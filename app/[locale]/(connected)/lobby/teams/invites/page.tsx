import LobbyPage from '@/src/client/presentation/components/app/Layout/LobbyPage/LobbyPage';
import TeamInvitationsList from '@/src/client/presentation/components/app/Teams/TeamInvitationsList/TeamInvitationsList';
import { Flex, Text } from '@radix-ui/themes';
import React, { type FC } from 'react';

const TeamsInvitesPage: FC = () => {
  return (
    <LobbyPage>
      <LobbyPage.Header>
        <LobbyPage.Title>Team Invitations</LobbyPage.Title>
        <LobbyPage.Subtitle>
          <Flex align="center" gap="2">
            <Text size="3" className="italic">
              Pending invitations to join teams
            </Text>
          </Flex>
        </LobbyPage.Subtitle>
      </LobbyPage.Header>

      <LobbyPage.Content>
        <TeamInvitationsList />
      </LobbyPage.Content>
    </LobbyPage>
  );
};

export default TeamsInvitesPage;
