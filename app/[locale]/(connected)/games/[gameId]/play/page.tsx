import {FC} from "react";
import PlayGamePage from "@/src/client/pages/Catalog/PlayGamePage/PlayGamePage";

type Props = {
  params: Promise<{ locale: string, gameId: string; }>;
};

const PlayGamePageContainer: FC<Props> = async ({params}: Props) => {
  const {gameId, locale} = await params;

  return (
    <PlayGamePage gameId={gameId} locale={locale}/>
  );
};

export default PlayGamePageContainer;