import {FC} from "react";
import {loadCatalogCardsByGameId} from "@/src/server/application/queries/loadCatalogByGameId";
import {Flex} from "@radix-ui/themes";
import {loadDeckBuilderSettingsByGameId} from "@/src/server/application/queries/loadDeckBuilderSettingsByGameId";
import DeckBuildingFilters from "@/src/client/components/app/DeckBuilding/DeckBuildingFilters/DeckBuildingFilters";
import DeckBuilderCardsGrid from "@/src/client/components/app/DeckBuilding/DeckBuilderCardsGrid/DeckBuilderCardsGrid";
import DeckBuilderPanel from "@/src/client/components/app/DeckBuilding/DeckBuilderPanel/DeckBuilderPanel";

type Props = {
  params: Promise<{ gameId: string; locale: string }>;
};

const DeckBuilderPageContainer: FC<Props> = async ({params}) => {
  const {gameId, locale} = await params;
  const {catalog} = await loadCatalogCardsByGameId({gameId});
  // const {gameSettings} = await loadGameSettingsByGameId({gameId});
  const {deckBuilderSettings} = await loadDeckBuilderSettingsByGameId({gameId});

  return (
    <Flex direction="row" gap="1" p="3" className="h-full">
      <Flex direction="column" className="min-w-[240px]">
        <DeckBuildingFilters/>
      </Flex>

      <Flex direction="column" gap="3" className="h-full">
        <DeckBuilderCardsGrid cards={catalog.cards} locale={locale}/>
      </Flex>

      <Flex direction="column" className="h-full min-w-[400px]">
        <DeckBuilderPanel/>
      </Flex>
    </Flex>
  );
};

export default DeckBuilderPageContainer;