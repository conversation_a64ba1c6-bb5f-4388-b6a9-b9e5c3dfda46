import { api } from '@/convex/_generated/api';
import CodeReviewEditor from '@/src/client/presentation/components/app/Code/CodeReviewEditor/CodeReviewEditor';
import BackToCodeReviewButton from '@/src/client/presentation/components/app/CodeReview/BackToCodeReviewButton/BackToCodeReviewButton';
import BackToPublicCodeReviewsButton from '@/src/client/presentation/components/app/CodeReview/BackToPublicCodeReviewsButton/BackToPublicCodeReviewsButton';
import CopyReportButton from '@/src/client/presentation/components/app/CodeReview/CopyReportButton/CopyReportButton';
import PresentationModeButton from '@/src/client/presentation/components/app/CodeReview/PresentationModeButon/PresentationModeButton';
import ShowFeedbacksButton from '@/src/client/presentation/components/app/CodeReview/ShowFeedbacksButton/ShowFeedbacksButton';
import DetailedFeedbackCodeContainer from '@/src/client/presentation/components/app/Feedbacks/FeedbackCodeContainer/DetailedFeedbackCodeContainer';
import { FeedbackCreationDialogProvider } from '@/src/client/presentation/components/app/Feedbacks/FeedbackCreationDialog/FeedbackCreationDialogContext';
import CodeReviewPage from '@/src/client/presentation/components/app/Layout/CodeReviewPage/CodeReviewPage';
import CodeReviewLeftSidebar from '@/src/client/presentation/components/app/Layout/LeftSidebar/CodeReviewLeftSidebar';
import CodeReviewRightSidebar from '@/src/client/presentation/components/app/Layout/RightSidebar/CodeReviewRightSidebar';
import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { Box, Flex, Text } from '@radix-ui/themes';
import { fetchQuery } from 'convex/nextjs';
import { RedirectType, redirect } from 'next/navigation';
import React, { type FC } from 'react';

// TODO: remove the url
const url =
  'https://api.dicebear.com/9.x/identicon/svg?rowColor=85E89D,9ECBFF,B392F0,F97583,FFEA7F,F99B15,C9D1D9,0D1117,161B22&seed=';
type Props = {
  params: Promise<{ codeReviewId: string; locale: string }>;
  searchParams: Promise<{ snippetId: string; feedbackId: string }>;
};

const CodeReviewIdPage: FC<Props> = async ({ params, searchParams }: Props) => {
  const { codeReviewId, locale } = await params;
  const { snippetId, feedbackId } = await searchParams;

  if (!snippetId) {
    const { firstCodeSnippetId } = await fetchQuery(
      api.queries.firstSnippetIdByCodeReviewId.endpoint,
      { codeReviewId },
      { token: await convexAuthNextjsToken() }
    );
    redirect(`/${locale}/code-reviews/${codeReviewId}?snippetId=${firstCodeSnippetId}`, RedirectType.replace);
  }

  const viewModel = await fetchQuery(
    api.queries.codeReviewEditor.endpoint,
    { codeReviewId },
    { token: await convexAuthNextjsToken() }
  );
  if (!viewModel) {
    return null;
  }

  return (
    <>
      <CodeReviewLeftSidebar codeReviewId={codeReviewId} />

      <Flex width="100%" flexGrow="1" className="min-h-0 overflow-hidden h-svh w-full pt-1">
        <CodeReviewPage>
          <CodeReviewPage.Header color="border-indigo-500" image={`${url}${codeReviewId}`}>
            <CodeReviewPage.Title>{viewModel.title}</CodeReviewPage.Title>
            <CodeReviewPage.Subtitle>
              <Flex align="center" gap="2">
                <Text size="3" className="italic">
                  Proposed by{' '}
                  <Text as="span" color="plum" weight="bold">
                    {viewModel.author.name}
                  </Text>
                </Text>
              </Flex>
            </CodeReviewPage.Subtitle>
          </CodeReviewPage.Header>

          <CodeReviewPage.Content>
            <Flex align="center" justify="between" width="100%">
              {!feedbackId && <BackToPublicCodeReviewsButton locale={locale} />}

              {feedbackId && (
                <BackToCodeReviewButton locale={locale} codeReviewId={codeReviewId} snippetId={snippetId} />
              )}

              <Flex display={{ initial: 'none', md: 'flex' }} gap="2">
                <CopyReportButton codeReviewId={codeReviewId} />
                <PresentationModeButton />
              </Flex>

              <Box display={{ initial: 'block', md: 'none' }}>
                <ShowFeedbacksButton />
              </Box>
            </Flex>

            <FeedbackCreationDialogProvider>
              <Flex className="h-full min-h-0 overflow-hidden w-full" gap="3" pb="3">
                <Box width="100%" height="100%">
                  <Flex flexGrow="1" direction="column" className="w-full h-full overflow-hidden rounded-md">
                    {feedbackId && (
                      <DetailedFeedbackCodeContainer
                        codeReviewId={codeReviewId}
                        feedbackId={feedbackId}
                        snippetId={snippetId}
                        locale={locale}
                      />
                    )}
                    {!feedbackId && <CodeReviewEditor snippetId={snippetId} />}
                  </Flex>
                </Box>
              </Flex>
            </FeedbackCreationDialogProvider>
          </CodeReviewPage.Content>
        </CodeReviewPage>
      </Flex>

      <CodeReviewRightSidebar snippetId={snippetId} />
    </>
  );
};

export default CodeReviewIdPage;
