import { Box, Flex } from '@radix-ui/themes';
import Image from 'next/image';
import type { FC } from 'react';

const Loading: FC = () => {
  return (
    <Flex
      direction="column"
      flexGrow="1"
      align="center"
      justify="center"
      style={{ height: '100svh', minHeight: '100svh' }}
    >
      <Box>
        <Image src="/logo.png" alt="logo" width="300" height="300" />
      </Box>
      <Box>
        <p>Loading...</p>
      </Box>
    </Flex>
  );
};

export default Loading;
