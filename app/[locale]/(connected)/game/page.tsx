import LobbyPage from '@/src/client/presentation/components/app/Layout/LobbyPage/LobbyPage';
import GameMenuRightSidebar from '@/src/client/presentation/components/app/Layout/RightSidebar/GameMenuSidebar';
import CardGrid from '@/src/client/presentation/components/ui/CardGrid/CardGrid';
import { ShakeCard } from '@/src/client/presentation/components/ui/ShakeCard/ShakeCard';
import { Box, Button, Callout, Card, Flex, Progress, Text } from '@radix-ui/themes';
import { Sword } from 'lucide-react';
import Image from 'next/image';
import React, { type FC } from 'react';

const FANTASY_NAMES = [
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Sage',
  'Phoenix',
  'Echo',
  'Orion',
  'Aurora',
  'Caspian',
  '<PERSON>',
  '<PERSON>',
  'Fable',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>raith',
  'Xenon',
  'Yonder',
];

const getRandomName = () => {
  return FANTASY_NAMES[Math.floor(Math.random() * FANTASY_NAMES.length)];
};

const getRandomLevel = () => {
  return Math.floor(Math.random() * 10) + 1;
};

const Page: FC = () => {
  const players = Array.from({ length: 15 }, () => ({
    name: getRandomName(),
    level: getRandomLevel(),
    character: Math.random() > 0.5 ? 'boy' : 'girl',
  }));

  return (
    <Flex width="100%" flexGrow="1" className="min-h-0 overflow-hidden w-full pt-1">
      <LobbyPage>
        <LobbyPage.Header>
          <LobbyPage.Title>Exploration</LobbyPage.Title>
          <LobbyPage.Subtitle>
            <Flex align="center" gap="2">
              <Text size="3" className="italic">
                Syntaxia
              </Text>
            </Flex>
          </LobbyPage.Subtitle>
        </LobbyPage.Header>

        <LobbyPage.Content>
          <Flex direction="column" gap="3" justify="center">
            <Flex direction="column" gap="2">
              <Text size="3" weight="bold">
                Dropped items
              </Text>
              <CardGrid>
                <Card className="hover:cursor-grab">
                  <Flex direction="column" align="center" justify="center" p="3">
                    <Text size="2" weight="bold">
                      T-300 (dead)
                    </Text>
                    <Text size="1" color="green">
                      Pieces: 1/3
                    </Text>
                  </Flex>
                  <Image src="/monsters/mob2-dead.png" alt="Mob2" width={250} height={250} />
                </Card>
              </CardGrid>
            </Flex>

            <Flex direction="column" gap="2">
              <Text size="3" weight="bold">
                Monsters on the same cell
              </Text>

              <Flex direction="column" gap="2" mb="2">
                <Callout.Root color="red" role="alert" size="1">
                  <Callout.Icon>
                    <Sword />
                  </Callout.Icon>
                  <Callout.Text>T-300 attacked you - You lost 100 HP</Callout.Text>
                </Callout.Root>

                <Callout.Root color="red" role="alert" size="1">
                  <Callout.Icon>
                    <Sword />
                  </Callout.Icon>
                  <Callout.Text>T-100 attacked you - You lost 120 HP</Callout.Text>
                </Callout.Root>

                <Callout.Root color="red" role="alert" size="1">
                  <Callout.Icon>
                    <Sword />
                  </Callout.Icon>
                  <Callout.Text>T-300 attacked you - You lost 25 HP</Callout.Text>
                </Callout.Root>
              </Flex>

              <CardGrid>
                <ShakeCard>
                  <Card className="hover:cursor-pointer">
                    <Flex direction="column" align="center" justify="center" p="3">
                      <Text size="2" weight="bold">
                        T-100
                      </Text>
                      <Text size="1" color="green">
                        Level 1
                      </Text>
                    </Flex>
                    <Image src="/monsters/mob1.png" alt="Mob1" width={250} height={250} />
                    <Flex direction="column" gap="4" maxWidth="300px">
                      <Progress color="green" value={75} size="2" />
                    </Flex>

                    <Box mt="3" className="w-full">
                      <Button variant="soft" color="red" className="!w-full">
                        <Sword />
                        Attack
                      </Button>
                    </Box>
                  </Card>
                </ShakeCard>

                <ShakeCard>
                  <Card className="hover:cursor-pointer">
                    <Flex direction="column" align="center" justify="center" p="3">
                      <Text size="2" weight="bold">
                        T-300
                      </Text>
                      <Text size="1" color="red" weight="bold">
                        Level 5
                      </Text>
                    </Flex>
                    <Image src="/monsters/mob2.png" alt="Mob1" width={250} height={250} />
                    <Flex direction="column" gap="4" maxWidth="300px">
                      <Progress color="orange" value={45} size="2" />
                    </Flex>

                    <Box mt="3" className="w-full">
                      <Button variant="soft" color="red" className="!w-full">
                        <Sword />
                        Attack
                      </Button>
                    </Box>
                  </Card>
                </ShakeCard>
                <ShakeCard>
                  <Card className="hover:cursor-pointer">
                    <Flex direction="column" align="center" justify="center" p="3">
                      <Text size="2" weight="bold">
                        T-100
                      </Text>
                      <Text size="1" color="orange">
                        Level 2
                      </Text>
                    </Flex>
                    <Image src="/monsters/mob1.png" alt="Mob1" width={250} height={250} />
                    <Flex direction="column" gap="4" maxWidth="300px">
                      <Progress color="green" value={75} size="2" />
                    </Flex>

                    <Box mt="3" className=" w-full">
                      <Button variant="soft" color="red" className="!w-full">
                        <Sword />
                        Attack
                      </Button>
                    </Box>
                  </Card>
                </ShakeCard>
              </CardGrid>
            </Flex>

            <Flex direction="column" gap="2">
              <Text size="3" weight="bold" mt="3">
                Players on the same cell
              </Text>
              <CardGrid>
                {players.map((player, index) => (
                  <ShakeCard key={index}>
                    <Card className="hover:cursor-pointer">
                      <Flex direction="column" align="center" justify="center" p="3">
                        <Text size="2" weight="bold">
                          {player.name}
                        </Text>
                        <Text size="1">Level {player.level}</Text>
                      </Flex>
                      <Image src={`/characters/${player.character}.png`} alt={player.name} width={1024} height={1536} />

                      <Box mt="3" className="w-full">
                        <Button variant="soft" color="red" className="!w-full">
                          <Sword />
                          Attack
                        </Button>
                      </Box>
                    </Card>
                  </ShakeCard>
                ))}
              </CardGrid>
            </Flex>
          </Flex>
        </LobbyPage.Content>
      </LobbyPage>

      <GameMenuRightSidebar />
    </Flex>
  );
};

export default Page;
