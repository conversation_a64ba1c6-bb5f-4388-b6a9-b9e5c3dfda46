import CodeReviewLeftSidebar from '@/src/client/presentation/components/app/Layout/LeftSidebar/GameLeftSidebar';
import AppScrollArea from '@/src/client/presentation/components/ui/ScrollAreas/AppScrollArea';
import { Flex } from '@radix-ui/themes';
import React, { type FC, type PropsWithChildren } from 'react';

const GameLayout: FC<PropsWithChildren> = async ({ children }) => (
  <>
    <CodeReviewLeftSidebar />

    <Flex width="100%" flexGrow="1" className="min-h-0 overflow-hidden h-svh w-full pt-1 pb-3">
      <AppScrollArea>{children}</AppScrollArea>
    </Flex>
  </>
);

export default GameLayout;
