# https://cleaner-code.com

"Cleaner Code" is a code review platform that helps teams maintain high code quality standards through collaborative feedback.

## Features
- **Submit Code Snippet for Review**: Submit code snippets for peer review, allowing team members to provide feedback on specific pieces of code.
- **Open Code Review Session**: Start a new code review session to gather feedback from team members.
- **Organize Code Review**: Set up structured code review sessions with specific parameters and participants.
- **Post Comment Feedback**: Add general comments on code snippets to discuss implementation details, suggest improvements, or ask questions.
- **Post Question Feedback**: Create polls to gather team opinions on specific code-related questions, with multiple options for answers.
- **Post Refacto Feedback**: Suggest code refactoring by providing alternative implementations along with explanations.
- **Post Check Standard Feedback**: Link code sections to team documentation and standards, ensuring consistency with established practices.
- **Update Feedback Content**: Modify existing feedback to clarify points or add additional information.
- **Delete Feedback**: Remove feedback entries that are no longer relevant or needed.
- **Up Vote Feedback**: Support valuable feedback by upvoting it, helping highlight important suggestions.
- **Down Vote Feedback**: Indicate disagreement or concerns with specific feedback through downvoting.
