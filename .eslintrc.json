{"extends": ["next/core-web-vitals", "next/typescript"], "plugins": ["boundaries"], "overrides": [{"files": ["src/server/**"], "extends": ["plugin:clean-architecture/strict"]}], "settings": {"boundaries/elements": [{"type": "server-domain", "pattern": "src/server/domain/**"}, {"type": "server-application", "pattern": "src/server/application/**"}, {"type": "server-infrastructure", "pattern": "src/server/infrastructure/**"}, {"type": "server-presentation", "pattern": "src/server/presentation/**"}]}, "rules": {"boundaries/element-types": ["error", {"default": "allow", "rules": [{"from": "server-domain", "disallow": ["server-application", "server-infrastructure", "server-presentation"]}, {"from": "server-application", "disallow": ["server-infrastructure", "server-presentation"]}]}], "boundaries/external": ["error", {"default": "allow", "rules": [{"from": "server-domain", "disallow": ["*"]}]}]}}